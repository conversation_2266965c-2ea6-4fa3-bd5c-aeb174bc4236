# Firebase Security Rules Documentation

## Overview

This document describes the comprehensive security rules implemented for both Firestore Database and Firebase Storage to ensure proper access control based on subscription tiers and ownership permissions.

## Pricing Plans

| Plan | Monthly Price | Yearly Price | Trial Period |
|------|--------------|--------------|--------------|
| **Free** | $0 | $0 | No trial needed |
| **Basic** | $5/month | $50/year | 7-day free trial |
| **Pro** | $19.90/month | $190/year | 7-day free trial |
| **Enterprise** | $45/month | $450/year | 7-day free trial |

*Yearly plans include 2 months free (16.7% savings)*

## Key Security Principles

### 1. **Subscription-Based Feature Access**
- Free users can only access basic features
- Pro users get advanced features (loyalty, referrals, marketing, analytics)
- Enterprise users get all features including custom branding and dedicated support
- Staff portal is available from Basic tier and up

### 2. **Ownership-Based Access Control**
- Users can only access resources they own (spaces/salons)
- Public resources (for booking) are readable by everyone
- Customer and appointment data is restricted to space/salon owners

### 3. **Data Protection**
- Default deny-all policy with explicit allow rules
- Backward compatibility for salon/space transition
- Proper validation for file uploads (type, size limits)

## Firestore Security Rules

### Helper Functions

```javascript
// Authentication checks
function isAuthenticated()
function isOwner(userId)
function isSpaceOwner(spaceId)
function isSalonOwner(salonId)

// Subscription tier checks
function getUserSubscriptionTier()
function hasFeatureAccess(feature)
```

### Collection Access Patterns

#### **Public Collections** (Readable by all)
- `spaces/{spaceId}` - For public booking pages
- `salons/{salonId}` - Backward compatibility
- `staff/{staffId}` - For staff selection on booking
- `services/{serviceId}` - For service selection on booking
- `reviews/{reviewId}` - For public review display

#### **Owner-Restricted Collections**
- `users/{userId}` - Users can only access their own profile
- `customers/{customerId}` - Space/salon owners only
- `appointments/{appointmentId}` - Space/salon owners only (creation allowed for public booking)
- `invoices/{invoiceId}` - Space/salon owners only

#### **Feature-Gated Collections**
- `loyalty-transactions/{transactionId}` - Requires `advanced_loyalty_program`
- `loyalty-rewards/{rewardId}` - Requires `advanced_loyalty_program`
- `referral-codes/{codeId}` - Requires `advanced_referral_system`
- `marketing-campaigns/{campaignId}` - Requires `advanced_marketing`
- `analytics/{analyticsId}` - Requires `advanced_analytics`
- `custom-branding/{brandingId}` - Requires `enterprise_custom_branding`

### Feature Access Matrix

| Feature | Free | Basic | Pro | Enterprise |
|---------|------|-------|-----|------------|
| **Core Features** |  |  |  |  |
| Basic Appointment Scheduling | ✅ | ✅ | ✅ | ✅ |
| Customer Management | ✅ (400 max) | ✅ (1,200 max) | ✅ (unlimited) | ✅ (unlimited) |
| Email Notifications | ✅ | ✅ | ✅ | ✅ |
| Salon Spaces | ✅ (1 space) | ✅ (2 spaces) | ✅ (5 spaces) | ✅ (unlimited) |
| **Advanced Features** |  |  |  |  |
| Staff Portal | 🚫 | ✅ | ✅ | ✅ |
| SMS Notifications | 🚫 | 🚫 | ✅ | ✅ |
| Revenue Tracking | 🚫 | 🚫 | ✅ | ✅ |
| Analytics Dashboard | 🚫 | 🚫 | ✅ | ✅ |
| **Growth Features** |  |  |  |  |
| Customer Loyalty Program | 🚫 | 🚫 | ✅ | ✅ |
| Referral System | 🚫 | 🚫 | ✅ | ✅ |
| Marketing Tools | 🚫 | 🚫 | ✅ | ✅ |
| **Enterprise Features** |  |  |  |  |
| Custom Branding | 🚫 | 🚫 | 🚫 | ✅ |
| API Access | 🚫 | 🚫 | 🚫 | ✅ |
| White-label Solution | 🚫 | 🚫 | 🚫 | ✅ |
| **Support** |  |  |  |  |
| Community Support | ✅ | ✅ | 🚫 | 🚫 |
| Priority Support | 🚫 | 🚫 | ✅ | ✅ |
| Dedicated Support Manager | 🚫 | 🚫 | 🚫 | ✅ |

**Legend:**
- ✅ **Included** - Feature is fully available
- 🚫 **Not Available** - Feature is not included in this plan

**Resource Limits by Plan:**
- **Free**: 1 space, 400 customers, 30 appointments/month
- **Basic**: 2 spaces, 1,200 customers, unlimited appointments
- **Pro**: 5 spaces, unlimited customers, unlimited appointments
- **Enterprise**: Unlimited spaces, customers, and appointments

## Firebase Storage Rules

### Access Patterns

#### **Public Read Collections**
- `users/{userId}/profile/` - Profile images
- `spaces/{spaceId}/` - Space images for booking pages
- `salons/{salonId}/` - Salon images (backward compatibility)
- `services/{spaceId}/{serviceId}/` - Service images
- `staff/{spaceId}/{staffId}/` - Staff photos
- `branding/{spaceId}/` - Custom branding assets (Enterprise only)

#### **Owner-Only Collections**
- `invoices/{spaceId}/{invoiceId}/` - Invoice documents
- `customers/{spaceId}/{customerId}/` - Customer files
- `backups/{spaceId}/` - Backup files
- `temp/{userId}/` - Temporary uploads

#### **Feature-Gated Collections**
- `marketing/{spaceId}/{campaignId}/` - Marketing assets (Pro+)
- `loyalty/{spaceId}/` - Loyalty program assets (Pro+)
- `analytics/{spaceId}/` - Analytics exports (Pro+)
- `branding/{spaceId}/` - Custom branding (Enterprise only)

### File Size Limits

| File Type | Size Limit | Purpose |
|-----------|------------|---------|
| Profile Images | 5MB | User profile photos |
| Space/Salon Images | 10MB | Business photos |
| Service Images | 5MB | Service photos |
| Staff Photos | 5MB | Staff profile photos |
| Branding Assets | 20MB | Custom logos, banners |
| Marketing Assets | 15MB | Campaign images |
| Loyalty Assets | 5MB | Loyalty program images |
| Invoice Documents | 10MB | PDF invoices |
| Customer Files | 5MB | Customer documents |
| Analytics Exports | 50MB | Data export files |
| Backup Files | 100MB | Database backups |
| Temporary Files | 20MB | Processing uploads |

## Implementation Guidelines

### 1. **Deploying Rules**

Deploy Firestore rules:
```bash
firebase deploy --only firestore:rules
```

Deploy Storage rules:
```bash
firebase deploy --only storage
```

Deploy both:
```bash
firebase deploy --only firestore:rules,storage
```

### 2. **Testing Rules**

Use Firebase Rules Playground to test your rules:
```bash
firebase serve --only firestore
```

Test specific scenarios:
- Free user trying to access loyalty features (should be denied)
- Pro user accessing their own marketing campaigns (should be allowed)
- Anonymous user viewing public booking page (should be allowed)

### 3. **Error Handling**

When rules deny access, your app should:
- Show appropriate upgrade prompts for feature-gated resources
- Display clear error messages for unauthorized access
- Redirect to login for unauthenticated requests

### 4. **Backup Strategy**

The current rules allow space owners to manage their own backups. Consider implementing:
- Automated daily backups via Cloud Functions
- Retention policies for backup files
- Secure backup verification

## Security Best Practices

### 1. **Principle of Least Privilege**
- Users only get access to what they need
- Default deny with explicit allow rules
- Regular security rule audits

### 2. **Input Validation**
- File type validation for uploads
- Size limits to prevent abuse
- Content type verification

### 3. **Monitoring and Logging**
- Monitor denied requests for potential attacks
- Log unauthorized access attempts
- Set up alerts for suspicious activity

### 4. **Regular Reviews**
- Review rules when adding new features
- Update rules when changing subscription tiers
- Test rules with different user types

## Common Issues and Solutions

### Issue: "Permission Denied" Errors

**Cause**: User doesn't have required subscription tier or ownership access

**Solution**: 
1. Check user's subscription tier in database
2. Verify user owns the resource they're trying to access
3. Ensure proper error handling in your app

### Issue: Public Booking Page Not Working

**Cause**: Overly restrictive rules blocking public access

**Solution**:
1. Ensure spaces, services, and staff are publicly readable
2. Allow appointment creation for public users
3. Test booking flow with unauthenticated users

### Issue: File Upload Failures

**Cause**: File size or type restrictions

**Solution**:
1. Check file size against limits
2. Verify file type matches allowed types
3. Implement client-side validation

## Migration Notes

If you're upgrading from the previous open rules:

1. **Backup your data** before deploying new rules
2. **Test thoroughly** with different user types
3. **Update your app** to handle rule-based errors
4. **Monitor logs** for any access issues after deployment

The new rules are backward compatible with your salon/space transition, supporting both collection structures during the migration period.

## Next Steps

1. Deploy the rules to your Firebase project
2. Test with different subscription tiers
3. Update your frontend error handling
4. Monitor for any access issues
5. Consider implementing automated rule testing in your CI/CD pipeline
