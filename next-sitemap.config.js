/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://groombook.me',
  generateRobotsTxt: true,
  changefreq: 'daily',
  priority: 0.7,
  exclude: [
    '/dashboard',
    '/dashboard/*',
    '/login',
    '/register',
    '/forgot-password',
    '/api/*',
    '/staff/*',
  ],
  robotsTxtOptions: {
    additionalSitemaps: [],
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/dashboard',
          '/login',
          '/register',
          '/forgot-password',
          '/api',
          '/staff',
        ],
      },
    ],
  },
  transform: async (config, path) => {
    // Custom transformation for dynamic routes
    // Return null if you don't want the path in your sitemap
    // For example, we might want to customize priority for certain pages

    // Give higher priority to main pages
    if (path === '/') {
      return {
        loc: path,
        changefreq: 'daily',
        priority: 1.0,
        lastmod: new Date().toISOString(),
      };
    }

    // Give higher priority to important service/booking pages
    if (path === '/book' || path.startsWith('/services') || path === '/about' || path === '/contact') {
      return {
        loc: path,
        changefreq: 'weekly',
        priority: 0.9,
        lastmod: new Date().toISOString(),
      };
    }

    // Use default transformation for other pages
    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: new Date().toISOString(),
    };
  },
};
