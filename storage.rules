rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function getUserData() {
      return firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data;
    }
    
    function getUserSubscriptionTier() {
      return getUserData().subscriptionTier;
    }
    
    function hasFeatureAccess(feature) {
      let tier = getUserSubscriptionTier();
      
      // Basic features (available on all tiers including free)
      let basicFeatures = ['basic_appointments', 'basic_customer_management', 'basic_email_notifications'];
      
      // Pro features (require Pro or Enterprise)
      let proFeatures = ['advanced_loyalty_program', 'advanced_referral_system', 'advanced_sms_notifications', 
                        'advanced_marketing', 'advanced_analytics'];
      
      // Staff portal is available from Basic tier up
      let basicPlusFeatures = ['advanced_staff_portal'];
      
      // Enterprise features (require Enterprise tier only)
      let enterpriseFeatures = ['enterprise_custom_branding', 'enterprise_api_access', 'enterprise_dedicated_support'];
      
      return (feature in basicFeatures) ||
             (feature in basicPlusFeatures && tier in ['basic', 'pro', 'enterprise']) ||
             (feature in proFeatures && tier in ['pro', 'enterprise']) ||
             (feature in enterpriseFeatures && tier == 'enterprise');
    }
    
    function isSpaceOwner(spaceId) {
      return isAuthenticated() && 
             firestore.get(/databases/(default)/documents/spaces/$(spaceId)).data.ownerId == request.auth.uid;
    }
    
    function isSalonOwner(salonId) {
      return isAuthenticated() && 
             firestore.get(/databases/(default)/documents/salons/$(salonId)).data.ownerId == request.auth.uid;
    }
    
    function isValidImageType() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidFileSize(maxSizeMB) {
      return request.resource.size <= maxSizeMB * 1024 * 1024;
    }

    // Default rule - deny all access
    match /{allPaths=**} {
      allow read, write: if false;
    }
    
    // User profile images - users can manage their own profile pictures
    match /users/{userId}/profile/{fileName} {
      allow read: if true; // Public read for profile images
      allow write: if isOwner(userId) && 
                      isValidImageType() && 
                      isValidFileSize(5); // 5MB limit for profile images
    }
    
    // Space/Salon images - owners can manage their space images
    match /spaces/{spaceId}/{fileName} {
      allow read: if true; // Public read for space images (booking page)
      allow write: if isSpaceOwner(spaceId) && 
                      isValidImageType() && 
                      isValidFileSize(10); // 10MB limit for space images
    }
    
    // Salon images (backward compatibility)
    match /salons/{salonId}/{fileName} {
      allow read: if true; // Public read for salon images (booking page)
      allow write: if isSalonOwner(salonId) && 
                      isValidImageType() && 
                      isValidFileSize(10); // 10MB limit for salon images
    }
    
    // Service images - space/salon owners can manage service images
    match /services/{spaceId}/{serviceId}/{fileName} {
      allow read: if true; // Public read for service images (booking page)
      allow write: if isSpaceOwner(spaceId) && 
                      isValidImageType() && 
                      isValidFileSize(5); // 5MB limit for service images
    }
    
    // Staff images - space/salon owners can manage staff photos
    match /staff/{spaceId}/{staffId}/{fileName} {
      allow read: if true; // Public read for staff photos (booking page)
      allow write: if isSpaceOwner(spaceId) && 
                      isValidImageType() && 
                      isValidFileSize(5); // 5MB limit for staff photos
    }
    
    // Custom branding assets - require enterprise tier
    match /branding/{spaceId}/{fileName} {
      allow read: if true; // Public read for branding assets (client-facing pages)
      allow write: if isSpaceOwner(spaceId) && 
                      hasFeatureAccess('enterprise_custom_branding') &&
                      isValidImageType() && 
                      isValidFileSize(20); // 20MB limit for branding assets
    }
    
    // Marketing campaign assets - require marketing access
    match /marketing/{spaceId}/{campaignId}/{fileName} {
      allow read: if isSpaceOwner(spaceId) && hasFeatureAccess('advanced_marketing');
      allow write: if isSpaceOwner(spaceId) && 
                      hasFeatureAccess('advanced_marketing') &&
                      isValidImageType() && 
                      isValidFileSize(15); // 15MB limit for marketing assets
    }
    
    // Loyalty program assets - require loyalty program access
    match /loyalty/{spaceId}/{fileName} {
      allow read: if isSpaceOwner(spaceId) && hasFeatureAccess('advanced_loyalty_program');
      allow write: if isSpaceOwner(spaceId) && 
                      hasFeatureAccess('advanced_loyalty_program') &&
                      isValidImageType() && 
                      isValidFileSize(5); // 5MB limit for loyalty assets
    }
    
    // Invoice documents - space/salon owners can manage invoices
    match /invoices/{spaceId}/{invoiceId}/{fileName} {
      allow read: if isSpaceOwner(spaceId);
      allow write: if isSpaceOwner(spaceId) && 
                      isValidFileSize(10); // 10MB limit for invoice documents
    }
    
    // Customer documents/photos - space/salon owners can manage
    match /customers/{spaceId}/{customerId}/{fileName} {
      allow read: if isSpaceOwner(spaceId);
      allow write: if isSpaceOwner(spaceId) && 
                      isValidFileSize(5); // 5MB limit for customer files
    }
    
    // Analytics exports - require analytics access
    match /analytics/{spaceId}/{fileName} {
      allow read: if isSpaceOwner(spaceId) && hasFeatureAccess('advanced_analytics');
      allow write: if isSpaceOwner(spaceId) && 
                      hasFeatureAccess('advanced_analytics') &&
                      isValidFileSize(50); // 50MB limit for analytics exports
    }
    
    // Backup files - space/salon owners can manage backups
    match /backups/{spaceId}/{fileName} {
      allow read: if isSpaceOwner(spaceId);
      allow write: if isSpaceOwner(spaceId) && 
                      isValidFileSize(100); // 100MB limit for backup files
    }
    
    // Temporary uploads - authenticated users can upload temporarily
    match /temp/{userId}/{fileName} {
      allow read, write: if isOwner(userId) && 
                            isValidFileSize(20); // 20MB limit for temp files
      // Note: You should implement a Cloud Function to clean up temp files after 24 hours
    }
  }
}
