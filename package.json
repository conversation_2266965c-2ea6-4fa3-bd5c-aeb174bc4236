{"name": "groombook", "version": "2.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "check-all": "npm run lint && npm run type-check", "prepare": "husky", "test-loyalty": "ts-node --project tsconfig.json -r tsconfig-paths/register src/scripts/test-loyalty-system.ts", "preview-emails": "ts-node --project tsconfig.json -r tsconfig-paths/register src/scripts/preview-emails.ts", "generate-sitemap": "next-sitemap"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@paddle/paddle-js": "^1.4.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@react-email/components": "^0.0.41", "@react-email/render": "^1.1.2", "@reduxjs/toolkit": "^2.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dotted-map": "^2.2.3", "firebase": "^11.7.1", "framer-motion": "^12.12.2", "lucide-react": "^0.509.0", "next": "14.2.28", "next-themes": "^0.4.6", "posthog-js": "^1.242.1", "posthog-node": "^4.17.1", "qrcode.react": "^4.2.0", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-email": "^4.0.15", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-qr-code": "^2.0.15", "react-redux": "^9.2.0", "recharts": "^2.15.3", "resend": "^4.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.28", "husky": "^9.1.7", "lint-staged": "^16.0.0", "next-sitemap": "^4.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5"}}