# Staff PIN Troubleshooting Guide

## Overview

The staff portal uses a PIN-based authentication system to allow staff members to access their dashboard. This guide helps troubleshoot common PIN-related issues.

## How the PIN System Works

1. **PIN Generation**: When a staff member is created, a 4-digit PIN is automatically generated
2. **PIN Storage**: PINs are stored in the `staff-pins` collection in Firestore
3. **First Login**: Staff members must change their PIN on first login
4. **PIN Verification**: The system checks the `staff-pins` collection for authentication

## Common Issues and Solutions

### Issue 1: "Invalid PIN" Error

**Symptoms**: Staff member enters their PIN but gets "Invalid PIN" error

**Possible Causes**:
- PIN not migrated to new system
- PIN document missing from `staff-pins` collection
- Staff member using old/incorrect PIN

**Solutions**:
1. **Run PIN Migration**:
   - Go to Dashboard → Staff PINs → Click "Migrate PINs" button
   - Or visit `/debug/staff-pins` page and click "Run Migration"

2. **Check PIN Status**:
   - Visit `/debug/staff-pins` to see PIN status for all staff
   - Look for staff members with "No New PIN" badge

3. **Reset PIN**:
   - Go to Dashboard → Staff PINs
   - Find the staff member and click "Reset PIN"
   - Give the new PIN to the staff member

### Issue 2: Staff Can't Access Portal

**Symptoms**: Staff member can't access the staff portal at all

**Possible Causes**:
- Wrong portal URL
- Staff member not assigned to correct space
- PIN system not set up

**Solutions**:
1. **Check Portal URL**: Should be `/staff/portal/[spaceId]`
2. **Verify Staff Assignment**: Ensure staff member is assigned to the correct space
3. **Run Migration**: Use the migration tools mentioned above

### Issue 3: "This PIN is not valid for this location"

**Symptoms**: PIN is accepted but staff member gets location error

**Possible Causes**:
- Staff member assigned to different space
- Space ID mismatch

**Solutions**:
1. **Check Staff Assignment**: Verify the staff member's `spaceId` matches the portal URL
2. **Update Staff Record**: Assign staff member to correct space

## Migration Process

The system includes an automatic migration that:
1. Finds staff members with PINs in their staff documents
2. Creates corresponding documents in the `staff-pins` collection
3. Preserves PIN values and first-login status

### Manual Migration

If automatic migration doesn't work:

1. **Via Dashboard**:
   - Go to Dashboard → Staff PINs
   - Click "Migrate PINs" button

2. **Via Debug Page**:
   - Visit `/debug/staff-pins`
   - Click "Run Migration" button

## Debug Tools

### Debug Page: `/debug/staff-pins`

This page shows:
- All staff members for current space
- PIN status (legacy vs new system)
- Migration status
- PIN test functionality

### PIN Test Feature

Use the PIN test feature to:
1. Enter a 4-digit PIN
2. See if it's found in the system
3. Check verification results
4. View detailed logs in browser console

## Database Structure

### Staff Document (Legacy)
```
staff/{staffId}
{
  displayName: "John Doe",
  email: "<EMAIL>",
  spaceId: "space123",
  pin: {
    value: "1234",
    hasChangedDefault: false,
    lastUpdated: "2024-01-01T00:00:00Z"
  }
}
```

### Staff PIN Document (New System)
```
staff-pins/{pinDocId}
{
  pin: "1234",
  staffId: "staff123",
  createdAt: Timestamp,
  updatedAt: Timestamp,
  isFirstLogin: true,
  attempts: 0
}
```

## Best Practices

1. **Always run migration** after creating new staff members
2. **Use the debug tools** to verify PIN status
3. **Check browser console** for detailed error logs
4. **Test PINs** using the debug page before giving them to staff
5. **Keep PINs secure** and change them if compromised

## Getting Help

If issues persist:
1. Check browser console for detailed error messages
2. Use the debug page to test specific PINs
3. Verify database structure matches expected format
4. Contact system administrator for database-level troubleshooting

## Recent Changes

- **PIN Migration**: Automatic migration from staff documents to staff-pins collection
- **Fallback System**: If PIN not found in new system, checks legacy system
- **Debug Tools**: Added comprehensive debugging and testing tools
- **Better Error Messages**: More helpful error messages for common issues
