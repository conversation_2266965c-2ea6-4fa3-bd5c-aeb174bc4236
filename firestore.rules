rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }
    
    function getUserSubscriptionTier() {
      return getUserData().subscriptionTier;
    }
    
    function hasFeatureAccess(feature) {
      let tier = getUserSubscriptionTier();
      
      // Basic features (available on all tiers including free)
      let basicFeatures = ['basic_appointments', 'basic_customer_management', 'basic_email_notifications'];
      
      // Pro features (require Pro or Enterprise)
      let proFeatures = ['advanced_loyalty_program', 'advanced_referral_system', 'advanced_sms_notifications', 
                        'advanced_marketing', 'advanced_analytics'];
      
      // Staff portal is available from Basic tier up
      let basicPlusFeatures = ['advanced_staff_portal'];
      
      // Enterprise features (require Enterprise tier only)
      let enterpriseFeatures = ['enterprise_custom_branding', 'enterprise_api_access', 'enterprise_dedicated_support'];
      
      return (feature in basicFeatures) ||
             (feature in basicPlusFeatures && tier in ['basic', 'pro', 'enterprise']) ||
             (feature in proFeatures && tier in ['pro', 'enterprise']) ||
             (feature in enterpriseFeatures && tier == 'enterprise');
    }
    
    function isSpaceOwner(spaceId) {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/spaces/$(spaceId)).data.ownerId == request.auth.uid;
    }
    
    function isSalonOwner(salonId) {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/salons/$(salonId)).data.ownerId == request.auth.uid;
    }
    
    function isSpaceOrSalonOwner(resourceData) {
      return isAuthenticated() && 
             ((resourceData.spaceId != null && isSpaceOwner(resourceData.spaceId)) ||
              (resourceData.salonId != null && isSalonOwner(resourceData.salonId)));
    }

    // Default rule - deny all access
    match /{document=**} {
      allow read, write: if false;
    }
    
    // User profiles - users can read/write their own data
    match /users/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // Spaces - users can read any space (for public booking), but only write their own
    match /spaces/{spaceId} {
      allow read: if true; // Public read for booking page
      allow write: if isAuthenticated() && resource.data.ownerId == request.auth.uid;
    }
    
    // Salons - backward compatibility for spaces
    match /salons/{salonId} {
      allow read: if true; // Public read for booking page
      allow write: if isAuthenticated() && resource.data.ownerId == request.auth.uid;
    }
    
    // Staff - space/salon owners can manage their staff
    match /staff/{staffId} {
      allow read: if true; // Public read for booking page (staff selection)
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Services - space/salon owners can manage their services
    match /services/{serviceId} {
      allow read: if true; // Public read for booking page (service selection)
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Customers - space/salon owners can manage their customers
    match /customers/{customerId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if true; // Allow public booking to create customers
    }
    
    // Appointments - space/salon owners can manage, public can create
    match /appointments/{appointmentId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if true; // Allow public appointment booking
    }
    
    // Active services - space/salon owners can manage
    match /active-services/{serviceId} {
      allow read, write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Messages - authenticated users can read and create
    match /messages/{messageId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if false; // No updates or deletes
    }
    
    // Contact form submissions - anyone can create, authenticated users can read
    match /contact-submissions/{submissionId} {
      allow read: if isAuthenticated();
      allow create: if true; // Anyone can submit contact form
      allow update, delete: if false;
    }
    
    // Loyalty transactions - require loyalty program access
    match /loyalty-transactions/{transactionId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_loyalty_program') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_loyalty_program') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_loyalty_program') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Loyalty rewards - require loyalty program access
    match /loyalty-rewards/{rewardId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_loyalty_program') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_loyalty_program') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_loyalty_program') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Referral codes - require referral system access
    match /referral-codes/{codeId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_referral_system') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_referral_system') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_referral_system') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Marketing campaigns - require marketing access
    match /marketing-campaigns/{campaignId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_marketing') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_marketing') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_marketing') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Analytics data - require analytics access
    match /analytics/{analyticsId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_analytics') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_analytics') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_analytics') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Custom branding - require enterprise access
    match /custom-branding/{brandingId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('enterprise_custom_branding') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('enterprise_custom_branding') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('enterprise_custom_branding') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Usage tracking - users can read/write their own usage data
    match /usage/{usageId} {
      allow read, write: if isAuthenticated() && 
                            usageId.matches(request.auth.uid + '_.*');
    }
    
    // Invoices - space/salon owners can manage their invoices
    match /invoices/{invoiceId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Reviews - public can read, space/salon owners can manage their reviews
    match /reviews/{reviewId} {
      allow read: if true; // Public read for review display
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if true; // Anyone can create reviews
    }
    
    // Shops - authenticated users can read/write shop settings
    match /shops/{shopId} {
      allow read, write: if isAuthenticated();
    }
  }
}
