rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isResourceOwner(resource) {
      return isAuthenticated() && request.auth.uid == resource.ownerId;
    }
    
    function isSpaceOrSalonOwner(resource) {
      return isAuthenticated() && 
             (request.auth.uid == resource.ownerId || 
              request.auth.uid == resource.spaceId ||
              request.auth.uid == resource.salonId);
    }
    
    function hasFeatureAccess(feature) {
      // For now, allow all features. In production, check subscription status
      return true;
    }

    // Users - users can read/write their own data
    match /users/{userId} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
    }

    // Spaces - space owners can manage their spaces
    match /spaces/{spaceId} {
      allow read: if true; // Public read for booking page
      allow update, delete: if isResourceOwner(resource.data);
      allow create: if isAuthenticated() && request.auth.uid == request.resource.data.ownerId;
    }

    // Business hours - space/salon owners can manage their hours
    match /business-hours/{hourId} {
      allow read: if true; // Public read for booking page (checking business hours)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }

    // Staff - space/salon owners can manage their staff
    match /staff/{staffId} {
      allow read: if true; // Public read for booking page (staff selection)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }

    // Staff PINs - PUBLIC READ for staff login
    match /staff-pins/{pinId} {
      allow read: if true; // Allow public read for PIN verification during staff login
      allow update, delete: if isAuthenticated() && 
                      exists(/databases/$(database)/documents/staff/$(resource.data.staffId)) &&
                      isSpaceOrSalonOwner(get(/databases/$(database)/documents/staff/$(resource.data.staffId)).data);
      allow create: if isAuthenticated() && 
                       exists(/databases/$(database)/documents/staff/$(request.resource.data.staffId)) &&
                       isSpaceOrSalonOwner(get(/databases/$(database)/documents/staff/$(request.resource.data.staffId)).data);
    }

    // PIN reset requests - space/salon owners can manage requests for their staff
    match /pin-reset-requests/{requestId} {
      allow read: if isAuthenticated() && 
                     isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && 
                      isSpaceOrSalonOwner(resource.data);
      allow create: if true; // Staff can create reset requests
    }
    
    // Services - space/salon owners can manage their services
    match /services/{serviceId} {
      allow read: if true; // Public read for booking page (service selection)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Customers - space/salon owners can manage their customers, public read for appointments
    match /customers/{customerId} {
      allow read: if true; // Allow public read for appointment details (booking system needs this)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if (request.resource.data.keys().hasAll(['displayName']) &&
                       request.resource.data.displayName is string &&
                       // Must have either email OR phone (or both)
                       (request.resource.data.keys().hasAny(['email', 'phoneNumber']) &&
                        // If email exists, validate format
                        (!request.resource.data.keys().hasAny(['email']) || request.resource.data.email.matches('.*@.*\\..*')) &&
                        // If phone exists, validate it's a string
                        (!request.resource.data.keys().hasAny(['phoneNumber']) || request.resource.data.phoneNumber is string))) ||
                      // Allow authenticated users to create customers for their space
                      (isAuthenticated() && isSpaceOrSalonOwner(request.resource.data));
    }

    // Appointments - public read for scheduling, protected write operations
    match /appointments/{appointmentId} {
      allow read: if true; // Allow public read for appointment scheduling (checking availability)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if (request.resource.data.keys().hasAll(['customerId', 'serviceId', 'staffId', 'spaceId', 'startTime', 'endTime']) &&
                       request.resource.data.customerId is string &&
                       request.resource.data.serviceId is string &&
                       request.resource.data.staffId is string &&
                       request.resource.data.spaceId is string &&
                       request.resource.data.startTime is string &&
                       request.resource.data.endTime is string) ||
                      // Allow authenticated users to create appointments for their space
                      (isAuthenticated() && isSpaceOrSalonOwner(request.resource.data));
    }

    // Active services - space/salon owners can manage
    match /active-services/{serviceId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }

    // Invoices - space/salon owners can manage their invoices
    match /invoices/{invoiceId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }

    // Reviews - public can read, validated creation
    match /reviews/{reviewId} {
      allow read: if true; // Public read for review display
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if request.resource.data.keys().hasAll(['rating', 'comment']) &&
                       request.resource.data.rating is int &&
                       request.resource.data.rating >= 1 &&
                       request.resource.data.rating <= 5;
    }

    // Contact form submissions - validated public creation
    match /contact-submissions/{submissionId} {
      allow read: if isAuthenticated();
      allow create: if request.resource.data.keys().hasAll(['name', 'email', 'message']) &&
                       request.resource.data.email.matches('.*@.*\\..*');
      allow update, delete: if false;
    }

    // Usage tracking - users can read/write their own usage data
    match /usage/{usageId} {
      allow read, write: if isAuthenticated() &&
                            usageId.matches(request.auth.uid + '_.*');
    }

    // Default deny all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
