rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function getUserSubscriptionTier() {
      // Better to use custom claims instead of database lookup for performance
      return request.auth.token.subscriptionTier;
    }

    function hasFeatureAccess(feature) {
      let tier = getUserSubscriptionTier();

      // Basic features (available on all tiers including free)
      let basicFeatures = ['basic_appointments', 'basic_customer_management', 'basic_email_notifications'];

      // Pro features (require Pro or Enterprise)
      let proFeatures = ['advanced_loyalty_program', 'advanced_referral_system', 'advanced_sms_notifications',
                        'advanced_marketing', 'advanced_analytics'];

      // Staff portal is available from Basic tier up
      let basicPlusFeatures = ['advanced_staff_portal'];

      // Enterprise features (require Enterprise tier only)
      let enterpriseFeatures = ['enterprise_custom_branding', 'enterprise_api_access', 'enterprise_dedicated_support'];

      return basicFeatures.hasAny([feature]) ||
             (basicPlusFeatures.hasAny([feature]) && ['basic', 'pro', 'enterprise'].hasAny([tier])) ||
             (proFeatures.hasAny([feature]) && ['pro', 'enterprise'].hasAny([tier])) ||
             (enterpriseFeatures.hasAny([feature]) && tier == 'enterprise');
    }

    function isSpaceOwner(spaceId) {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/spaces/$(spaceId)).data.ownerId == request.auth.uid;
    }

    function isSalonOwner(salonId) {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/salons/$(salonId)).data.ownerId == request.auth.uid;
    }

    function isSpaceOrSalonOwner(resourceData) {
      return isAuthenticated() &&
             ((resourceData.spaceId != null && isSpaceOwner(resourceData.spaceId)) ||
              (resourceData.salonId != null && isSalonOwner(resourceData.salonId)));
    }

    function isResourceOwner(data) {
      return isAuthenticated() && data.ownerId == request.auth.uid;
    }

    // Default rule - deny all access
    match /{document=**} {
      allow read, write: if false;
    }
    
    // User profiles - users can read/write their own data
    match /users/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // Spaces - users can read any space (for public booking), but only write their own
    match /spaces/{spaceId} {
      allow read: if true; // Public read for booking page
      allow update, delete: if isResourceOwner(resource.data);
      allow create: if isAuthenticated() && request.resource.data.ownerId == request.auth.uid;
    }

    // Salons - backward compatibility for spaces
    match /salons/{salonId} {
      allow read: if true; // Public read for booking page
      allow update, delete: if isResourceOwner(resource.data);
      allow create: if isAuthenticated() && request.resource.data.ownerId == request.auth.uid;
    }
    
    // Staff - space/salon owners can manage their staff
    match /staff/{staffId} {
      allow read: if true; // Public read for booking page (staff selection)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Staff PINs - space/salon owners can manage their staff PINs
    match /staff-pins/{pinId} {
      allow read: if isAuthenticated() && 
                     exists(/databases/$(database)/documents/staff/$(resource.data.staffId)) &&
                     isSpaceOrSalonOwner(get(/databases/$(database)/documents/staff/$(resource.data.staffId)).data);
      allow write: if isAuthenticated() && 
                      exists(/databases/$(database)/documents/staff/$(resource.data.staffId)) &&
                      isSpaceOrSalonOwner(get(/databases/$(database)/documents/staff/$(resource.data.staffId)).data);
      allow create: if isAuthenticated() && 
                       exists(/databases/$(database)/documents/staff/$(request.resource.data.staffId)) &&
                       isSpaceOrSalonOwner(get(/databases/$(database)/documents/staff/$(request.resource.data.staffId)).data);
    }
    
    // PIN reset requests - space/salon owners can manage requests for their staff
    match /pin-reset-requests/{requestId} {
      allow read: if isAuthenticated() && 
                     exists(/databases/$(database)/documents/staff/$(resource.data.staffId)) &&
                     isSpaceOrSalonOwner(get(/databases/$(database)/documents/staff/$(resource.data.staffId)).data);
      allow write: if isAuthenticated() && 
                      exists(/databases/$(database)/documents/staff/$(resource.data.staffId)) &&
                      isSpaceOrSalonOwner(get(/databases/$(database)/documents/staff/$(resource.data.staffId)).data);
      allow create: if true; // Staff can create reset requests
    }
    
    // Services - space/salon owners can manage their services
    match /services/{serviceId} {
      allow read: if true; // Public read for booking page (service selection)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Customers - space/salon owners can manage their customers, limited public read for appointments
    match /customers/{customerId} {
      allow read: if (isAuthenticated() && isSpaceOrSalonOwner(resource.data)) ||
                     // Allow public read for appointment details (booking system needs this)
                     true;
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if (request.resource.data.keys().hasAll(['displayName']) &&
                       request.resource.data.displayName is string &&
                       // Must have either email OR phone (or both)
                       (request.resource.data.keys().hasAny(['email', 'phoneNumber']) &&
                        // If email exists, validate format
                        (!request.resource.data.keys().hasAny(['email']) || request.resource.data.email.matches('.*@.*\\..*')) &&
                        // If phone exists, validate it's a string
                        (!request.resource.data.keys().hasAny(['phoneNumber']) || request.resource.data.phoneNumber is string))) ||
                      // Allow authenticated users to create customers for their space
                      (isAuthenticated() && isSpaceOrSalonOwner(request.resource.data));
    }

    // Appointments - space/salon owners can manage, public read for scheduling, validated public creation
    match /appointments/{appointmentId} {
      allow read: if true; // Allow public read for appointment scheduling (checking availability)
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if (request.resource.data.keys().hasAll(['customerId', 'serviceId', 'staffId', 'spaceId', 'startTime', 'endTime']) &&
                       request.resource.data.customerId is string &&
                       request.resource.data.serviceId is string &&
                       request.resource.data.staffId is string &&
                       request.resource.data.spaceId is string &&
                       request.resource.data.startTime is string &&
                       request.resource.data.endTime is string) ||
                      // Allow authenticated users to create appointments for their space
                      (isAuthenticated() && isSpaceOrSalonOwner(request.resource.data));
    }
    
    // Active services - space/salon owners can manage
    match /active-services/{serviceId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Revenue targets - space/salon owners can manage their revenue targets
    match /revenueTargets/{targetId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Messages - authenticated users can read and create
    match /messages/{messageId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if false; // No updates or deletes
    }
    
    // Contact form submissions - anyone can create, authenticated users can read
    match /contact-submissions/{submissionId} {
      allow read: if isAuthenticated();
      allow create: if true; // Anyone can submit contact form
      allow update, delete: if false;
    }
    
    // Contacts - anyone can create, authenticated users can read
    match /contacts/{contactId} {
      allow read: if isAuthenticated();
      allow create: if true; // Anyone can submit contact form
      allow write: if false; // No updates after creation
    }
    
    // Inventory - space/salon owners can manage their inventory
    match /inventory/{inventoryId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Notifications - space/salon owners can manage their notifications
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Loyalty transactions - require loyalty program access
    match /loyalty-transactions/{transactionId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_loyalty_program') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_loyalty_program') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_loyalty_program') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Loyalty rewards - require loyalty program access
    match /loyalty-rewards/{rewardId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_loyalty_program') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_loyalty_program') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_loyalty_program') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Referral codes - require referral system access
    match /referral-codes/{codeId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_referral_system') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_referral_system') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_referral_system') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Marketing campaigns - require marketing access
    match /marketing-campaigns/{campaignId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_marketing') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_marketing') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_marketing') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Analytics data - require analytics access
    match /analytics/{analyticsId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('advanced_analytics') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('advanced_analytics') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('advanced_analytics') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Onboarding analytics - users can write their own onboarding data
    match /onboarding-analytics/{analyticsId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow write: if false; // No updates after creation
    }
    
    // Space analytics - space owners can read their analytics
    match /space-analytics/{spaceId} {
      allow read: if isAuthenticated() && isSpaceOwner(spaceId);
      allow write: if isAuthenticated() && isSpaceOwner(spaceId);
      allow create: if isAuthenticated() && isSpaceOwner(spaceId);
    }
    
    // Custom branding - require enterprise access
    match /custom-branding/{brandingId} {
      allow read: if isAuthenticated() && 
                     hasFeatureAccess('enterprise_custom_branding') &&
                     isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && 
                      hasFeatureAccess('enterprise_custom_branding') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && 
                       hasFeatureAccess('enterprise_custom_branding') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Usage tracking - users can read/write their own usage data
    match /usage/{usageId} {
      allow read, write: if isAuthenticated() && 
                            usageId.matches(request.auth.uid + '_.*');
    }
    
    // Invoices - space/salon owners can manage their invoices
    match /invoices/{invoiceId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }
    
    // Reviews - public can read, space/salon owners can manage their reviews
    match /reviews/{reviewId} {
      allow read: if true; // Public read for review display
      allow write: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if true; // Anyone can create reviews
    }
    
    // Shops - authenticated users can read/write shop settings
    match /shops/{shopId} {
      allow read, write: if isAuthenticated();
    }

    // Commission records - space/salon owners can manage their staff commissions
    match /commission-records/{commissionId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }

    // Service product usage - space/salon owners can manage their product usage records
    match /service-product-usage/{usageId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }

    // Service transactions - space/salon owners can manage their transaction records
    match /service-transactions/{transactionId} {
      allow read: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() && isSpaceOrSalonOwner(request.resource.data);
    }

    // Loyalty transactions - require loyalty program access
    match /loyalty-transactions/{transactionId} {
      allow read: if isAuthenticated() &&
                     hasFeatureAccess('advanced_loyalty_program') &&
                     isSpaceOrSalonOwner(resource.data);
      allow update, delete: if isAuthenticated() &&
                      hasFeatureAccess('advanced_loyalty_program') &&
                      isSpaceOrSalonOwner(resource.data);
      allow create: if isAuthenticated() &&
                       hasFeatureAccess('advanced_loyalty_program') &&
                       isSpaceOrSalonOwner(request.resource.data);
    }

    // Reviews - public can read, validated creation
    match /reviews/{reviewId} {
      allow read: if true; // Public read for review display
      allow update, delete: if isAuthenticated() && isSpaceOrSalonOwner(resource.data);
      allow create: if request.resource.data.keys().hasAll(['rating', 'comment']) &&
                       request.resource.data.rating is int &&
                       request.resource.data.rating >= 1 &&
                       request.resource.data.rating <= 5;
    }

    // Contact form submissions - validated public creation
    match /contact-submissions/{submissionId} {
      allow read: if isAuthenticated();
      allow create: if request.resource.data.keys().hasAll(['name', 'email', 'message']) &&
                       request.resource.data.email.matches('.*@.*\\..*');
      allow update, delete: if false;
    }

    // Usage tracking - users can read/write their own usage data
    match /usage/{usageId} {
      allow read, write: if isAuthenticated() &&
                            usageId.matches(request.auth.uid + '_.*');
    }

    // Add rate limiting collection for public endpoints
    match /rate-limits/{clientId} {
      allow read, write: if true; // Managed by Cloud Functions
    }
  }
}
