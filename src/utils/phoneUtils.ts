/**
 * Utility functions for phone number validation and formatting
 */

/**
 * Validates if a phone number is in E.164 format
 * E.164 format: +[country code][subscriber number]
 * Example: +254712345678
 */
export function isValidE164(phoneNumber: string): boolean {
  // E.164 regex pattern: + followed by 1-15 digits
  const e164Pattern = /^\+[1-9]\d{1,14}$/;
  return e164Pattern.test(phoneNumber);
}

/**
 * Attempts to format a phone number to E.164 format
 * If the number already has a country code, it will be preserved
 * Otherwise, it will add the default country code
 * 
 * @param phoneNumber The phone number to format
 * @param defaultCountryCode The default country code to use if none is provided (default: 254 for Kenya)
 * @returns The formatted phone number or null if it cannot be formatted
 */
export function formatToE164(phoneNumber: string, defaultCountryCode = '254'): string | null {
  // Remove all non-digit characters except the leading +
  let cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // If the number already starts with +, assume it's already in international format
  if (cleaned.startsWith('+')) {
    return isValidE164(cleaned) ? cleaned : null;
  }
  
  // If the number starts with 0, assume it's a local number and replace the 0 with the country code
  if (cleaned.startsWith('0')) {
    cleaned = `+${defaultCountryCode}${cleaned.substring(1)}`;
    return isValidE164(cleaned) ? cleaned : null;
  }
  
  // If the number doesn't start with 0 or +, assume it's missing the country code
  cleaned = `+${defaultCountryCode}${cleaned}`;
  return isValidE164(cleaned) ? cleaned : null;
}

/**
 * Formats a phone number for display
 * 
 * @param phoneNumber The E.164 formatted phone number
 * @returns A formatted phone number for display (e.g., +254 712 345 678)
 */
export function formatPhoneForDisplay(phoneNumber: string): string {
  if (!isValidE164(phoneNumber)) {
    return phoneNumber;
  }
  
  // Extract country code and subscriber number
  const countryCode = phoneNumber.substring(1, 4); // Assuming 3-digit country code
  const subscriberNumber = phoneNumber.substring(4);
  
  // Format the subscriber number with spaces for readability
  // This is a simple implementation - you might want to customize based on country
  const formatted = subscriberNumber.replace(/(\d{3})(\d{3})(\d{3,})/, '$1 $2 $3');
  
  return `+${countryCode} ${formatted}`;
}

/**
 * Format a phone number to international format for consistent database storage
 * Attempts to use formatToE164 first, but returns the original if that fails
 * This ensures we always have a usable phone format for database lookups
 * 
 * @param phoneNumber The phone number in any format
 * @returns International formatted phone number or the original if formatting fails
 */
export function formatToInternational(phoneNumber: string): string {
  if (!phoneNumber || phoneNumber.trim() === '') {
    return phoneNumber;
  }
  
  // Try to format to E.164 format
  const formattedNumber = formatToE164(phoneNumber);
  
  // Return the formatted number if successful, otherwise return the original
  return formattedNumber || phoneNumber;
}
