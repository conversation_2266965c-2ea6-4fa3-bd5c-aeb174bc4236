import { hasFeatureAccess } from '@/services/subscriptionService';

/**
 * Server-side feature access utilities
 */

export interface AuthenticatedUser {
  uid: string;
  email?: string;
}

/**
 * Extract user ID from request headers or body
 * This is a simplified approach - in production you'd verify the Firebase token
 */
export async function getUserFromRequest(request: Request): Promise<string | null> {
  try {
    // Try to get from body first
    const body = await request.json();
    return body.userId || null;
  } catch {
    // If body parsing fails, try headers
    const userId = request.headers.get('x-user-id');
    return userId;
  }
}

/**
 * Require feature access for an API route
 */
export async function requireFeatureAccess(request: Request, feature: string): Promise<string> {
  const userId = await getUserFromRequest(request);
  
  if (!userId) {
    throw new Error('User ID required');
  }
  
  const hasAccess = await hasFeatureAccess(userId, feature);
  if (!hasAccess) {
    throw new Error(`Access denied: This feature requires a higher plan`);
  }
  
  return userId;
}

/**
 * API Response helpers
 */
export const ApiResponse = {
  unauthorized: () => new Response(
    JSON.stringify({ error: 'Authentication required' }), 
    { status: 401, headers: { 'Content-Type': 'application/json' } }
  ),
  
  forbidden: (message = 'Access denied: This feature requires a higher plan') => new Response(
    JSON.stringify({ error: message }), 
    { status: 403, headers: { 'Content-Type': 'application/json' } }
  ),
  
  badRequest: (message = 'Bad request') => new Response(
    JSON.stringify({ error: message }), 
    { status: 400, headers: { 'Content-Type': 'application/json' } }
  ),
  
  success: (data: any) => new Response(
    JSON.stringify(data), 
    { status: 200, headers: { 'Content-Type': 'application/json' } }
  ),
  
  error: (message = 'Internal server error') => new Response(
    JSON.stringify({ error: message }), 
    { status: 500, headers: { 'Content-Type': 'application/json' } }
  )
};
