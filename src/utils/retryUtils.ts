/**
 * Utility functions for handling retries on operations
 */

// Default retry settings
const DEFAULT_RETRY_OPTIONS = {
  maxRetries: 3,
  initialDelayMs: 500,
  backoffFactor: 2,
  shouldRetry: (error: Error) => true, // By default, retry all errors
};

export type RetryOptions = Partial<typeof DEFAULT_RETRY_OPTIONS>;

/**
 * Retry a function if it fails
 * 
 * @param fn Function to retry
 * @param options Retry options
 * @returns Result of the function
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options?: RetryOptions
): Promise<T> {
  const config = {
    ...DEFAULT_RETRY_OPTIONS,
    ...options,
  };
  
  let lastError: Error | undefined;
  
  for (let attempt = 0; attempt < config.maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Check if we should retry this error
      if (!config.shouldRetry(lastError)) {
        throw lastError;
      }
      
      // Last attempt, don't wait
      if (attempt === config.maxRetries - 1) {
        throw lastError;
      }
      
      // Calculate backoff time
      const delayMs = config.initialDelayMs * Math.pow(config.backoffFactor, attempt);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
  
  // This should never happen, but TypeScript requires a return
  throw lastError ?? new Error('Retry failed for unknown reason');
}

/**
 * Track operations that have failed after all retries,
 * so they can be retried later if needed
 */
export class FailedOperationsQueue {
  private static instance: FailedOperationsQueue;
  private queue: Array<{
    operation: string;
    params: any;
    timestamp: number;
    error: string;
  }> = [];
  
  private constructor() {
    // Load from localStorage if available
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem('failedLoyaltyOperations');
        if (saved) {
          this.queue = JSON.parse(saved);
        }
      } catch (error) {
        console.error('Error loading failed operations from localStorage:', error);
      }
    }
  }
  
  public static getInstance(): FailedOperationsQueue {
    if (!FailedOperationsQueue.instance) {
      FailedOperationsQueue.instance = new FailedOperationsQueue();
    }
    return FailedOperationsQueue.instance;
  }
  
  /**
   * Add a failed operation to the queue
   */
  public addFailedOperation(operation: string, params: any, error: Error | string): void {
    this.queue.push({
      operation,
      params,
      timestamp: Date.now(),
      error: error instanceof Error ? error.message : error
    });
    
    this.saveToStorage();
  }
  
  /**
   * Get all failed operations
   */
  public getFailedOperations() {
    return [...this.queue];
  }
  
  /**
   * Remove an operation from the queue
   */
  public removeOperation(index: number): void {
    if (index >= 0 && index < this.queue.length) {
      this.queue.splice(index, 1);
      this.saveToStorage();
    }
  }
  
  /**
   * Clear all failed operations
   */
  public clearAll(): void {
    this.queue = [];
    this.saveToStorage();
  }
  
  /**
   * Save the queue to localStorage
   */
  private saveToStorage(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('failedLoyaltyOperations', JSON.stringify(this.queue));
      } catch (error) {
        console.error('Error saving failed operations to localStorage:', error);
      }
    }
  }
}
