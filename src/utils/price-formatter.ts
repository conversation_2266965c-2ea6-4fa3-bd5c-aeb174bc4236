import { formatCurrency } from './currency';
import type { Space } from '@/services/types/models';

export function formatPrice(amount: number, space: Space | null | undefined) {
  if (!space?.currency) {
    // Fallback to KES if no space currency settings
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  return formatCurrency(amount, space.currency, {
    minimumFractionDigits: 0,
  });
}
