export const EMAIL_SENDER = 'GroomBook <<EMAIL>>';

/**
 * Client wrapper that calls the secure email API route
 */
export async function sendEmail({
  to,
  subject,
  text,
  html,
  from = EMAIL_SENDER,
}: {
  to: string | string[];
  subject: string;
  text: string;
  html: string;
  from?: string;
}): Promise<boolean> {
  try {
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ to, subject, text, html, from }),
    });
    const data = await response.json();
    return data.success;
  } catch {
    return false;
  }
}

/**
 * Generates HTML template for referral invitation emails
 */
export function getReferralEmailTemplate({
  senderName,
  referralCode,
  referralUrl,
  customMessage,
}: {
  senderName: string;
  referralCode: string;
  referralUrl: string;
  customMessage?: string;
}): string {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <header style="text-align: center; margin-bottom: 30px;">
        <img src="https://yourapp.com/logo.png" alt="GroomBook Logo" style="max-width: 150px;" />
      </header>
      
      <div style="background-color: #FFF8E0; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
        <h2 style="color: #333; margin-top: 0; margin-bottom: 20px;">You've Been Invited!</h2>
        <p style="font-size: 16px; line-height: 1.5; color: #333;">
          ${customMessage || `${senderName} has invited you to join GroomBook, an easy way to manage bookings for your service space!`}
        </p>
        
        <div style="background-color: #F5F5F5; border-radius: 4px; padding: 15px; margin: 20px 0; text-align: center;">
          <p style="margin: 0; font-size: 14px; color: #666;">Your referral code</p>
          <p style="margin: 10px 0 0; font-size: 24px; font-weight: bold; letter-spacing: 2px; color: #333;">${referralCode}</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
          <a href="${referralUrl}" style="display: inline-block; background-color: #F5B800; color: #333; padding: 14px 30px; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px;">
            Create Your Account
          </a>
        </div>
      </div>
      
      <footer style="text-align: center; color: #666; font-size: 14px;">
        <p>This invitation was sent to you by a GroomBook user. If you didn't expect this invitation, you can ignore it.</p>
        <p>&copy; ${new Date().getFullYear()} GroomBook. All rights reserved.</p>
      </footer>
    </div>
  `;
}


