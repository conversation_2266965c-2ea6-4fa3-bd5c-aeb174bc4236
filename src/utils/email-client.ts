/**
 * Client-side helper for sending emails via the secure API route
 * This can be imported and used in any client component
 */
export async function sendEmailClient({
  to,
  subject,
  text,
  html,
  from,
}: {
  to: string | string[];
  subject: string;
  text?: string;
  html: string;
  from?: string;
}): Promise<boolean> {
  try {
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to,
        subject,
        text,
        html,
        from,
      }),
    });

    const data = await response.json();
    
    if (!response.ok) {
      console.error('Failed to send email:', data.error);
      return false;
    }

    return data.success;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

/**
 * Client-side helper specifically for sending welcome emails
 */
export async function sendWelcomeEmail({
  email,
  name,
  dashboardUrl,
}: {
  email: string;
  name?: string;
  dashboardUrl?: string;
}): Promise<boolean> {
  try {
    const response = await fetch('/api/welcome-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        name,
        dashboardUrl,
      }),
    });

    const data = await response.json();
    
    if (!response.ok) {
      console.error('Failed to send welcome email:', data.error);
      return false;
    }

    return data.success;
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return false;
  }
}
