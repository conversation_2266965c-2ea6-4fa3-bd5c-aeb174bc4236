interface CurrencySettings {
  code: string;
  symbol: string;
  name: string;
  position: 'prefix' | 'suffix';
}

/**
 * Formats a number value according to the given currency settings
 * @param amount - The amount to format
 * @param currency - The currency settings object
 * @param options - Optional formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: CurrencySettings,
  options: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {}
): string {
  const formatted = new Intl.NumberFormat(undefined, {
    minimumFractionDigits: options.minimumFractionDigits ?? 0,
    maximumFractionDigits: options.maximumFractionDigits ?? 2,
  }).format(amount);

  return currency.position === 'prefix'
    ? `${currency.symbol}${formatted}`
    : `${formatted}${currency.symbol}`;
}

/**
 * Parses a currency string into a number
 * @param value - The currency string to parse
 * @param currency - The currency settings object
 * @returns The parsed number value
 */
export function parseCurrencyValue(
  value: string,
  currency: CurrencySettings
): number | null {
  // Remove currency symbol and any non-numeric characters except decimal point
  const cleaned = value
    .replace(currency.symbol, '')
    .replace(/[^\d.]/g, '');

  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? null : parsed;
}

/**
 * Gets default currency settings for a country code
 * @param countryCode - ISO country code (e.g., 'KE' for Kenya)
 * @returns Default currency settings for the country
 */
export function getDefaultCurrencyForCountry(countryCode: string): CurrencySettings {
  const defaults: { [key: string]: CurrencySettings } = {
    KE: {
      code: 'KES',
      symbol: 'Ksh',
      name: 'Kenyan Shilling',
      position: 'prefix'
    },
    US: {
      code: 'USD',
      symbol: '$',
      name: 'US Dollar',
      position: 'prefix'
    },
    GB: {
      code: 'GBP',
      symbol: '£',
      name: 'British Pound',
      position: 'prefix'
    },
    EU: {
      code: 'EUR',
      symbol: '€',
      name: 'Euro',
      position: 'prefix'
    },
    NG: {
      code: 'NGN',
      symbol: '₦',
      name: 'Nigerian Naira',
      position: 'prefix'
    },
    ZA: {
      code: 'ZAR',
      symbol: 'R',
      name: 'South African Rand',
      position: 'prefix'
    },
    UG: {
      code: 'UGX',
      symbol: 'USh',
      name: 'Ugandan Shilling',
      position: 'prefix'
    },
    TZ: {
      code: 'TZS',
      symbol: 'TSh',
      name: 'Tanzanian Shilling',
      position: 'prefix'
    },
    RW: {
      code: 'RWF',
      symbol: 'RF',
      name: 'Rwandan Franc',
      position: 'prefix'
    },
  };

  return defaults[countryCode] || defaults['KE']; // Default to KES if country not found
}

export function formatPrice(value: number, space?: { currency?: { code: string, symbol: string, name: string, position: 'prefix' | 'suffix' } }) {
  if (!space?.currency) {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(value);
  }

  return formatCurrency(value, space.currency, {
    minimumFractionDigits: 0,
  });
}
