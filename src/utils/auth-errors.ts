export const firebaseAuthErrors: Record<string, string> = {
  'auth/invalid-credential': 'Invalid email or password. Please check your credentials and try again.',
  'auth/user-not-found': 'No account found with this email address. Please check your email or create a new account.',
  'auth/wrong-password': 'Incorrect password. Please check your password and try again.',
  'auth/email-already-in-use': 'An account already exists with this email address. Please use a different email or try logging in.',
  'auth/weak-password': 'Password must be at least 8 characters long.',
  'auth/invalid-email': 'Please enter a valid email address.',
  'auth/user-disabled': 'This account has been disabled. Please contact support for assistance.',
  'auth/operation-not-allowed': 'This type of account is not allowed. Please contact support for assistance.',
  'auth/too-many-requests': 'Too many unsuccessful attempts. Please try again later.',
  'auth/network-request-failed': 'A network error occurred. Please check your internet connection and try again.',
  'auth/popup-closed-by-user': 'The sign in popup was closed before completing authentication.',
  'auth/requires-recent-login': 'Please sign in again to perform this action.',
};

export function getAuthErrorMessage(error: any): string {
  // Check if it's an error with a Firebase auth code
  if (typeof error === 'object' && error !== null) {
    // First check the error.code which is the standard Firebase error format
    if (error.code && typeof error.code === 'string' && firebaseAuthErrors[error.code]) {
      return firebaseAuthErrors[error.code];
    }
    
    // If no code found, check if the message contains a Firebase error code
    if (error.message && typeof error.message === 'string') {
      // Extract the error code from a message like "Firebase: Error (auth/invalid-credential)."
      const match = error.message.match(/\((.*?)\)/);
      if (match && match[1] && firebaseAuthErrors[match[1]]) {
        return firebaseAuthErrors[match[1]];
      }
    }
  }

  // If we can't find a specific error message, use a generic one
  return 'An error occurred during authentication. Please try again.';
}
