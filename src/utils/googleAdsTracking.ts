/**
 * Google Ads conversion tracking utility
 * 🚨 COMPLETELY DISABLED DUE TO $100 BILLING ISSUE 🚨
 */

declare global {
  function gtag(...args: any[]): void;
}

/**
 * Track conversion with callback for redirects or navigation
 * 🚨 DISABLED - WAS CAUSING EXPENSIVE BILLING ISSUES 🚨
 */
export const gtag_report_conversion = (url?: string) => {
  console.log('🚫 Google Ads tracking COMPLETELY DISABLED due to $100 billing issue');
  
  // Still handle navigation without any tracking
  if (url) {
    window.location.href = url;
  }
  return false;
};

/**
 * Track final conversion when users complete registration
 * 🚨 DISABLED - WAS CAUSING EXPENSIVE BILLING ISSUES 🚨
 */
export const trackRegistrationConversion = () => {
  console.log('🚫 Google Ads registration tracking COMPLETELY DISABLED due to $100 billing issue');
  // No tracking whatsoever
  return;
};
