import React from 'react';
import {
  Body,
  Container,
  Column,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Text,
} from '@react-email/components';

interface ReferralEmailProps {
  senderName: string;
  referralCode: string;
  referralUrl: string;
  customMessage?: string;
}

export const ReferralEmail: React.FC<ReferralEmailProps> = ({
  senderName,
  referralCode,
  referralUrl,
  customMessage,
}) => {
  const currentYear = new Date().getFullYear();
  
  return (
    <Html>
      <Head />
      <Preview>You've been invited to join GroomBook by {senderName}! 🎉</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header with Logo */}
          <Section style={logoContainer}>
            <Img
              src="https://groombook.me/logo.png"
              alt="GroomBook Logo"
              width="180"
              height="auto"
            />
          </Section>

          {/* Main Content Section */}
          <Section style={mainSection}>
            <Heading style={h1}>You've Been Invited! 🎉</Heading>
            <Text style={text}>
              {customMessage || `${senderName} has invited you to join GroomBook, the easiest way to manage bookings for your service space!`}
            </Text>
            
            {/* Referral Code Box */}
            <Section style={referralCodeBox}>
              <Text style={referralCodeLabel}>Your referral code</Text>
              <Text style={referralCodeValue}>{referralCode}</Text>
            </Section>
            
            {/* Benefits */}
            <Text style={text}>
              Join GroomBook and enjoy these benefits:
            </Text>
            <Text style={benefitItem}>
              ⏱️ <strong>Save 5+ hours weekly</strong> on administration
            </Text>
            <Text style={benefitItem}>
              📱 <strong>Reduce no-shows by 80%</strong> with automated reminders
            </Text>
            <Text style={benefitItem}>
              🌟 <strong>Increase client satisfaction</strong> with 24/7 booking
            </Text>
            
            {/* CTA Button */}
            <Section style={ctaSection}>
              <Link href={referralUrl} style={button}>
                Create Your Account
              </Link>
            </Section>
            
            {/* Incentive */}
            <Text style={incentiveText}>
              Both you and {senderName} will receive 100 bonus points when you sign up and make your first appointment!
            </Text>
          </Section>
          
          {/* Personal Sign-off */}
          <Section style={section}>
            <Text style={signoffText}>
              We're excited to welcome you to our community!
            </Text>
            <Text style={signatureText}>
              Brian Mwangi<br />
              CEO & Founder, GroomBook
            </Text>
          </Section>
          
          <Hr style={hr} />
          
          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              This invitation was sent to you by a GroomBook user. 
              If you didn't expect this invitation, you can ignore it.
            </Text>
            <Text style={footerText}>
              &copy; {currentYear} GroomBook. All rights reserved.
            </Text>
            <Text style={footerLinks}>
              <Link href="https://groombook.me/privacy" style={footerLink}>Privacy Policy</Link> &nbsp;·&nbsp; 
              <Link href="https://groombook.me/terms" style={footerLink}>Terms of Service</Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#F9F9F9',
  fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
};

const container = {
  maxWidth: '600px',
  margin: '0 auto',
  backgroundColor: '#FFFFFF',
  boxShadow: '0 4px 10px rgba(0,0,0,0.05)',
};

const logoContainer = {
  padding: '25px 0',
  backgroundColor: '#FFF8E0',
  textAlign: 'center' as const,
};

const mainSection = {
  padding: '30px',
  backgroundColor: '#FFF8E0',
  borderRadius: '8px',
  margin: '20px 30px',
};

const section = {
  padding: '0 30px 30px',
};

const h1 = {
  color: '#333333',
  fontSize: '28px',
  fontWeight: 'bold',
  margin: '0 0 15px',
  lineHeight: '1.3',
};

const text = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  margin: '0 0 15px',
};

const benefitItem = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  margin: '0 0 12px',
  paddingLeft: '10px',
};

const referralCodeBox = {
  backgroundColor: '#F5F5F5',
  borderRadius: '4px',
  padding: '15px',
  margin: '20px 0',
  textAlign: 'center' as const,
};

const referralCodeLabel = {
  margin: '0',
  fontSize: '14px',
  color: '#666666',
};

const referralCodeValue = {
  margin: '10px 0 0',
  fontSize: '24px',
  fontWeight: 'bold',
  letterSpacing: '2px',
  color: '#333333',
};

const ctaSection = {
  textAlign: 'center' as const,
  margin: '30px 0 20px',
};

const button = {
  backgroundColor: '#F5B800',
  color: '#333333',
  padding: '14px 30px',
  textDecoration: 'none',
  borderRadius: '6px',
  fontWeight: 'bold',
  fontSize: '16px',
  display: 'inline-block',
};

const incentiveText = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  margin: '0 0 15px',
  textAlign: 'center' as const,
  fontWeight: 'bold' as const,
};

const signoffText = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  margin: '0 0 20px',
};

const signatureText = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  fontWeight: 'bold' as const,
  margin: '0',
};

const hr = {
  borderColor: '#E5E5E5',
  margin: '30px 0',
};

const footer = {
  padding: '20px 30px 30px',
  backgroundColor: '#333333',
  color: '#FFFFFF',
  textAlign: 'center' as const,
};

const footerText = {
  fontSize: '14px',
  color: '#FFFFFF',
  margin: '0 0 10px',
  textAlign: 'center' as const,
};

const footerLinks = {
  fontSize: '14px',
  color: '#FFFFFF',
  margin: '15px 0 0',
  textAlign: 'center' as const,
};

const footerLink = {
  color: '#AAAAAA',
  textDecoration: 'underline',
};

