import React from 'react';
import {
  Body,
  Container,
  Column,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Text,
} from '@react-email/components';

interface WelcomeEmailProps {
  userName?: string;
  dashboardUrl?: string;
}

export const WelcomeEmail: React.FC<WelcomeEmailProps> = ({
  userName = '',
  dashboardUrl = 'https://groombook.me/dashboard',
}) => {
  const currentYear = new Date().getFullYear();

  return (
    <Html>
      <Head />
      <Preview>Welcome to your GroomBook journey - transform your salon management today! 🚀</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header with Logo */}
          <Section style={logoContainer}>
            <Img
              src="https://groombook.me/logo.png"
              alt="GroomBook Logo"
              width="180"
              height="auto"
            />
          </Section>

          {/* Hero Section */}
          <Section style={section}>
            <Heading style={h1}>Welcome to Your GroomBook Journey! 🚀</Heading>
            <Text style={text}>
              Hello{userName ? ` ${userName}` : ''},
            </Text>
            <Text style={text}>
              We're thrilled to welcome you to the GroomBook family! You've just taken the first step toward 
              transforming how you manage your salon or service business. Your success is our priority, 
              and we're here to support you every step of the way.
            </Text>
          </Section>

          {/* Benefits Section */}
          <Section style={benefitsSection}>
            <Heading style={h2}>Why Thousands Choose GroomBook ✨</Heading>
            <Text style={benefitItem}>
              ⏱️ <strong>Save 5+ hours weekly</strong> on administration and booking management
            </Text>
            <Text style={benefitItem}>
              📱 <strong>Reduce no-shows by 80%</strong> with automated reminders and confirmations
            </Text>
            <Text style={benefitItem}>
              🌟 <strong>Increase client satisfaction</strong> with 24/7 online booking availability
            </Text>
            <Text style={benefitItem}>
              📊 <strong>Gain valuable insights</strong> into your business performance with detailed analytics
            </Text>
          </Section>

          {/* Quick Start Guide */}
          <Section style={section}>
            <Heading style={h2}>Your Quick-Start Guide 🔍</Heading>
            
            <Row style={stepRow}>
              <Column style={stepNumberColumn}>
                <Text style={stepNumber}>1</Text>
              </Column>
              <Column style={stepContentColumn}>
                <Text style={stepTitle}>Set Up Your Space Profile</Text>
                <Text style={stepDescription}>
                  Customize your business details, operating hours, and upload photos that showcase your space.
                </Text>
              </Column>
            </Row>
            
            <Row style={stepRow}>
              <Column style={stepNumberColumn}>
                <Text style={stepNumber}>2</Text>
              </Column>
              <Column style={stepContentColumn}>
                <Text style={stepTitle}>Add Your Services & Staff</Text>
                <Text style={stepDescription}>
                  Create your service menu with prices and durations, then invite your team members to join.
                </Text>
              </Column>
            </Row>
            
            <Row style={stepRow}>
              <Column style={stepNumberColumn}>
                <Text style={stepNumber}>3</Text>
              </Column>
              <Column style={stepContentColumn}>
                <Text style={stepTitle}>Accept Your First Booking</Text>
                <Text style={stepDescription}>
                  Share your booking link with clients or embed it on your website and social media.
                </Text>
              </Column>
            </Row>
          </Section>

          {/* CTA Button */}
          <Section style={ctaSection}>
            <Link href={dashboardUrl} style={button}>
              Get Started Now
            </Link>
          </Section>

          {/* Support Section */}
          <Section style={section}>
            <Heading style={h2}>We're Here to Help 🤝</Heading>
            <Text style={text}>
              Our dedicated support team is ready to assist you with any questions or guidance you need.
            </Text>
            <Text style={supportItem}>
              📧 Email us at: <Link href="mailto:<EMAIL>" style={link}><EMAIL></Link>
            </Text>
          </Section>
          
          <Hr style={hr} />
          
          {/* Personal Sign-off */}
          <Section style={section}>
            <Text style={signoffText}>
              We're excited to be part of your business growth journey!
            </Text>
            <Text style={signatureText}>
              Brian Mwangi<br />
              CEO & Founder, GroomBook
            </Text>
          </Section>
          
          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              GroomBook
            </Text>
            <Text style={footerText}>
              &copy; {currentYear} GroomBook. All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#F9F9F9',
  fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
};

const container = {
  maxWidth: '600px',
  margin: '0 auto',
  backgroundColor: '#FFFFFF',
  boxShadow: '0 4px 10px rgba(0,0,0,0.05)',
};

const logoContainer = {
  padding: '25px 0',
  backgroundColor: '#FFF8E0',
  textAlign: 'center' as const,
};

const section = {
  padding: '20px 30px',
};

const benefitsSection = {
  padding: '25px 30px',
  backgroundColor: '#FFF8E0',
  borderRadius: '8px',
  margin: '0 30px 20px',
};

const testimonialSection = {
  padding: '25px 30px',
  backgroundColor: '#F7F7F7',
  borderRadius: '8px',
  margin: '20px 30px',
};

const ctaSection = {
  padding: '10px 30px 30px',
  textAlign: 'center' as const,
};

const h1 = {
  color: '#333333',
  fontSize: '28px',
  fontWeight: 'bold',
  margin: '0 0 15px',
  lineHeight: '1.3',
};

const h2 = {
  color: '#333333',
  fontSize: '22px',
  fontWeight: 'bold',
  margin: '0 0 15px',
  lineHeight: '1.3',
};

const h3 = {
  color: '#333333',
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 15px',
};

const text = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  margin: '0 0 15px',
};

const signoffText = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  margin: '0 0 20px',
};

const signatureText = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  fontWeight: 'bold' as const,
  margin: '0',
};

const benefitItem = {
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  margin: '0 0 12px',
  paddingLeft: '10px',
};

const stepRow = {
  marginBottom: '20px',
};

const stepNumberColumn = {
  width: '40px',
  verticalAlign: 'top',
};

const stepContentColumn = {
  verticalAlign: 'top',
};

const stepNumber = {
  width: '30px',
  height: '30px',
  backgroundColor: '#F5B800',
  borderRadius: '50%',
  color: '#333333',
  textAlign: 'center' as const,
  lineHeight: '30px',
  fontWeight: 'bold',
  margin: '0',
};

const stepTitle = {
  fontSize: '18px',
  fontWeight: 'bold',
  color: '#333333',
  margin: '0 0 5px',
};

const stepDescription = {
  fontSize: '15px',
  lineHeight: '1.5',
  color: '#555555',
  margin: '0',
};

const button = {
  backgroundColor: '#F5B800',
  color: '#333333',
  padding: '14px 30px',
  textDecoration: 'none',
  borderRadius: '6px',
  fontWeight: 'bold',
  fontSize: '16px',
  display: 'inline-block',
};

const testimonialText = {
  fontSize: '16px',
  lineHeight: '1.6',
  color: '#333333',
  fontStyle: 'italic',
  margin: '0 0 15px',
};

const testimonialAuthor = {
  fontSize: '16px',
  fontWeight: 'bold',
  color: '#333333',
  margin: '0',
};

const supportItem = {
  fontSize: '15px',
  lineHeight: '1.6',
  color: '#333333',
  margin: '0 0 10px',
};

const link = {
  color: '#F5B800',
  textDecoration: 'none',
  fontWeight: 'bold',
};

const hr = {
  borderColor: '#E5E5E5',
  margin: '30px 0',
};

const footer = {
  padding: '20px 30px 30px',
  backgroundColor: '#333333',
  color: '#FFFFFF',
  textAlign: 'center' as const,
};

const footerText = {
  fontSize: '14px',
  color: '#FFFFFF',
  margin: '0 0 10px',
  textAlign: 'center' as const,
};

const footerLinks = {
  fontSize: '14px',
  color: '#FFFFFF',
  margin: '15px 0 0',
  textAlign: 'center' as const,
};

const footerLink = {
  color: '#AAAAAA',
  textDecoration: 'underline',
};

export default WelcomeEmail;
