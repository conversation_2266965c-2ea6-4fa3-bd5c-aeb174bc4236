/**
 * This file contains the Firestore security rules for analytics collections
 * Copy these rules to your firestore.rules file
 */

/*
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Existing rules...
    
    // Analytics collections - only owners and admins can read
    match /onboarding-analytics/{docId} {
      allow create: if request.auth != null;
      allow read: if request.auth != null && (
        request.auth.uid == resource.data.userId || 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'
      );
      allow delete: if false;
      allow update: if false;
    }
    
    match /space-analytics/{docId} {
      allow create: if request.auth != null;
      allow read: if request.auth != null && (
        request.auth.uid == resource.data.userId || 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'
      );
      allow delete: if false;
      allow update: if false;
    }
  }
}
*/

// Instructions:
// 1. Open your firestore.rules file
// 2. Add the rules for onboarding-analytics and space-analytics collections
// 3. Deploy the updated rules using Firebase CLI: firebase deploy --only firestore:rules
