import { render } from '@react-email/render';
import * as fs from 'fs';
import * as path from 'path';
import WelcomeEmail from '../emails/WelcomeEmail';
import { ReferralEmail } from '../emails/ReferralEmail';

/**
 * Script to preview email templates
 * Usage: npx ts-node src/scripts/preview-emails.ts
 */
async function previewEmails() {
  // Create output directory if it doesn't exist
  const outputDir = path.resolve(__dirname, '../../.email-previews');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  console.log('Generating email previews...');

  // Generate Welcome Email preview
  const welcomeHtml = await render(WelcomeEmail({ 
    userName: '<PERSON>', 
    dashboardUrl: 'https://groombook.me/dashboard' 
  }));

  // Save welcome email to file
  fs.writeFileSync(
    path.join(outputDir, 'welcome-email.html'),
    welcomeHtml
  );

  // Generate Referral Email preview
  const referralHtml = await render(ReferralEmail({
    senderName: '<PERSON>',
    referralCode: 'FRIEND123',
    referralUrl: 'https://groombook.me/referral/FRIEND123',
    customMessage: 'Hey there! I think you\'d love using GroomBook for your business. Join me!'
  }));

  // Save referral email to file
  fs.writeFileSync(
    path.join(outputDir, 'referral-email.html'),
    referralHtml
  );

  console.log(`Email previews generated in ${outputDir}`);
  console.log('You can open these HTML files in your browser to preview the emails.');
}

previewEmails().catch(console.error);
