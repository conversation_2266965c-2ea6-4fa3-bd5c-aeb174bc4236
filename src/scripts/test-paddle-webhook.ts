// Simple test script for Paddle webhook
// Using built-in fetch available in Node.js v18+
import crypto from 'crypto';

// Replace these with your actual values
const TEST_SECRET = process.env.PADDLE_WEBHOOK_SECRET || 'your_webhook_secret';
const WEBHOOK_URL = 'http://localhost:3000/api/paddle/webhook';
const USER_ID = 'test_user_123'; // Your test user ID

// Sample webhook payload for a subscription created event
const webhookPayload = {
  event_id: `evt_${Date.now()}`,
  event_type: 'subscription.created',
  occurred_at: new Date().toISOString(),
  data: {
    id: `sub_${Date.now()}`,
    customer_id: `cus_${Date.now()}`,
    address_id: `add_${Date.now()}`,
    business_id: null,
    custom_data: {
      userId: USER_ID
    },
    collection_mode: 'automatic',
    billing_cycle: {
      interval: 'month',
      frequency: 1
    },
    items: [
      {
        price: {
          id: 'pri_01jw0xnpq49mv9qfr6nzv2qkjn', // Pro monthly
          description: 'Pro Plan',
        },
        quantity: 1
      }
    ],
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    started_at: new Date().toISOString(),
    first_billed_at: new Date().toISOString(),
    next_billed_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    paused_at: null,
    canceled_at: null,
    current_billing_period: {
      starts_at: new Date().toISOString(),
      ends_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    }
  }
};

// Create signature
interface WebhookPayload {
    [key: string]: any;
}

const createSignature = (payload: string | WebhookPayload): string => {
    const stringPayload = typeof payload === 'string' ? payload : JSON.stringify(payload);
    return crypto.createHmac('sha256', TEST_SECRET).update(stringPayload).digest('hex');
};

// Send webhook
const sendWebhook = async () => {
  const stringPayload = JSON.stringify(webhookPayload);
  const signature = createSignature(stringPayload);
  
  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'paddle-signature': signature
      },
      body: stringPayload
    });
    
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', data);
  } catch (error) {
    console.error('Error sending webhook:', error);
  }
};

// Execute webhook test
sendWebhook();
