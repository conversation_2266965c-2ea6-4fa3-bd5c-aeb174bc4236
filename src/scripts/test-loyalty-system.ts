/**
 * Test script for the loyalty system
 * 
 * This script tests various aspects of the loyalty system:
 * 1. Adding points for appointments
 * 2. Adding points for referrals
 * 3. Adding points for birthdays
 * 4. Redeeming points for rewards
 * 5. Testing error recovery
 * 
 * This is intended to be run in a development environment only.
 */
import { db } from '@/utils/firebase';
import { doc, getDoc, setDoc, Timestamp } from 'firebase/firestore';
import { loyaltyService } from '@/services/loyaltyService';
import { birthdayService } from '@/services/birthdayService';
import { FailedOperationsQueue } from '@/utils/retryUtils';
import { Customer } from '@/services/types/models';

// Test constants
const TEST_CUSTOMER_ID = 'test-loyalty-customer-123';
const TEST_REFERRAL_CUSTOMER_ID = 'test-referred-customer-456';
const TEST_APPOINTMENT_ID = 'test-appointment-123';

// Helper functions
async function createTestCustomer(id: string, name: string, withBirthday = false): Promise<void> {
  const customerRef = doc(db, 'customers', id);
  const birthday = withBirthday ? new Date().toISOString().split('T')[0] : '1990-01-01';
  
  await setDoc(customerRef, {
    id,
    displayName: name,
    email: `${name.toLowerCase().replace(/\s/g, '.')}@test.com`,
    phoneNumber: '************',
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
    loyaltyPoints: {
      balance: 0,
      tier: 'bronze',
      enrolledAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      birthdate: birthday,
      referralCode: `REF-${id.slice(0, 6)}`
    }
  }, { merge: true });
  
  console.log(`Created test customer: ${name} (${id})`);
}

async function getCustomer(id: string): Promise<Customer | null> {
  const customerRef = doc(db, 'customers', id);
  const customerDoc = await getDoc(customerRef);
  
  if (!customerDoc.exists()) {
    return null;
  }
  
  return {
    id: customerDoc.id,
    ...customerDoc.data()
  } as Customer;
}

async function logCustomerInfo(id: string): Promise<void> {
  const customer = await getCustomer(id);
  
  if (!customer) {
    console.log(`Customer ${id} not found`);
    return;
  }
  
  console.log(`--- Customer Info: ${customer.displayName} ---`);
  console.log(`Points balance: ${customer.loyaltyPoints?.balance || 0}`);
  console.log(`Tier: ${customer.loyaltyPoints?.tier || 'none'}`);
  console.log(`Referral code: ${customer.loyaltyPoints?.referralCode || 'none'}`);
}

// Test functions
async function testSignupPoints(): Promise<void> {
  console.log('\n=== Testing Points for Signup ===');
  
  try {
    // Create a test customer with no existing loyalty data
    const signupCustomerId = `test-signup-${Date.now()}`;
    await setDoc(doc(db, 'customers', signupCustomerId), {
      id: signupCustomerId,
      displayName: 'New Test Customer',
      email: '<EMAIL>',
      phoneNumber: '************',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    
    // Award points for signup
    const result = await loyaltyService.awardPointsForSignup(signupCustomerId);
    console.log('Signup points awarded:', result.points);
    
    // Verify the points were added
    await logCustomerInfo(signupCustomerId);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error testing signup points:', error);
    return Promise.reject(error);
  }
}

async function testAppointmentPoints(): Promise<void> {
  console.log('\n=== Testing Points for Appointment ===');
  
  try {
    // First, log the initial state
    await logCustomerInfo(TEST_CUSTOMER_ID);
    
    // Award points for an appointment
    const appointmentAmount = 120; // $120 service
    const result = await loyaltyService.awardPointsForAppointment(
      TEST_CUSTOMER_ID,
      TEST_APPOINTMENT_ID,
      appointmentAmount
    );
    
    console.log('Appointment points awarded:', result.points);
    
    // Verify the points were added
    await logCustomerInfo(TEST_CUSTOMER_ID);
    
    // Try awarding points for the same appointment again (should fail gracefully)
    try {
      await loyaltyService.awardPointsForAppointment(
        TEST_CUSTOMER_ID,
        TEST_APPOINTMENT_ID,
        appointmentAmount
      );
      console.log('⚠️ Warning: Double-awarding points did not throw an error');
    } catch (error) {
      console.log('✅ Success: Prevented double-awarding points for the same appointment');
    }
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error testing appointment points:', error);
    return Promise.reject(error);
  }
}

async function testReferralPoints(): Promise<void> {
  console.log('\n=== Testing Points for Referral ===');
  
  try {
    // First, log the initial state
    await logCustomerInfo(TEST_CUSTOMER_ID);
    
    // Award points for a referral
    const result = await loyaltyService.awardPointsForReferral(
      TEST_CUSTOMER_ID,
      TEST_REFERRAL_CUSTOMER_ID
    );
    
    console.log('Referral points awarded:', result.points);
    
    // Verify the points were added
    await logCustomerInfo(TEST_CUSTOMER_ID);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error testing referral points:', error);
    return Promise.reject(error);
  }
}

async function testBirthdayPoints(): Promise<void> {
  console.log('\n=== Testing Points for Birthday ===');
  
  try {
    // Create a test customer with today's birthday
    const birthdayCustomerId = `test-birthday-${Date.now()}`;
    await createTestCustomer(birthdayCustomerId, 'Birthday Customer', true);
    
    // Process birthday rewards
    console.log('Finding and processing birthday celebrants...');
    const results = await birthdayService.processBirthdayRewards();
    
    console.log('Birthday processing results:');
    console.log(`- Success: ${results.success}`);
    console.log(`- Failure: ${results.failure}`);
    console.log(`- Customers: ${results.customers.join(', ')}`);
    
    // Verify the points were added
    await logCustomerInfo(birthdayCustomerId);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error testing birthday points:', error);
    return Promise.reject(error);
  }
}

async function testFailedOperationsRecovery(): Promise<void> {
  console.log('\n=== Testing Failed Operations Recovery ===');
  
  try {
    // Simulate a failed operation
    FailedOperationsQueue.getInstance().addFailedOperation(
      'awardPointsForSignup',
      { customerId: TEST_CUSTOMER_ID },
      'Simulated network error'
    );
    
    // Get failed operations
    const failedOps = FailedOperationsQueue.getInstance().getFailedOperations();
    console.log('Failed operations:', failedOps.length);
    
    // Try to recover
    const recoveryResults = await loyaltyService.recoverFailedOperations();
    console.log('Recovery results:', recoveryResults);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error testing failed operations recovery:', error);
    return Promise.reject(error);
  }
}

// Main test function
async function runLoyaltyTests(): Promise<void> {
  console.log('=== Starting Loyalty System Tests ===');
  
  try {
    // Setup test data
    await createTestCustomer(TEST_CUSTOMER_ID, 'Test Loyalty Customer');
    await createTestCustomer(TEST_REFERRAL_CUSTOMER_ID, 'Test Referred Customer');
    
    // Run tests
    await testSignupPoints();
    await testAppointmentPoints();
    await testReferralPoints();
    await testBirthdayPoints();
    await testFailedOperationsRecovery();
    
    console.log('\n=== All tests completed successfully ===');
  } catch (error) {
    console.error('Test suite failed:', error);
  }
}

// Only run if called directly (not imported)
if (require.main === module) {
  runLoyaltyTests()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Tests failed with error:', error);
      process.exit(1);
    });
}

export { runLoyaltyTests };
