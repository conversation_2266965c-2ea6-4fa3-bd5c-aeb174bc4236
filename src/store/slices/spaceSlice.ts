import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { Space } from '@/services/types/models'
import { spaceService } from '@/services/firestore'

interface SpaceState {
  currentSpace: Space | null
  spaces: Space[]
  loading: boolean
  error: string | null
}

const initialState: SpaceState = {
  currentSpace: null,
  spaces: [],
  loading: false,
  error: null
}

// Async thunks
export const fetchSpaces = createAsyncThunk(
  'space/fetchSpaces',
  async (userId: string) => {
    const spaces = await spaceService.getAllForUser(userId)
    return spaces
  }
)

export const updateSpaceDetails = createAsyncThunk(
  'space/updateSpaceDetails',
  async (spaceData: Partial<Space>) => {
    if (!spaceData.id) throw new Error('Space ID is required')
    const updatedSpace = await spaceService.update(spaceData.id, spaceData)
    return updatedSpace
  }
)

export const updateMpesaDetails = createAsyncThunk(
  'space/updateMpesaDetails',
  async ({ 
    spaceId, 
    mpesaDetails 
  }: { 
    spaceId: string, 
    mpesaDetails: {
      mpesaBusinessName?: string
      mpesaAccountType?: 'till' | 'paybill'
      mpesaShortcode?: string
      mpesaAccountNumber?: string
      mpesaPhoneNumber?: string
      mpesaAutoConfirm?: boolean
    } 
  }) => {
    // Use a type assertion or ensure these properties exist on the Space type
    const spaceUpdate = mpesaDetails as unknown as Partial<Space>
    const updatedSpace = await spaceService.update(spaceId, spaceUpdate)
    return updatedSpace
  }
)

export const deleteSpace = createAsyncThunk(
  'space/deleteSpace',
  async (spaceId: string) => {
    await spaceService.delete(spaceId)
    return spaceId
  }
)

const spaceSlice = createSlice({
  name: 'space',
  initialState,
  reducers: {
    setCurrentSpace: (state, action) => {
      state.currentSpace = action.payload
    },
    clearSpaceState: (state) => {
      state.currentSpace = null
      state.spaces = []
      state.loading = false
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch spaces
      .addCase(fetchSpaces.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchSpaces.fulfilled, (state, action) => {
        state.loading = false
        state.spaces = action.payload
        if (!state.currentSpace && action.payload.length > 0) {
          state.currentSpace = action.payload[0]
        }
      })
      .addCase(fetchSpaces.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch spaces'
      })
      // Update space details
      .addCase(updateSpaceDetails.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateSpaceDetails.fulfilled, (state, action) => {
        state.loading = false
        state.currentSpace = action.payload
        state.spaces = state.spaces.map(space =>
          space.id === action.payload.id ? action.payload : space
        )
      })
      .addCase(updateSpaceDetails.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to update space'
      })
      // Update M-PESA details
      .addCase(updateMpesaDetails.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateMpesaDetails.fulfilled, (state, action) => {
        state.loading = false
        state.currentSpace = action.payload
        state.spaces = state.spaces.map(space =>
          space.id === action.payload.id ? action.payload : space
        )
      })
      .addCase(updateMpesaDetails.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to update M-PESA details'
      })
      // Delete space
      .addCase(deleteSpace.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteSpace.fulfilled, (state, action) => {
        state.loading = false
        state.spaces = state.spaces.filter(space => space.id !== action.payload)
        if (state.currentSpace?.id === action.payload) {
          state.currentSpace = null
        }
      })
      .addCase(deleteSpace.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to delete space'
      })
  }
})

// Selectors
export const selectCurrentSpace = (state: any) => state.space.currentSpace
export const selectSpaces = (state: any) => state.space.spaces
export const selectSpaceLoading = (state: any) => state.space.loading
export const selectSpaceError = (state: any) => state.space.error

export const { setCurrentSpace, clearSpaceState } = spaceSlice.actions
export default spaceSlice.reducer
