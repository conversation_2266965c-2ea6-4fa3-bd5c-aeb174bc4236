import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { db } from '@/utils/firebase'
import { collection, getDocs, orderBy, query } from 'firebase/firestore'

export interface Campaign {
  id: string
  campaignName: string
  message: string
  recipients: string[]
  sentAt: string
  status?: string
}

export const fetchCampaigns = createAsyncThunk('campaign/fetchCampaigns', async () => {
  const q = query(collection(db, 'campaigns'), orderBy('sentAt', 'desc'))
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Campaign[]
})

interface CampaignState {
  campaigns: Campaign[]
  loading: boolean
  error: string | null
}

const initialState: CampaignState = {
  campaigns: [],
  loading: false,
  error: null,
}

const campaignSlice = createSlice({
  name: 'campaign',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchCampaigns.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchCampaigns.fulfilled, (state, action) => {
        state.loading = false
        state.campaigns = action.payload
      })
      .addCase(fetchCampaigns.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch campaigns'
      })
  },
})

export default campaignSlice.reducer 