import { db, auth, google<PERSON>rovider } from '@/utils/firebase';
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  updateProfile,
  UserCredential,
  signOut,
  sendPasswordResetEmail,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  GoogleAuthProvider
} from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { getAuthErrorMessage } from '@/utils/auth-errors';
import { type User, type AuthState } from '@/types/auth';



const initialState: AuthState = {
  user: null,
  loading: false,
  authLoading: true, // Start with true since we need to check auth state
  error: null,
};

interface RegisterData {
  email: string;
  password: string;
  name: string;
  planDetails?: {
    plan: string;
    billingCycle: string;
    price: string;
  };
}

interface LoginData {
  email: string;
  password: string;
}

export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async (data: RegisterData, { rejectWithValue }) => {
    try {
      // Create auth user
      const userCredential: UserCredential = await createUserWithEmailAndPassword(
        auth,
        data.email,
        data.password
      );

      // Update profile with name
      await updateProfile(userCredential.user, {
        displayName: data.name
      });

      // Create user document in Firestore
      const userData: User = {
        uid: userCredential.user.uid,
        email: userCredential.user.email,
        displayName: data.name,
        photoURL: userCredential.user.photoURL,
        role: 'owner', // Default role for new registrations
        hasCompletedOnboarding: false, // Initialize onboarding status
        businessType: null,
        referralSource: null
      };
      
      // Add plan details if provided
      if (data.planDetails) {
        userData.planDetails = data.planDetails;
        
        // Set subscription tier based on plan selection
        if (data.planDetails.plan === 'free') {
          // For free plan, set the tier immediately - no billing required
          userData.subscriptionTier = 'free';
          userData.subscriptionStatus = 'active';
        }
        // For paid plans, subscription tier will be set after successful payment
      }

      await setDoc(doc(db, 'users', userCredential.user.uid), userData);

      return userData;
    } catch (error) {
      return rejectWithValue(getAuthErrorMessage(error));
    }
  }
);

export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (data: LoginData, { rejectWithValue }) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        data.email,
        data.password
      );

      // Get user data from Firestore to include onboarding status
      const userDocRef = doc(db, 'users', userCredential.user.uid);
      const userDocSnap = await getDoc(userDocRef);
      
      if (!userDocSnap.exists()) {
        return rejectWithValue('User data not found');
      }

      const userData = userDocSnap.data();
      return {
        uid: userCredential.user.uid,
        email: userCredential.user.email,
        displayName: userCredential.user.displayName,
        photoURL: userCredential.user.photoURL,
        hasCompletedOnboarding: userData.hasCompletedOnboarding || false,
        businessType: userData.businessType || null,
        referralSource: userData.referralSource || null,
      };
    } catch (error) {
      return rejectWithValue(getAuthErrorMessage(error));
    }
  }
);

export const loginWithGoogle = createAsyncThunk(
  'auth/loginWithGoogle',
  async (options: { planDetails?: User['planDetails'] } = {}, { rejectWithValue }) => {
    try {
      
      
      // Optimized popup handling
      let result;
      try {
        // Set a timeout for the popup to prevent hanging
        const popupPromise = signInWithPopup(auth, googleProvider);
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Popup timeout')), 30000)
        );

        result = await Promise.race([popupPromise, timeoutPromise]);
      } catch (error: any) {
        // Simplified error handling
        if (error.code === 'auth/popup-closed-by-user') {
          return rejectWithValue("Sign-in popup was closed. Please try again.");
        } else if (error.code === 'auth/popup-blocked') {
          return rejectWithValue("Sign-in popup was blocked. Please enable popups for this site and try again.");
        } else if (error.message === 'Popup timeout') {
          return rejectWithValue("Sign-in took too long. Please try again.");
        }

        return rejectWithValue(getAuthErrorMessage(error));
      }
      
      // This gives you a Google Access Token
      const credential = GoogleAuthProvider.credentialFromResult(result);
      
      const user = result.user;
      
      // Check if the user already exists in Firestore
      const userDocRef = doc(db, 'users', user.uid);
      const userDocSnap = await getDoc(userDocRef);
      
      // Check if user is new or existing
      let isNewUser = !userDocSnap.exists();
      
      // If user doesn't exist in Firestore, create a new user document
      if (isNewUser) {
        const userData: User = {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL,
          role: 'owner', // Default role for new registrations
          hasCompletedOnboarding: false, // For new Google users, they should go through onboarding
          businessType: null,
          referralSource: null,
          ...(options.planDetails && { planDetails: options.planDetails })
        };
        
        // Set subscription tier for free plan
        if (options.planDetails?.plan === 'free') {
          userData.subscriptionTier = 'free';
          userData.subscriptionStatus = 'active';
        }
        
        await setDoc(userDocRef, userData);
      }
      
      // Get user data from Firestore for consistent shape with other login methods
      const updatedUserDoc = await getDoc(userDocRef);
      
      if (!updatedUserDoc.exists()) {
        throw new Error("Failed to create or fetch user document");
      }
      
      // Get the data from the document
      const docData = updatedUserDoc.data();
      
      const userData = {
        uid: user.uid,
        email: user.email,
        displayName: docData.displayName || user.displayName,
        photoURL: docData.photoURL || user.photoURL,
        role: docData.role || 'owner',
        // For Google login, we need to explicitly check if onboarding is completed
        hasCompletedOnboarding: docData.hasCompletedOnboarding === true,
        businessType: docData.businessType || null,
        referralSource: docData.referralSource || null,
        ...(docData.planDetails && { planDetails: docData.planDetails })
      } as User;
      
     
      return userData;
    } catch (error) {
      return rejectWithValue(getAuthErrorMessage(error));
    }
  }
);

export const loginWithGoogleRedirect = createAsyncThunk(
  'auth/loginWithGoogleRedirect',
  async (options: { planDetails?: User['planDetails'] } = {}, { rejectWithValue }) => {
    try {
      console.log("Starting Google sign-in process with redirect...");
      
      // Use redirect method instead of popup
      await signInWithRedirect(auth, googleProvider);
      
      // Note: The rest of the logic happens in AuthStateListener after redirect
      return null;
    } catch (error) {
      console.error("Google redirect error:", error);
      return rejectWithValue(getAuthErrorMessage(error));
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      await signOut(auth);
      return null;
    } catch (error) {
      return rejectWithValue(getAuthErrorMessage(error));
    }
  }
);

export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async (email: string, { rejectWithValue }) => {
    try {
      await sendPasswordResetEmail(auth, email);
      return { success: true };
    } catch (error) {
      return rejectWithValue(getAuthErrorMessage(error));
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      state.error = null;
    },
    setAuthLoading: (state, action: PayloadAction<boolean>) => {
      state.authLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Register cases
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Login cases
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Google login cases
      .addCase(loginWithGoogle.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginWithGoogle.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.error = null;
      })
      .addCase(loginWithGoogle.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Google redirect login cases
      .addCase(loginWithGoogleRedirect.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginWithGoogleRedirect.fulfilled, (state) => {
        // This is just setting loading to false, actual auth happens after redirect
        state.loading = false;
      })
      .addCase(loginWithGoogleRedirect.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Logout cases
      .addCase(logoutUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.loading = false;
        state.user = null;
        state.error = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Reset password cases
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setUser, setAuthLoading, setError, clearError } = authSlice.actions;
export default authSlice.reducer;