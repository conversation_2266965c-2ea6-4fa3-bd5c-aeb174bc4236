import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { Customer } from '@/services/types/models'
import { db } from '@/utils/firebase'
import { collection, getDocs } from 'firebase/firestore'

export const fetchCustomers = createAsyncThunk('customer/fetchCustomers', async () => {
  const snapshot = await getDocs(collection(db, 'customers'))
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Customer[]
})

interface CustomerState {
  customers: Customer[]
  loading: boolean
  error: string | null
}

const initialState: CustomerState = {
  customers: [],
  loading: false,
  error: null,
}

const customerSlice = createSlice({
  name: 'customer',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchCustomers.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchCustomers.fulfilled, (state, action) => {
        state.loading = false
        state.customers = action.payload
      })
      .addCase(fetchCustomers.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch customers'
      })
  },
})

export default customerSlice.reducer 