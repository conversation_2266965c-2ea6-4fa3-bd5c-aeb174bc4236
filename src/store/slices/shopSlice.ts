import { shopService } from '@/services/firestore';
import { Shop } from '@/services/types/models';
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';


interface ShopState {
  data: Shop | null;
  loading: boolean;
  error: string | null;
}

const initialState: ShopState = {
  data: null,
  loading: false,
  error: null,
};

export const fetchShopSettings = createAsyncThunk(
  'shop/fetchSettings',
  async () => {
    const shopData = await shopService.get();
    return shopData;
  }
);

export const updateShopSettings = createAsyncThunk(
  'shop/updateSettings',
  async ({ id, data }: { id: string; data: Partial<Shop> }) => {
    await shopService.update(id, data);
    return data;
  }
);

const shopSlice = createSlice({
  name: 'shop',
  initialState,
  reducers: {
    setShop: (state, action: PayloadAction<Shop>) => {
      state.data = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchShopSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchShopSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchShopSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch shop settings';
      })
      .addCase(updateShopSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateShopSettings.fulfilled, (state, action) => {
        state.loading = false;
        if (state.data) {
          state.data = { ...state.data, ...action.payload };
        }
      })
      .addCase(updateShopSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update shop settings';
      });
  },
});

export const { setShop } = shopSlice.actions;
export default shopSlice.reducer; 