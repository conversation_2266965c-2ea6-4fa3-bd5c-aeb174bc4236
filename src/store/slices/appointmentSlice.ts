import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { appointmentValidationService } from '@/services/appointmentValidation'
import { appointmentService, staffService, serviceService, customerService } from '@/services/firestore'
import { enhancedAppointmentService, AppointmentWithDetails } from '@/services/appointmentService'
import { Appointment } from '@/services/types/models'
import { formatToInternational } from '@/utils/phoneUtils'

interface AppointmentState {
  appointments: Appointment[]
  appointmentsWithDetails: AppointmentWithDetails[]
  loading: boolean
  error: string | null
}

const initialState: AppointmentState = {
  appointments: [],
  appointmentsWithDetails: [],
  loading: false,
  error: null
}

export const createAppointment = createAsyncThunk(
  'appointment/create',
  async ({
    startTime,
    serviceId,
    staffId,
    customerId,
    spaceId,
    businessHours
  }: {
    startTime: Date
    serviceId: string
    staffId: string
    customerId: string
    spaceId: string
    businessHours?: any[] // Using any to avoid circular dependency
  }, { rejectWithValue }) => {
    try {
      // Fetch required data
      const [staff, service, staffAppointments] = await Promise.all([
        staffService.getById(staffId),
        serviceService.getById(serviceId),
        appointmentService.getStaffAppointments(staffId, startTime)
      ])

      if (!staff || !service) {
        return rejectWithValue('Staff or service not found')
      }

      // Validate appointment
      const validation = await appointmentValidationService.validateAppointment(
        startTime,
        serviceId,
        staffId,
        staff,
        {
          staffId,
          appointments: staffAppointments.map(apt => ({
            startTime: new Date(apt.startTime),
            endTime: new Date(apt.endTime)
          }))
        },
        service,
        businessHours
      )

      if (!validation.valid) {
        return rejectWithValue(validation.error || 'Invalid appointment')
      }

      // Calculate end time
      const endTime = new Date(startTime.getTime() + service.duration * 60000)

      // Create appointment
      const appointment = await appointmentService.create({
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        serviceId,
        staffId,
        customerId,
        spaceId,
        status: 'scheduled',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })

      return appointment
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create appointment')
    }
  }
)

export const fetchAppointments = createAsyncThunk(
  'appointment/fetchAll',
  async (spaceId: string) => {
    const appointments = await appointmentService.getBySpace(spaceId)
    return appointments
  }
)

export const fetchAppointmentsWithDetails = createAsyncThunk(
  'appointment/fetchWithDetails',
  async (spaceId: string, { rejectWithValue }) => {
    try {
      const appointmentsWithDetails = await enhancedAppointmentService.getAppointmentsWithDetails(spaceId)
      return appointmentsWithDetails
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch appointment details')
    }
  }
)

export const cancelAppointment = createAsyncThunk(
  'appointment/cancel',
  async ({ appointmentId, spaceId }: { appointmentId: string, spaceId: string }, { rejectWithValue }) => {
    try {
      // Update the appointment status to cancelled
      await appointmentService.update(appointmentId, {
        status: 'cancelled',
        updatedAt: new Date().toISOString()
      })

      // Fetch updated appointments
      const appointmentsWithDetails = await enhancedAppointmentService.getAppointmentsWithDetails(spaceId)
      return appointmentsWithDetails
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to cancel appointment')
    }
  }
)

export const createPublicAppointment = createAsyncThunk(
  'appointment/createPublic',
  async ({
    customerName,
    customerEmail,
    customerPhone,
    serviceId,
    staffId,
    spaceId,
    startTime,
    notes,
    businessHours
  }: {
    customerName: string
    customerEmail: string
    customerPhone: string
    serviceId: string
    staffId: string
    spaceId: string
    startTime: Date | string
    notes?: string
    businessHours?: any[] // Using any to avoid circular dependency
  }, { rejectWithValue }) => {
    try {
      // Convert startTime to Date if it's a string
      const appointmentStartTime = typeof startTime === 'string' ? new Date(startTime) : startTime

      // Fetch required data
      const [staff, service, staffAppointments] = await Promise.all([
        staffService.getById(staffId),
        serviceService.getById(serviceId),
        appointmentService.getStaffAppointments(staffId, appointmentStartTime)
      ])

      if (!staff || !service) {
        return rejectWithValue('Staff or service not found')
      }

      // Validate appointment
      const validation = await appointmentValidationService.validateAppointment(
        appointmentStartTime,
        serviceId,
        staffId,
        staff,
        {
          staffId,
          appointments: staffAppointments.map(apt => ({
            startTime: new Date(apt.startTime),
            endTime: new Date(apt.endTime)
          }))
        },
        service,
        businessHours
      )

      if (!validation.valid) {
        return rejectWithValue(validation.error || 'Invalid appointment')
      }

      // Calculate end time
      const endTime = new Date(appointmentStartTime.getTime() + service.duration * 60000)

      // Format phone number to international format to ensure consistency
      const formattedPhone = formatToInternational(customerPhone)
      
      // Try to find existing customer by email or phone
      let customer = null
      
      // First check by phone number as it's more unique
      if (formattedPhone) {
        customer = await customerService.getByPhone(formattedPhone)
      }
      
      // If not found by phone, try by email
      if (!customer && customerEmail) {
        const emailCustomers = await customerService.queryCustomers([
          { field: 'email', operator: '==', value: customerEmail.toLowerCase().trim() }
        ])
        if (emailCustomers && emailCustomers.length > 0) {
          customer = emailCustomers[0]
        }
      }

      let customerId
      
      // If customer doesn't exist, create a new one
      if (!customer) {
        // Create new customer record
        customerId = await customerService.create({
          email: customerEmail.toLowerCase().trim(),
          phoneNumber: formattedPhone,
          displayName: customerName,
          role: 'customer',
          visits: 1,
          lastVisit: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          loyaltyPoints: {
            balance: 0,
            tier: 'bronze',
            updatedAt: new Date().toISOString()
          }
        })
        
        // Award signup points to new customer via loyalty service
        try {
          const { loyaltyService } = await import('@/services/loyaltyService')
          await loyaltyService.awardPointsForSignup(customerId)
          console.log(`Awarded signup loyalty points to new customer ${customerId}`)
        } catch (loyaltyError) {
          console.error('Error awarding signup loyalty points:', loyaltyError)
          // Don't block the appointment creation if loyalty points fail
        }
      } else {
        // Use existing customer
        customerId = customer.id
        
        // Update visit count and lastVisit date
        await customerService.update(customerId, {
          visits: (customer.visits || 0) + 1,
          lastVisit: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
      }

      // Create the appointment
      const appointment = await appointmentService.create({
        startTime: appointmentStartTime.toISOString(),
        endTime: endTime.toISOString(),
        serviceId,
        staffId,
        customerId,
        spaceId,
        status: 'scheduled',
        notes: notes || `Booking by ${customerName} (${customerEmail}, ${formattedPhone})`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })

      return appointment
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create appointment')
    }
  }
)

export const rescheduleAppointment = createAsyncThunk(
  'appointment/reschedule',
  async ({ 
    appointmentId, 
    newStartTime,
    spaceId 
  }: { 
    appointmentId: string
    newStartTime: Date
    spaceId: string 
  }, { rejectWithValue }) => {
    try {
      // Get the original appointment
      const appointment = await appointmentService.getById(appointmentId)
      if (!appointment) {
        return rejectWithValue('Appointment not found')
      }

      // Get the service to calculate new end time
      const service = await serviceService.getById(appointment.serviceId)
      if (!service) {
        return rejectWithValue('Service not found')
      }

      // Calculate new end time based on service duration
      const newEndTime = new Date(newStartTime.getTime() + service.duration * 60000)

      // Get staff appointments to check availability
      const staffAppointments = await appointmentService.getStaffAppointments(
        appointment.staffId,
        newStartTime
      )

      // Validate the new time
      const staff = await staffService.getById(appointment.staffId);
      if (!staff) {
        return rejectWithValue('Staff not found');
      }

      const validation = await appointmentValidationService.validateAppointment(
        newStartTime,
        appointment.serviceId,
        appointment.staffId,
        staff,
        {
          staffId: appointment.staffId,
          appointments: staffAppointments
            .filter(apt => apt.id !== appointmentId) // Exclude current appointment
            .map(apt => ({
              startTime: new Date(apt.startTime),
              endTime: new Date(apt.endTime)
            }))
        },
        service
      )

      if (!validation.valid) {
        return rejectWithValue(validation.error || 'Invalid appointment time')
      }

      // Update the appointment
      await appointmentService.update(appointmentId, {
        startTime: newStartTime.toISOString(),
        endTime: newEndTime.toISOString(),
        updatedAt: new Date().toISOString()
      })

      // Fetch updated appointments
      const appointmentsWithDetails = await enhancedAppointmentService.getAppointmentsWithDetails(spaceId)
      return appointmentsWithDetails
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to reschedule appointment')
    }
  }
)

export const updateAppointmentStatus = createAsyncThunk(
  'appointment/updateStatus',
  async ({ 
    appointmentId, 
    status, 
    spaceId
  }: { 
    appointmentId: string
    status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no-show'
    spaceId: string 
  }, { rejectWithValue }) => {
    try {
      // Get the appointment details before updating
      const appointment = await appointmentService.getById(appointmentId)
      if (!appointment) {
        return rejectWithValue('Appointment not found')
      }

      // Update appointment status
      await appointmentService.update(appointmentId, {
        status,
        updatedAt: new Date().toISOString()
      })

      // Award loyalty points if the appointment is being marked as completed
      if (status === 'completed') {
        try {
          // Get service details to calculate points based on price
          const service = await serviceService.getById(appointment.serviceId)
          if (service && appointment.customerId !== 'guest-customer') {
            const { loyaltyService } = await import('@/services/loyaltyService')
            await loyaltyService.awardPointsForAppointment(
              appointment.customerId,
              appointmentId,
              service.price
            )
          }
        } catch (loyaltyError) {
          console.error('Error awarding loyalty points:', loyaltyError)
          // Continue with appointment status update even if loyalty points fail
        }
      }

      // Fetch updated appointments
      const appointmentsWithDetails = await enhancedAppointmentService.getAppointmentsWithDetails(spaceId)
      return appointmentsWithDetails
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update appointment status')
    }
  }
)

const appointmentSlice = createSlice({
  name: 'appointment',
  initialState,
  reducers: {
    clearAppointmentError: (state) => {
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Create appointment
      .addCase(createAppointment.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createAppointment.fulfilled, (state, action) => {
        state.loading = false
        state.appointments.push(action.payload)
      })
      .addCase(createAppointment.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string || 'Failed to create appointment'
      })
      // Fetch appointments
      .addCase(fetchAppointments.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAppointments.fulfilled, (state, action) => {
        state.loading = false
        state.appointments = action.payload
      })
      .addCase(fetchAppointments.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch appointments'
      })
      // Fetch appointments with details
      .addCase(fetchAppointmentsWithDetails.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchAppointmentsWithDetails.fulfilled, (state, action) => {
        state.loading = false
        state.appointmentsWithDetails = action.payload
      })
      .addCase(fetchAppointmentsWithDetails.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string || 'Failed to fetch appointment details'
      })
      // Cancel appointment
      .addCase(cancelAppointment.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(cancelAppointment.fulfilled, (state, action) => {
        state.loading = false
        state.appointmentsWithDetails = action.payload
      })
      .addCase(cancelAppointment.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string || 'Failed to cancel appointment'
      })
      // Create public appointment
      .addCase(createPublicAppointment.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createPublicAppointment.fulfilled, (state, action) => {
        state.loading = false
        state.appointments.push(action.payload)
      })
      .addCase(createPublicAppointment.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string || 'Failed to create public appointment'
      })
      // Reschedule appointment
      .addCase(rescheduleAppointment.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(rescheduleAppointment.fulfilled, (state, action) => {
        state.loading = false
        state.appointmentsWithDetails = action.payload
      })
      .addCase(rescheduleAppointment.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string || 'Failed to reschedule appointment'
      })
      // Update appointment status
      .addCase(updateAppointmentStatus.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateAppointmentStatus.fulfilled, (state, action) => {
        state.loading = false
        state.appointmentsWithDetails = action.payload
      })
      .addCase(updateAppointmentStatus.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string || 'Failed to update appointment status'
      })
  }
})

export const { clearAppointmentError } = appointmentSlice.actions
export default appointmentSlice.reducer