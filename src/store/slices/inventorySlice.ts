import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { db } from '@/utils/firebase'
import { collection, getDocs } from 'firebase/firestore'

export interface InventoryItem {
  id: string
  name: string
  type: string
  quantity: number
  thresholdLow: number
  thresholdCritical: number
  status?: 'ok' | 'low' | 'critical'
  lastUpdated: string
  spaceId: string
}

export const fetchInventory = createAsyncThunk('inventory/fetchInventory', async (spaceId: string) => {
  const snapshot = await getDocs(collection(db, 'inventory'))
  // Optionally filter by spaceId if needed
  return snapshot.docs
    .map(doc => ({ id: doc.id, ...doc.data() } as InventoryItem))
    .filter(item => !spaceId || item.spaceId === spaceId)
})

interface InventoryState {
  items: InventoryItem[]
  loading: boolean
  error: string | null
}

const initialState: InventoryState = {
  items: [],
  loading: false,
  error: null,
}

const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchInventory.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchInventory.fulfilled, (state, action) => {
        state.loading = false
        state.items = action.payload
      })
      .addCase(fetchInventory.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch inventory'
      })
  },
})

export default inventorySlice.reducer 