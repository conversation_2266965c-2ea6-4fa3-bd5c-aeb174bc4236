import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Staff } from '@/services/types/models';
import { staffService } from '@/services/firestore';
import { RootState } from '../../store';

interface StaffState {
  staff: Staff[];
  currentStaff: Staff | null;
  selectedStaff: Staff | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: StaffState = {
  staff: [],
  currentStaff: null,
  selectedStaff: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchAllStaff = createAsyncThunk(
  'staff/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      const staff = await staffService.getAll();
      return staff;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const createStaff = createAsyncThunk(
  'staff/create',
  async (staffData: Omit<Staff, 'id'>, { rejectWithValue }) => {
    try {
      await staffService.create(staffData);
      // Fetch all staff to ensure we have the latest data
      const staff = await staffService.getAll();
      return staff;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateStaff = createAsyncThunk(
  'staff/update',
  async ({ id, data }: { id: string; data: Partial<Staff> }, { rejectWithValue }) => {
    try {
      await staffService.update(id, data);
      // Fetch all staff to ensure we have the latest data
      const staff = await staffService.getAll();
      return staff;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteStaff = createAsyncThunk(
  'staff/delete',
  async (id: string, { rejectWithValue }) => {
    try {
      await staffService.delete(id);
      // Fetch all staff to ensure we have the latest data
      const staff = await staffService.getAll();
      return staff;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// Fetch individual staff data
export const fetchStaffData = createAsyncThunk(
  'staff/fetchData',
  async (staffId: string, { rejectWithValue }) => {
    try {
      const staffData = await staffService.getById(staffId);
      if (!staffData) {
        throw new Error('Staff not found');
      }
      return staffData;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const staffSlice = createSlice({
  name: 'staff',
  initialState,
  reducers: {
    setSelectedStaff: (state, action: PayloadAction<Staff | null>) => {
      state.selectedStaff = action.payload;
    },
    clearStaffError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchAllStaff
      .addCase(fetchAllStaff.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllStaff.fulfilled, (state, action) => {
        state.isLoading = false;
        state.staff = action.payload;
      })
      .addCase(fetchAllStaff.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // createStaff
      .addCase(createStaff.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createStaff.fulfilled, (state, action) => {
        state.isLoading = false;
        state.staff = action.payload;
      })
      .addCase(createStaff.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // updateStaff
      .addCase(updateStaff.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateStaff.fulfilled, (state, action) => {
        state.isLoading = false;
        state.staff = action.payload;
        state.selectedStaff = null; // Clear selected staff after update
      })
      .addCase(updateStaff.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // deleteStaff
      .addCase(deleteStaff.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteStaff.fulfilled, (state, action) => {
        state.isLoading = false;
        state.staff = action.payload;
        state.selectedStaff = null; // Clear selected staff after delete
      })
      .addCase(deleteStaff.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // fetchStaffData
      .addCase(fetchStaffData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchStaffData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentStaff = action.payload;
        state.error = null;
      })
      .addCase(fetchStaffData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedStaff, clearStaffError } = staffSlice.actions;

// Selectors
export const selectAllStaff = (state: RootState) => state.staff.staff;
export const selectCurrentStaff = (state: RootState) => state.staff.currentStaff;
export const selectSelectedStaff = (state: RootState) => state.staff.selectedStaff;
export const selectStaffLoading = (state: RootState) => state.staff.isLoading;
export const selectStaffError = (state: RootState) => state.staff.error;

export default staffSlice.reducer;
