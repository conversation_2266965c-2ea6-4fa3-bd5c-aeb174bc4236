import { configureStore } from '@reduxjs/toolkit';
import shopReducer from './slices/shopSlice';
import authReducer from './slices/authSlice';
import spaceReducer from './slices/spaceSlice';
import staffReducer from './slices/staffSlice';
import appointmentReducer from './slices/appointmentSlice';
import campaignReducer from './slices/campaignSlice';
import inventoryReducer from './slices/inventorySlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    space: spaceReducer, // New reducer for spaces
    appointments: appointmentReducer,
    campaigns: campaignReducer,
    inventory: inventoryReducer,
    shop: shopReducer,
    staff: staffReducer
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

