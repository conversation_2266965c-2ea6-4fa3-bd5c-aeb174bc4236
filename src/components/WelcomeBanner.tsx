'use client';

import { useEffect, useState } from 'react';
import { useAppSelector } from '@/store/hooks';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Check, X } from 'lucide-react';

export function WelcomeBanner() {
  const { user } = useAppSelector((state) => state.auth);
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    if (user) {
      // Check if we've shown the banner before
      const bannerShown = localStorage.getItem(`welcome_banner_${user.uid}`);
      
      if (!bannerShown) {
        setShowBanner(true);
        // Mark as shown
        localStorage.setItem(`welcome_banner_${user.uid}`, 'true');
      }
    }
  }, [user]);

  const handleDismiss = () => {
    // Mark the banner as seen in local storage
    if (user?.uid) {
      localStorage.setItem(`welcome_banner_seen_${user.uid}`, 'true');
    }
    setShowBanner(false);
  };

  if (!showBanner || !user) {
    return null;
  }
  
  return (
    <Card className="relative overflow-hidden bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 p-6 mb-6">
      <button 
        onClick={handleDismiss} 
        className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
        aria-label="Dismiss welcome banner"
      >
        <X size={18} />
      </button>
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold mb-1">Welcome to GroomBook! 🎉</h3>
          <p className="text-gray-700 mb-2">
            Your account has been successfully created. Let's get started by setting up your business!
          </p>
        </div>
      </div>
    </Card>
  );
}
