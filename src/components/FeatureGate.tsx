import React from 'react';
import { useRouter } from 'next/navigation';
import { useFeatureAccess, RestrictedFeature } from '@/hooks/use-feature-access';

interface FeatureGateProps {
  /**
   * The feature to gate access to
   */
  feature: RestrictedFeature;
  
  /**
   * The children to render if user has access to the feature
   */
  children: React.ReactNode;
  
  /**
   * Custom title for the feature notice
   */
  title?: string;
  
  /**
   * Custom description for the feature notice
   */
  description?: string;
  
  /**
   * Alternative component to render when access is restricted
   */
  fallback?: React.ReactNode;
  
  /**
   * Whether to show the beta notice
   */
  showBetaNotice?: boolean;

  benefits?: string[];
}


/**
 * Component that manages feature access based on subscription plan.
 * Restricts access to paid features for users on the free plan.
 */
export function FeatureGate({
  feature,
  children,
  title,
  description,
  fallback,
  showBetaNotice = false,
  benefits
}: FeatureGateProps) {
  const { hasAccess, isLoading, currentPlan } = useFeatureAccess(feature);
  const router = useRouter();
  
  if (isLoading) {
    return (
      <div className="animate-pulse flex flex-col space-y-4 p-6 bg-muted/50 rounded-lg">
        <div className="h-5 w-2/3 bg-muted rounded"></div>
        <div className="h-4 w-4/5 bg-muted rounded"></div>
        <div className="h-4 w-3/5 bg-muted rounded"></div>
      </div>
    );
  }

  const getRequiredPlanName = () => {
    if (feature.startsWith('enterprise_')) return 'Enterprise';
    if (feature.startsWith('advanced_')) return 'Pro';
    return 'Pro';
  };

  const getFeatureName = () => {
    return title || feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // If user has access, render the feature
  if (hasAccess) {
    return (
      <div>
        {showBetaNotice && (
          <div className="m-2 p-2 bg-violet-100 text-violet-800 rounded-md text-sm flex items-center gap-2">
            <span className="text-violet-500">✨</span>
            <span>
              {title || 'Beta Feature'}: {description || `Currently in beta - enjoy this feature while we fine-tune it!`}
            </span>
          </div>
        )}
        {children}
      </div>
    );
  }

  // If user doesn't have access, show upgrade prompt or fallback
  if (fallback) {
    return <>{fallback}</>;
  }

  const requiredPlan = getRequiredPlanName();
  const featureName = getFeatureName();

  return (
    <div className="p-6 bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 rounded-lg">
      <div className="text-center space-y-4">
        <div className="mx-auto w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
          <span className="text-white text-xl">🚀</span>
        </div>
        
        <div>
          <h3 className="text-lg font-semibold text-slate-900 mb-2">
            {featureName} - {requiredPlan} Feature
          </h3>
          <p className="text-slate-600 text-sm mb-4">
            {description || `Upgrade to ${requiredPlan} to unlock ${featureName.toLowerCase()} and grow your business.`}
          </p>
        </div>

        {benefits && benefits.length > 0 && (
          <div className="text-left bg-white p-4 rounded-lg border">
            <p className="font-medium text-slate-900 mb-2">What you'll get:</p>
            <ul className="space-y-1">
              {benefits.map((benefit, index) => (
                <li key={index} className="text-sm text-slate-600 flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  {benefit}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={() => router.push('/dashboard/billing')}
            className="px-4 py-2 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
          >
            Upgrade to {requiredPlan}
          </button>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-white hover:bg-slate-50 text-slate-600 border border-slate-300 rounded-lg font-medium transition-colors"
          >
            Maybe Later
          </button>
        </div>
        
        <p className="text-xs text-slate-500">
          Currently on {currentPlan ? currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1) : 'Free'} plan
        </p>
      </div>
    </div>
  );
}
