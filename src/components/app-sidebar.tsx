import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/utils/auth";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { logoutUser } from "@/store/slices/authSlice";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "sonner";
import {
  LayoutDashboard,
  Users,
  Scissors,
  Calendar,
  Settings,
  Store,
  ChevronDown,
  ChevronUp,
  User2,
  Plus,
  Edit,
  Clock,
  Box,
  DollarSign,
  Megaphone,
  CreditCard,
  Bell,
  Award,
  BarChart,
  Star,
} from "lucide-react";
import Link from "next/link";
import { CreateSpaceDialog } from "./CreateSpaceDialog";
import { useState } from "react";
import { RestrictedFeature, useFeatureAccess } from '@/hooks/use-feature-access';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

import {
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { fetchSpaces, setCurrentSpace } from "@/store/slices/spaceSlice";

const navItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Active Services",
    url: "/dashboard/active-services",
    icon: Clock,
  },
  {
    title: "Appointments",
    url: "/dashboard/appointments",
    icon: Calendar,
  },
  {
    title: "Customers",
    url: "/dashboard/customers",
    icon: Users,
  },
  {
    title: "Services",
    url: "/dashboard/services",
    icon: Scissors,
  },
  {
    title: "Inventory",
    url: "/dashboard/inventory",
    icon: Box,
  },
  {
    title: "Accounting",
    url: "/dashboard/accounting",
    icon: DollarSign,
  },
  {
    title: "Marketing",
    url: "/dashboard/marketing",
    icon: Megaphone,
    premium: true,
    feature: RestrictedFeature.MARKETING,
  },
  {
    title: "Notifications",
    url: "/dashboard/notifications",
    icon: Bell,
  },
  {
    title: "Loyalty",
    url: "/dashboard/loyalty",
    icon: Award,
    premium: true,
    feature: RestrictedFeature.LOYALTY_PROGRAM,
  },
  
  {
    title: "Settings",
    url: "/dashboard/settings",
    icon: Settings,
  },
];



export function AppSidebar() {
  const { user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useAppDispatch();
  const spaces = useAppSelector((state) => state.space.spaces);
  const currentSpace = useAppSelector((state) => state.space.currentSpace);
  const [shopDialogOpen, setShopDialogOpen] = useState(false)

  const handleAddNewShop = () => {
    setShopDialogOpen(true)
  }

  const handleLogout = () => {
    toast.loading('Logging out...');
    dispatch(logoutUser())
      .unwrap()
      .then(() => {
        toast.success('Logged out successfully');
        router.push('/login');
      })
      .catch((error) => {
        console.error('Error logging out:', error);
        toast.error('Failed to log out');
      });
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton>
                  <Store className="mr-2 h-4 w-4" />
                  {currentSpace?.name || "Select Space"}
                  <ChevronDown className="ml-auto h-4 w-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[--radix-popper-anchor-width]">
                <DropdownMenuLabel>Your Spaces</DropdownMenuLabel>
                {spaces.map((space) => (
                  <DropdownMenuItem
                    key={space.id}
                    onSelect={() => dispatch(setCurrentSpace(space))}
                  >
                    <div className="flex flex-col">
                      <span>{space.name}</span>
                      <span className="text-xs text-muted-foreground">{space.address}</span>
                    </div>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onSelect={handleAddNewShop}
                  className="text-primary"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Salon
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navItems.map((item) => {
                // Check if the current path matches this nav item
                // For the dashboard, we need an exact match
                // For other items, we check if the pathname starts with the item URL
                const isActive =
                  item.url === "/dashboard"
                    ? pathname === "/dashboard"
                    : pathname.startsWith(item.url);

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild isActive={isActive}>
                      <Link href={item.url} className="flex items-center">
                        <item.icon className="mr-2 h-4 w-4" />
                        <span>{item.title}</span>
                        {item.premium && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="ml-2 bg-primary/10 text-primary text-xs px-1.5 py-0.5 rounded-sm font-medium cursor-help">
                                  PRO
                                </span>
                              </TooltipTrigger>
                              <TooltipContent side="right" className="bg-white text-black border border-gray-200 shadow-md max-w-[200px]">
                                <p className="text-xs">This is a premium feature available on the Pro plan and above.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link href="/dashboard/billing" className="flex items-center">
                <Star className="mr-2 h-4 w-4 text-amber-500" />
                <span>Upgrade to Pro</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton className="flex items-center space-x-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={user?.photoURL || ''} alt={user?.displayName || 'User'} />
                    <AvatarFallback>
                      {user?.displayName ? user.displayName.charAt(0).toUpperCase() : user?.email ? user.email.charAt(0).toUpperCase() : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="truncate max-w-[120px]">{user?.displayName || user?.email}</span>
                  <ChevronUp className="ml-auto h-4 w-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="top"
                className="w-[--radix-popper-anchor-width]"
              >
                <DropdownMenuItem>
                  <Link href="/dashboard/settings">Profile</Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleLogout}>
                  <span>Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <Dialog open={shopDialogOpen} onOpenChange={setShopDialogOpen}>
        <DialogContent className="sm:max-w-[500px] bg-white p-0 gap-0 overflow-hidden">
          <DialogHeader className="px-6 pt-6 pb-4 border-b">
            <DialogTitle>Create New Shop</DialogTitle>
            <DialogDescription>
              Fill in the details below to create your shop.
            </DialogDescription>
          </DialogHeader>
          <CreateSpaceDialog onClose={() => setShopDialogOpen(false)} />
        </DialogContent>
      </Dialog>
    </Sidebar>
  );
}
