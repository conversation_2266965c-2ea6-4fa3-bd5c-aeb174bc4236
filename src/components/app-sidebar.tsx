import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/utils/auth";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { logoutUser } from "@/store/slices/authSlice";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "sonner";
import {
  LayoutDashboard,
  Users,
  Scissors,
  Calendar,
  Settings,
  Store,
  ChevronDown,
  ChevronUp,
  User2,
  Plus,
  Edit,
  Clock,
  Box,
  Megaphone,
  CreditCard,
  Bell,
  Award,
  BarChart,
  Star,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { RestrictedFeature, useFeatureAccess } from '@/hooks/use-feature-access';
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import {
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { fetchSpaces, setCurrentSpace } from "@/store/slices/spaceSlice";

const navItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Active Services",
    url: "/dashboard/active-services",
    icon: Clock,
  },
  {
    title: "Appointments",
    url: "/dashboard/appointments",
    icon: Calendar,
  },
  {
    title: "Customers",
    url: "/dashboard/customers",
    icon: Users,
  },
  {
    title: "Services",
    url: "/dashboard/services",
    icon: Scissors,
  },
  {
    title: "Inventory",
    url: "/dashboard/inventory",
    icon: Box,
  },
  {
    title: "Marketing",
    url: "/dashboard/marketing",
    icon: Megaphone,
    premium: true,
    feature: RestrictedFeature.MARKETING,
  },
  {
    title: "Notifications",
    url: "/dashboard/notifications",
    icon: Bell,
  },
  {
    title: "Loyalty",
    url: "/dashboard/loyalty",
    icon: Award,
    premium: true,
    feature: RestrictedFeature.LOYALTY_PROGRAM,
  },
  
  {
    title: "Settings",
    url: "/dashboard/settings",
    icon: Settings,
  },
];

const shopFormSchema = z.object({
  name: z.string().min(2, "Shop name must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  address: z.string().min(5, "Address must be at least 5 characters"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  imageUrl: z.string().optional(),
})

type ShopFormValues = z.infer<typeof shopFormSchema>

export function AppSidebar() {
  const { user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useAppDispatch();
  const spaces = useAppSelector((state) => state.space.spaces);
  const currentSpace = useAppSelector((state) => state.space.currentSpace);
  const [shopDialogOpen, setShopDialogOpen] = useState(false)
  const [isEditingShop, setIsEditingShop] = useState(false)

  const spaceForm = useForm<ShopFormValues>({
    resolver: zodResolver(shopFormSchema),
    defaultValues: {
      name: "",
      description: "",
      address: "",
      phone: "",
      imageUrl: "",
    },
  })

  const onSpaceSubmit = async (data: ShopFormValues) => {
    if (!user) return;

    try {
      // Here you would typically make an API call to add/update the space
      console.log(isEditingShop ? "Updating space:" : "Adding new space:", data)

      // Prepare space data for API call
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const spaceData = {
        ...data,
        ownerId: user.uid,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        stats: {
          totalCustomers: 0,
          totalAppointments: 0,
          totalRevenue: 0,
          totalServices: 0,
        }
      }

      // After successful API call, refresh the spaces list
      dispatch(fetchSpaces(user.uid))
      setShopDialogOpen(false)
      spaceForm.reset()
    } catch (error) {
      console.error("Failed to save space:", error)
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleEditShop = (shop: any) => {
    setIsEditingShop(true)
    spaceForm.reset({
      name: shop.name,
      description: shop.description,
      address: shop.address,
      phone: shop.phone,
      imageUrl: shop.imageUrl,
    })
    setShopDialogOpen(true)
  }

  const handleAddNewShop = () => {
    setIsEditingShop(false)
    spaceForm.reset()
    setShopDialogOpen(true)
  }

  const handleLogout = () => {
    toast.loading('Logging out...');
    dispatch(logoutUser())
      .unwrap()
      .then(() => {
        toast.success('Logged out successfully');
        router.push('/login');
      })
      .catch((error) => {
        console.error('Error logging out:', error);
        toast.error('Failed to log out');
      });
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton>
                  <Store className="mr-2 h-4 w-4" />
                  {currentSpace?.name || "Select Space"}
                  <ChevronDown className="ml-auto h-4 w-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[--radix-popper-anchor-width]">
                <DropdownMenuLabel>Your Spaces</DropdownMenuLabel>
                {spaces.map((space) => (
                  <DropdownMenuItem
                    key={space.id}
                    className="justify-between"
                    onSelect={() => dispatch(setCurrentSpace(space))}
                  >
                    <div className="flex flex-col">
                      <span>{space.name}</span>
                      <span className="text-xs text-muted-foreground">{space.address}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleEditShop(space)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onSelect={handleAddNewShop}
                  className="text-primary"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Salon
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navItems.map((item) => {
                // Check if the current path matches this nav item
                // For the dashboard, we need an exact match
                // For other items, we check if the pathname starts with the item URL
                const isActive =
                  item.url === "/dashboard"
                    ? pathname === "/dashboard"
                    : pathname.startsWith(item.url);

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild isActive={isActive}>
                      <Link href={item.url} className="flex items-center">
                        <item.icon className="mr-2 h-4 w-4" />
                        <span>{item.title}</span>
                        {item.premium && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="ml-2 bg-primary/10 text-primary text-xs px-1.5 py-0.5 rounded-sm font-medium cursor-help">
                                  PRO
                                </span>
                              </TooltipTrigger>
                              <TooltipContent side="right" className="bg-white text-black border border-gray-200 shadow-md max-w-[200px]">
                                <p className="text-xs">This is a premium feature available on the Pro plan and above.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link href="/dashboard/billing" className="flex items-center">
                <Star className="mr-2 h-4 w-4 text-amber-500" />
                <span>Upgrade to Pro</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton className="flex items-center space-x-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={user?.photoURL || ''} alt={user?.displayName || 'User'} />
                    <AvatarFallback>
                      {user?.displayName ? user.displayName.charAt(0).toUpperCase() : user?.email ? user.email.charAt(0).toUpperCase() : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="truncate max-w-[120px]">{user?.displayName || user?.email}</span>
                  <ChevronUp className="ml-auto h-4 w-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="top"
                className="w-[--radix-popper-anchor-width]"
              >
                <DropdownMenuItem>
                  <Link href="/dashboard/settings">Profile</Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleLogout}>
                  <span>Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <Dialog open={shopDialogOpen} onOpenChange={setShopDialogOpen}>
        <DialogContent className="max-w-[95vw] w-full sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-1">
          <DialogHeader>
            <DialogTitle>{isEditingShop ? "Edit Space" : "Add New Space"}</DialogTitle>
            <DialogDescription>
              {isEditingShop
                ? "Update your space information."
                : "Add a new space location."}
            </DialogDescription>
          </DialogHeader>
          <Form {...spaceForm}>
            <form onSubmit={spaceForm.handleSubmit(onSpaceSubmit)} className="space-y-6">
              <FormField
                control={spaceForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Salon Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter salon name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={spaceForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your space"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={spaceForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter space address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={spaceForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="+254 700 000000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="submit" className="w-full">
                  {isEditingShop ? "Update Space" : "Add Space"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Sidebar>
  );
}
