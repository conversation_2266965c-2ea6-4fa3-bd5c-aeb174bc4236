'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Array of greetings in different languages
const greetings = [
  { text: "Hello", language: "English" },
  { text: "Hey", language: "English (Casual)" },
  { text: "Sup", language: "English (Slang)" },
  { text: "你好", language: "Chinese" },
  { text: "Hallo", language: "German" },
  { text: "Niaje", language: "Sheng" },
  { text: "Jambo", language: "Swahili" },
  { text: "Ekaaro", language: "Yoruba" },
  { text: "Sannu", language: "Hausa" },
  { text: "Dumela", language: "Setswana" },
  { text: "Molo", language: "Xhosa" },
  { text: "Sawubona", language: "Zulu" },
  { text: "Hola", language: "Spanish" },
  { text: "Bonjour", language: "French" },
  { text: "Ciao", language: "Italian" },
  { text: "Olá", language: "Portuguese" },
];

export function AnimatedGreeting() {
  const [index, setIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Change greeting every 3.5 seconds
    const interval = setInterval(() => {
      // First fade out
      setIsVisible(false);

      // Then change text and fade in after a short delay
      setTimeout(() => {
        setIndex((prevIndex) => (prevIndex + 1) % greetings.length);
        setIsVisible(true);
      }, 500);
    }, 3500);

    // Clean up interval on component unmount
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="h-[4rem] flex items-center overflow-hidden">
      <motion.div
        animate={{
          opacity: isVisible ? 1 : 0,
          y: isVisible ? 0 : 10
        }}
        transition={{
          duration: 0.4,
          ease: "easeInOut"
        }}
        className="flex items-baseline"
      >
        <h2 className="text-4xl font-bold text-black">
          <motion.span
            className="text-mustard"
            animate={{
              color: ['#F5B800', '#E5AB00', '#F5B800']
            }}
            transition={{
              duration: 3,
              ease: "easeInOut",
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            {greetings[index].text}
          </motion.span>,
        </h2>
        <span className="ml-2 text-xs display-none text-gray-500 italic mt-2">
          {greetings[index].language}
        </span>
      </motion.div>
    </div>
  );
}
