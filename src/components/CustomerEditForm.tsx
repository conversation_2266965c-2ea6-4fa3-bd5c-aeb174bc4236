'use client'

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { customerService } from "@/services/firestore"
import { Customer } from "@/services/types/models"
import { formatToE164 } from "@/utils/phoneUtils"
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { CalendarIcon } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

const customerFormSchema = z.object({
  displayName: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phoneNumber: z.string()
    .refine(
      (value) => {
        // Try to format to E.164 and check if valid
        const formatted = formatToE164(value);
        return formatted !== null;
      },
      {
        message: "Please enter a valid phone number (e.g., +254712345678 or 0712345678).",
      }
    ),
  notes: z.string().optional(),
  birthdate: z.string().optional(),
  enrollInLoyalty: z.boolean(),
})

type CustomerFormValues = z.infer<typeof customerFormSchema>

interface CustomerEditFormProps {
  customer?: Customer
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: (customer: Customer) => void
}

export function CustomerEditForm({ customer, isOpen, onOpenChange, onSuccess }: CustomerEditFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const isEditMode = !!customer

  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerFormSchema),
    defaultValues: {
      displayName: customer?.displayName || "",
      email: customer?.email || "",
      phoneNumber: customer?.phoneNumber || "",
      notes: customer?.notes || "",
      birthdate: customer?.loyaltyPoints?.birthdate || "",
      enrollInLoyalty: !!customer?.loyaltyPoints,
    },
  })

  // Update form values when customer changes
  useEffect(() => {
    if (customer) {
      form.reset({
        displayName: customer.displayName,
        email: customer.email,
        phoneNumber: customer.phoneNumber,
        notes: customer.notes || "",
        birthdate: customer.loyaltyPoints?.birthdate || "",
        enrollInLoyalty: !!customer.loyaltyPoints,
      })
    }
  }, [customer, form])

  async function onSubmit(data: CustomerFormValues) {
    try {
      setIsLoading(true)

      // Format phone number to E.164
      const formattedPhoneNumber = formatToE164(data.phoneNumber);
      if (!formattedPhoneNumber) {
        toast.error("Invalid phone number format");
        setIsLoading(false);
        return;
      }

      // Create a copy of data with the formatted phone number
      const formattedData = {
        ...data,
        phoneNumber: formattedPhoneNumber
      };

      if (isEditMode && customer) {
        const now = new Date().toISOString();
        
        // Prepare loyalty data if needed
        const updateData: any = {
          ...formattedData,
          updatedAt: now,
        };
        
        // Handle loyalty program enrollment/unenrollment
        if (form.getValues('enrollInLoyalty')) {
          if (!customer.loyaltyPoints) {
            // Customer is being enrolled in loyalty program
            updateData.loyaltyPoints = {
              balance: 50, // Signup bonus
              tier: 'bronze',
              updatedAt: now,
              birthdate: form.getValues('birthdate') || undefined,
              referralCode: Math.random().toString(36).substring(2, 10).toUpperCase(),
            };
          } else {
            // Update existing loyalty data
            updateData['loyaltyPoints.birthdate'] = form.getValues('birthdate') || customer.loyaltyPoints.birthdate;
            updateData['loyaltyPoints.updatedAt'] = now;
          }
        } else if (customer.loyaltyPoints && !form.getValues('enrollInLoyalty')) {
          // User is being removed from loyalty program - this is optional and may require confirmation
          // Uncomment this if you want to allow removing customers from the loyalty program
          // updateData.loyaltyPoints = null;
        }
        
        // Update existing customer
        await customerService.update(customer.id, updateData);

        // Award signup points if newly enrolled
        if (!customer.loyaltyPoints && form.getValues('enrollInLoyalty')) {
          try {
            const { loyaltyService } = await import('@/services/loyaltyService');
            await loyaltyService.awardPointsForSignup(customer.id);
          } catch (error) {
            console.error('Error awarding signup points:', error);
            // Don't fail the whole process if loyalty points can't be awarded
          }
        }

        const updatedCustomer = {
          ...customer,
          ...formattedData,
          updatedAt: now,
          loyaltyPoints: updateData.loyaltyPoints || customer.loyaltyPoints
        }

        toast.success("Customer updated successfully")

        if (onSuccess) {
          onSuccess(updatedCustomer)
        }
      } else {
        // Create new customer
        const newCustomer = {
          ...formattedData,
          role: 'customer' as const,
          visits: 0,
          createdAt: new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString().split('T')[0],
        }

        const result = await customerService.create(newCustomer)
        const createdCustomer = result as unknown as Customer

        toast.success("Customer added successfully")

        if (onSuccess) {
          onSuccess(createdCustomer)
        }
      }

      form.reset()
      onOpenChange(false)
    } catch (error) {
      console.error('Error saving customer:', error)
      toast.error(isEditMode ? "Failed to update customer" : "Failed to add customer")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95vw] w-full sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditMode ? "Edit Customer" : "Add New Customer"}</DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "Update customer details."
              : "Enter customer details to create a new customer profile."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="displayName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter customer name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter email address" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter phone number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="birthdate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Birthdate (for birthday rewards)</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(new Date(field.value), "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => {
                          field.onChange(date ? date.toISOString() : "")
                        }}
                        disabled={(date) => date > new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="enrollInLoyalty"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Loyalty Program</FormLabel>
                    <FormMessage />
                    <p className="text-sm text-muted-foreground">
                      Enroll this customer in the loyalty program
                    </p>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any notes about this customer"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditMode ? "Update Customer" : "Add Customer"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
