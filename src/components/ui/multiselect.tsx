import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

interface Option {
  value: string
  label: string
}

interface MultiSelectProps {
  options: Option[]
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
}

export function MultiSelect({ options, value, onChange, placeholder }: MultiSelectProps) {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")

  const filtered = options.filter(opt =>
    opt.label.toLowerCase().includes(search.toLowerCase())
  )

  const handleToggle = (val: string) => {
    if (value.includes(val)) {
      onChange(value.filter(v => v !== val))
    } else {
      onChange([...value, val])
    }
  }

  return (
    <div className="relative">
      <Button
        type="button"
        variant="outline"
        className="w-full justify-between"
        onClick={() => setOpen(o => !o)}
      >
        {value.length === 0 ? (
          <span className="text-muted-foreground">{placeholder || "Select..."}</span>
        ) : (
          <span>{options.filter(o => value.includes(o.value)).map(o => o.label).join(", ")}</span>
        )}
        <span className="ml-2">▼</span>
      </Button>
      {open && (
        <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow p-2 max-h-60 overflow-auto">
          <Input
            placeholder="Search..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="mb-2"
          />
          {filtered.length === 0 && (
            <div className="text-sm text-muted-foreground px-2 py-1">No options</div>
          )}
          {filtered.map(opt => (
            <label key={opt.value} className="flex items-center gap-2 px-2 py-1 cursor-pointer hover:bg-accent rounded">
              <input
                type="checkbox"
                checked={value.includes(opt.value)}
                onChange={() => handleToggle(opt.value)}
              />
              <span>{opt.label}</span>
            </label>
          ))}
        </div>
      )}
    </div>
  )
} 