'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  priority?: boolean;
  objectFit?: 'contain' | 'cover' | 'fill';
  onLoad?: () => void;
}

/**
 * A wrapper around Next.js Image component that adds loading state
 * and better defaults for performance
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  objectFit = 'cover',
  onLoad,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <div className="relative" style={{ width, height }}>
      {isLoading && (
        <Skeleton 
          className="absolute inset-0 rounded-md" 
          style={{ width, height }}
        />
      )}
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-500`}
        style={{ 
          objectFit: objectFit as any, 
          width: '100%',
          height: '100%'
        }}
        priority={priority}
        onLoadingComplete={() => {
          setIsLoading(false);
          onLoad?.();
        }}
      />
    </div>
  );
}
