'use client';

import { useState, useRef, useEffect } from 'react';
import { Check } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
}

const presetColors = [
  '#000000',
  '#ffffff',
  '#f43f5e',
  '#ec4899',
  '#d946ef',
  '#a855f7',
  '#8b5cf6',
  '#6366f1',
  '#3b82f6',
  '#0ea5e9',
  '#06b6d4',
  '#14b8a6',
  '#10b981',
  '#22c55e',
  '#84cc16',
  '#eab308',
  '#f59e0b',
  '#f97316',
  '#ef4444',
];

export function ColorPicker({ color, onChange }: ColorPickerProps) {
  const [currentColor, setCurrentColor] = useState(color);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setCurrentColor(color);
  }, [color]);

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentColor(e.target.value);
    onChange(e.target.value);
  };

  const handlePresetClick = (preset: string) => {
    setCurrentColor(preset);
    onChange(preset);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className="w-10 h-10 p-0 border-2"
          style={{ backgroundColor: currentColor }}
        />
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <div 
              className="w-10 h-10 rounded-md border border-input"
              style={{ backgroundColor: currentColor }}
            />
            <input
              ref={inputRef}
              type="color"
              value={currentColor}
              onChange={handleColorChange}
              className="w-full h-10"
            />
          </div>
          <div>
            <div className="grid grid-cols-5 gap-2">
              {presetColors.map((preset) => (
                <button
                  key={preset}
                  type="button"
                  className={cn(
                    "w-8 h-8 rounded-md border border-input flex items-center justify-center",
                    currentColor === preset && "ring-2 ring-ring"
                  )}
                  style={{ backgroundColor: preset }}
                  onClick={() => handlePresetClick(preset)}
                >
                  {currentColor === preset && (
                    <Check className={cn("h-4 w-4", preset === '#ffffff' ? "text-black" : "text-white")} />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
