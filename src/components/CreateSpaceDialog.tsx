'use client'

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { doc, setDoc } from 'firebase/firestore';
import { getDownloadURL, ref, uploadBytes } from 'firebase/storage';

import { fetchSpaces } from '@/store/slices/spaceSlice';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ImagePlus } from 'lucide-react';
import { toast } from 'sonner';
import { db } from '@/utils/firebase';
import { storage } from '@/utils/firebase';

const spaceSchema = z.object({
  name: z.string().min(2, 'Space name must be at least 2 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  // Make these fields optional
  city: z.string().min(2, 'City must be at least 2 characters').optional().or(z.literal('')),
  state: z.string().min(2, 'State/Province must be at least 2 characters').optional().or(z.literal('')),
  postalCode: z.string().min(4, 'Postal code must be at least 4 characters').optional().or(z.literal('')),
  phone: z.string().min(10, 'Phone number must be at least 10 characters').optional().or(z.literal('')),
  email: z.string().email('Invalid email address'),
  type: z.enum(['salon', 'barbershop', 'spa', 'fitness', 'other']),
  website: z.string().url('Must be a valid URL').optional().or(z.literal('')),
});

type FormData = z.infer<typeof spaceSchema>;

interface Props {
  onClose: () => void;
  isOnboarding?: boolean;
}

export function CreateSpaceDialog({ onClose, isOnboarding = false }: Props) {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(spaceSchema),
    defaultValues: {
      name: '',
      description: '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      phone: '',
      email: '',
      type: 'salon',
      website: '',
    },
  });

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: FormData) => {
    if (!user) {
      toast.error("You must be logged in to create a space");
      return;
    }

    try {
      setIsLoading(true);

      // Upload image if selected
      let imageUrl = '';
      if (imageFile) {
        const imageRef = ref(storage, `spaces/${user.uid}/${imageFile.name}`);
        await uploadBytes(imageRef, imageFile);
        imageUrl = await getDownloadURL(imageRef);
      }

      // Create space document
      const spaceId = crypto.randomUUID();
      const spaceData = {
        id: spaceId,
        ...data,
        logo: imageUrl,
        ownerId: user.uid,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await setDoc(doc(db, 'spaces', spaceId), spaceData);

      // Log space creation in analytics collection
      const spaceAnalyticsData = {
        spaceId: spaceId,
        userId: user.uid,
        spaceName: data.name,
        spaceType: data.type,
        hasLogo: !!imageUrl,
        createdAt: new Date().toISOString(),
        // Include only non-sensitive fields for analytics
        city: data.city || null,
        state: data.state || null,
        hasWebsite: !!data.website,
        // Add context about where this space was created
        createdDuringOnboarding: true
      };
      
      // Add to space-analytics collection
      await setDoc(doc(db, 'space-analytics', spaceId), spaceAnalyticsData);
      console.log("Space analytics saved:", spaceAnalyticsData);

      // If this is during onboarding, update user's onboarding status
      if (isOnboarding) {
        await setDoc(doc(db, 'users', user.uid), {
          hasCompletedOnboarding: true,
          updatedAt: new Date().toISOString(),
        }, { merge: true });
      }

      // Refresh spaces list
      await dispatch(fetchSpaces(user.uid));

      console.log("Space created successfully, ID:", spaceId);
      toast.success("Your space has been created");

      // Reset form
      form.reset();
      setImagePreview(null);
      setImageFile(null);
      onClose();
    } catch (error) {
      console.error('Error creating space:', error);
      toast.error("There was an error creating your space");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full max-h-[90vh] sm:max-h-[80vh]">
      <ScrollArea className="flex-1 overflow-scroll">
        <div className="px-4 sm:px-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
              {/* Required fields notice */}
              <div className="mb-4 text-sm text-muted-foreground">
                <p>Fields marked with <span className="text-red-500">*</span> are required.</p>
              </div>
              
              {/* Image upload */}
              <div className="flex flex-col items-center mb-4">
                <div
                  className="w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-muted flex items-center justify-center mb-2 overflow-hidden"
                  style={{
                    backgroundImage: imagePreview ? `url(${imagePreview})` : 'none',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                >
                  {!imagePreview && <ImagePlus className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />}
                </div>
                <label
                  htmlFor="logo-upload"
                  className="cursor-pointer text-sm text-primary hover:underline"
                >
                  Upload Logo
                </label>
                <input
                  id="logo-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="col-span-1 sm:col-span-2">
                      <FormLabel>Space Name <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input placeholder="Your business name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem className="col-span-1 sm:col-span-2">
                      <FormLabel>Space Type <span className="text-red-500">*</span></FormLabel>
                      <Select 
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select space type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="salon">Salon</SelectItem>
                          <SelectItem value="barbershop">Barbershop</SelectItem>
                          <SelectItem value="spa">Spa</SelectItem>
                          <SelectItem value="fitness">Fitness Center</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="col-span-1 sm:col-span-2">
                      <FormLabel>Description <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe your business"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem className="col-span-1 sm:col-span-2">
                      <FormLabel>Address <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input placeholder="Street address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City <span className="text-xs text-muted-foreground">(Optional)</span></FormLabel>
                      <FormControl>
                        <Input placeholder="City" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State/Province <span className="text-xs text-muted-foreground">(Optional)</span></FormLabel>
                      <FormControl>
                        <Input placeholder="State/Province" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code <span className="text-xs text-muted-foreground">(Optional)</span></FormLabel>
                      <FormControl>
                        <Input placeholder="Postal code" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone <span className="text-xs text-muted-foreground">(Optional)</span></FormLabel>
                      <FormControl>
                        <Input placeholder="Business phone" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input placeholder="Business email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem className="col-span-1 sm:col-span-2">
                      <FormLabel>Website (optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="https://yourwebsite.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>
      </ScrollArea>
      
      {/* Action buttons - fixed at the bottom */}
      <div className="p-4 border-t bg-background mt-auto">
        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              'Create Space'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
