'use client';

import React from 'react';
import Script from 'next/script';

interface PricingSchemaProps {
  planName: string;
  description: string;
  currency: string;
  price: number;
  billingPeriod: string;
  trialDays?: number;
}

/**
 * Adds structured JSON-LD data for pricing plans
 * This helps search engines understand your pricing structure
 * and display rich results for your product
 */
export function PricingSchema({
  planName,
  description,
  currency,
  price,
  billingPeriod,
  trialDays
}: PricingSchemaProps) {
  // Define the type for our structured data
  interface StructuredData {
    '@context': string;
    '@type': string;
    name: string;
    description: string;
    applicationCategory: string;
    offers: {
      '@type': string;
      price: string;
      priceCurrency: string;
      priceValidUntil: string;
      availability: string;
      priceSpecification: {
        '@type': string;
        price: string;
        priceCurrency: string;
        billingIncrement: number;
        billingDuration: string;
      };
      additionalProperty?: {
        '@type': string;
        name: string;
        value: string;
      };
    };
  }
  
  // Create the structured data object with proper typing
  const structuredData: StructuredData = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: `GroomBook ${planName} Plan`,
    description,
    applicationCategory: 'BusinessApplication',
    offers: {
      '@type': 'Offer',
      price: price.toString(),
      priceCurrency: currency,
      priceValidUntil: new Date(
        new Date().setFullYear(new Date().getFullYear() + 1)
      ).toISOString().split('T')[0],
      availability: 'https://schema.org/InStock',
      priceSpecification: {
        '@type': 'PriceSpecification',
        price: price.toString(),
        priceCurrency: currency,
        billingIncrement: 1,
        billingDuration: billingPeriod === 'monthly' ? 'P1M' : 'P1Y' 
      }
    }
  };

  // Add trial period information if applicable
  if (trialDays) {
    structuredData.offers.additionalProperty = {
      '@type': 'PropertyValue',
      name: 'Trial Period',
      value: `${trialDays}-day free trial`
    };
  }

  return (
    <Script id={`pricing-schema-${planName.toLowerCase()}`} type="application/ld+json" strategy="afterInteractive">
      {JSON.stringify(structuredData)}
    </Script>
  );
}
