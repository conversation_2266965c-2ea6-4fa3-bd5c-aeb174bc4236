'use client';

import Script from 'next/script';

export function WebsiteSchema() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "GroomBook",
    "description": "Barbershop management software that helps eliminate empty chairs, reduce no-shows, and increase revenue. Complete solution for appointment scheduling, walk-in management, and business analytics.",
    "url": "https://groombook.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://groombook.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "GroomBook",
      "url": "https://groombook.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://groombook.com/logo.png",
        "width": 300,
        "height": 100
      },
      "sameAs": [
        "https://twitter.com/groombook",
        "https://facebook.com/groombook",
        "https://linkedin.com/company/groombook"
      ]
    }
  };

  return (
    <Script
      id="website-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2),
      }}
    />
  );
}
