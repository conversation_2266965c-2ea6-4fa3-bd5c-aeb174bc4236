'use client';

import Script from 'next/script';

export function LocalBusinessSchema() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "GroomBook",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web, iOS, Android",
    "description": "Simple salon and barbershop management software that helps organize appointments, handle walk-ins, process payments, and manage staff scheduling. Complete business solution for salons and barbershops.",
    "url": "https://groombook.com",
    "downloadUrl": "https://groombook.com/register",
    "screenshot": "https://groombook.com/screenshots/dashboard.png",
    "softwareVersion": "2.0",
    "datePublished": "2024-01-01",
    "publisher": {
      "@type": "Organization",
      "name": "GroomBook",
      "url": "https://groombook.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://groombook.com/logo.png"
      }
    },
    "offers": [
      {
        "@type": "Offer",
        "name": "Basic Plan",
        "description": "For 1-2 chair shops",
        "price": "5.00",
        "priceCurrency": "USD",
        "billingIncrement": 1,
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "5.00",
          "priceCurrency": "USD",
          "unitCode": "MON"
        }
      },
      {
        "@type": "Offer",
        "name": "Pro Plan", 
        "description": "Most barbershops choose this",
        "price": "19.90",
        "priceCurrency": "USD",
        "billingIncrement": 1,
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "19.90",
          "priceCurrency": "USD",
          "unitCode": "MON"
        }
      }
    ],
    "featureList": [
      "Walk-in queue management",
      "Appointment scheduling", 
      "Automated reminders",
      "Revenue tracking",
      "Staff scheduling",
      "Customer communication",
      "Payment processing",
      "Business analytics"
    ]
  };

  return (
    <Script
      id="local-business-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2),
      }}
    />
  );
}
