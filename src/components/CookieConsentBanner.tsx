'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CookieSettings } from '@/components/CookieSettings';
import { <PERSON>ie, X } from 'lucide-react';

export function CookieConsentBanner() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already consented to cookies
    const hasConsented = localStorage.getItem('cookieConsent');

    if (!hasConsented) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    // Save consent to localStorage
    localStorage.setItem('cookieConsent', 'all');
    setIsVisible(false);
  };

  const handleClose = () => {
    // Save minimal consent to localStorage
    localStorage.setItem('cookieConsent', 'necessary');
    setIsVisible(false);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4 md:p-6 z-50 shadow-lg">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div className="flex items-start gap-3">
            <Cookie className="h-5 w-5 text-mustard flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-medium mb-1">We use cookies</h3>
              <p className="text-sm text-muted-foreground">
                We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking &ldquo;Accept All&rdquo;, you consent to our use of cookies.
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3 ml-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClose}
              className="rounded-full w-8 h-8 p-0"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>

            <CookieSettings
              trigger={
                <Button variant="outline" size="sm">
                  Customize
                </Button>
              }
            />

            <Button
              size="sm"
              onClick={handleAcceptAll}
              className="bg-mustard text-black hover:bg-mustard-dark"
            >
              Accept All
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
