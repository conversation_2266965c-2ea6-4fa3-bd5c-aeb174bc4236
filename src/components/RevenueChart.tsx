"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, Legend, XAxis, <PERSON>Axis } from "recharts"
import { startOfMonth, endOfMonth, format } from "date-fns"
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/utils/firebase';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { revenueTargetService } from "@/services/revenueTargetService"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Invoice } from "@/services/types/models"
import { useAppSelector } from '@/store/hooks';
import { formatCurrency } from '@/utils/currency';
import { formatPrice } from "@/utils/price-formatter"
import { space } from "postcss/lib/list";

interface DailyRevenue {
  date: string;
  received: number;
  target: number | null;
}

interface RevenueChartProps {
  spaceId?: string; 
}

const chartConfig = {
  received: {
    label: "Received Revenue",
    color: "hsl(43, 96%, 56%)", // Mustard color
  },
  target: {
    label: "Target Revenue",
    color: "hsl(220, 13%, 91%)", // Gray color
  },
} satisfies ChartConfig

export function RevenueChart({ spaceId }: RevenueChartProps) {

  const { currentSpace } = useAppSelector((state) => state.space);
  const [selectedMonth, setSelectedMonth] = React.useState(new Date().getMonth());
  const [selectedYear] = React.useState(new Date().getFullYear());
  const [chartData, setChartData] = React.useState<DailyRevenue[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);

  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];      const fetchRevenueData = React.useCallback(async () => {
    try {
      setIsLoading(true);
      const start = startOfMonth(new Date(selectedYear, selectedMonth));
      const end = endOfMonth(new Date(selectedYear, selectedMonth));
      
      // Use spaceId if provided, otherwise fallback to currentSpace.id or empty string
      const businessId = spaceId || (currentSpace?.id || '')

      // Get the active daily target (if any)
      const dailyTarget = await revenueTargetService.getActiveDailyTarget(businessId);

      // Get all invoices for the month - query both space and salon collections for backward compatibility
      const invoicesQuery = query(
        collection(db, 'invoices'),
        where(spaceId ? 'spaceId' : 'salonId', '==', businessId),
        where('status', '==', 'paid'),
        where('createdAt', '>=', start.toISOString()),
        where('createdAt', '<=', end.toISOString())
      );

      const invoicesSnapshot = await getDocs(invoicesQuery);
      const invoices = invoicesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Invoice[];

      // Initialize the dataset with target values if available
      const dataset: DailyRevenue[] = [];
      let currentDate = new Date(start);

      while (currentDate <= end) {
        const dateString = format(currentDate, 'yyyy-MM-dd');
        
        // Get all invoices for this day
        const dayInvoices = invoices.filter(invoice => 
          format(new Date(invoice.createdAt), 'yyyy-MM-dd') === dateString
        );

        // Calculate total revenue for the day
        const dayRevenue = dayInvoices.reduce((sum, invoice) => sum + invoice.total, 0);

        dataset.push({
          date: dateString,
          received: dayRevenue,
          target: dailyTarget ? dailyTarget.amount : null
        });

        currentDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
      }

      setChartData(dataset);
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching revenue data:', error);
      setChartData([]);
      setIsLoading(false);
    }
  }, [selectedMonth, selectedYear, spaceId]);

  React.useEffect(() => {
    fetchRevenueData();
  }, [fetchRevenueData]);

  const total = React.useMemo(
    () => ({
      received: chartData.reduce((acc, curr) => acc + curr.received, 0),
      target: chartData.reduce((acc, curr) => acc + (curr.target || 0), 0),
    }),
    [chartData]
  );

  const formatCurrencyValue = (value: number) => {
    // Use currentSpace if available, otherwise fall back to currentSalon
    return formatPrice(value, currentSpace);
  };

  // Calculate the difference and percentage
  const difference = total.received - total.target
  const percentage = ((difference / total.target) * 100).toFixed(1)
  const isAhead = difference > 0

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Revenue Overview</CardTitle>
          <CardDescription>Loading revenue data...</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px] flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </CardContent>
      </Card>
    );
  }

  if (!chartData.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Revenue Overview</CardTitle>
          <CardDescription>No revenue data available for this period</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px] flex flex-col items-center justify-center text-center">
          <p className="text-muted-foreground">
            Complete your first transaction to see revenue analytics
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0">
        <div className="flex flex-row items-center justify-between px-6 py-5">
          <div className="flex flex-col gap-1">
            <CardTitle>Monthly Revenue Overview</CardTitle>
            <CardDescription>
              Your revenue for {months[selectedMonth]} {selectedYear}
            </CardDescription>
            <div className="mt-2 text-2xl font-bold">
              {formatCurrencyValue(total.received)}
            </div>
          </div>
          <Select
            value={selectedMonth.toString()}
            onValueChange={(value) => setSelectedMonth(parseInt(value))}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select month" />
            </SelectTrigger>
            <SelectContent>
              {months.map((month, index) => (
                <SelectItem key={index} value={index.toString()}>
                  {month} {selectedYear}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex border-t">
          {["received", "target"].map((key) => {
            const chart = key as keyof typeof chartConfig
            return (
              <div
                key={chart}
                className="relative z-30 flex flex-1 flex-col justify-center gap-1 px-6 py-4 text-left even:border-l sm:px-8 sm:py-6"
              >
                <span className="text-xs text-muted-foreground">
                  {chartConfig[chart].label}
                </span>
                <span className="text-lg font-bold leading-none sm:text-2xl">
                  {formatCurrencyValue(total[chart])}
                </span>
              </div>
            )
          })}
        </div>
      </CardHeader>
      <CardContent className="px-2 pb-8 sm:p-6 sm:pb-8">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[350px] w-full"
        >
          <BarChart
            data={chartData}
            margin={{
              left: 48,
              right: 12,
              top: 24,
              bottom: 24,
            }}
          >
            <CartesianGrid vertical={false} strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.getDate().toString()
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => formatCurrencyValue(value)}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  className="w-[200px]"
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "long",
                      day: "numeric",
                      year: "numeric",
                    })
                  }}
                />
              }
            />
            <Legend />
            <Bar
              name="Received Revenue"
              dataKey="received"
              fill={chartConfig.received.color}
              radius={[4, 4, 0, 0]}
            />
            <Bar
              name="Target Revenue"
              dataKey="target"
              fill={chartConfig.target.color}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
