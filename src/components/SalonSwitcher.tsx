'use client';

import React, { useEffect, useState } from 'react';
import { Check, ChevronsUpDown, PlusCircle, Store } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CreateSpaceDialog } from '@/components/CreateSpaceDialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setCurrentSpace, fetchSpaces } from '@/store/slices/spaceSlice';

export function SalonSwitcher() {
  const dispatch = useAppDispatch();
  const { spaces, currentSpace, loading } = useAppSelector((state) => state.space);
  const user = useAppSelector((state) => state.auth.user);

  useEffect(() => {
    if (user) {
      dispatch(fetchSpaces(user.uid));
    }
  }, [dispatch, user]);

  const handleSpaceSelect = (spaceId: string) => {
    const selectedSpace = spaces.find((space) => space.id === spaceId);
    if (selectedSpace) {
      dispatch(setCurrentSpace(selectedSpace));
    }
  };

  // Create state for the dialog
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const handleCreateSpace = () => {
    // Open the dialog
    setCreateDialogOpen(true);
  };

  if (loading) {
    return (
      <Button variant="ghost" className="w-full justify-between px-3">
        <div className="flex items-center">
          <Store className="mr-2 h-5 w-5 text-gray-400" />
          <div className="flex flex-col items-start">
            <span className="text-sm font-medium">Loading spaces...</span>
            <span className="text-xs text-gray-500">Please wait</span>
          </div>
        </div>
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-[#F5B800]"></div>
      </Button>
    );
  }

  return (
    <>
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-[95vw] w-full sm:max-w-[500px] max-h-[90vh] overflow-y-auto bg-white p-0 gap-0">
          <DialogHeader className="px-6 pt-6 pb-4 border-b">
            <DialogTitle>Create New Space</DialogTitle>
            <DialogDescription>
              Fill in the details below to create your space.
            </DialogDescription>
          </DialogHeader>
          <CreateSpaceDialog onClose={() => setCreateDialogOpen(false)} />
        </DialogContent>
      </Dialog>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            role="combobox"
            aria-expanded={false}
            aria-label="Select a space"
            className="w-full justify-between hover:bg-[#FFF8E0] px-3"
          >
            <div className="flex items-center truncate">
              <Store className="mr-2 h-5 w-5 text-[#F5B800]" />
              <div className="flex flex-col items-start">
                <span className="text-sm font-medium">
                  {currentSpace?.name || "Select a space"}
                </span>
                <span className="text-xs text-gray-500">
                  {spaces.length} {spaces.length === 1 ? 'space' : 'spaces'}
                </span>
              </div>
            </div>
            <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[240px] p-0">
          <Command>
            <CommandInput placeholder="Search space..." />
            <CommandList>
              <CommandEmpty>No space found.</CommandEmpty>
              <CommandGroup heading="Your Spaces">
                {spaces.map((space) => (
                  <CommandItem
                    key={space.id}
                    onSelect={() => handleSpaceSelect(space.id)}
                    className="text-sm"
                  >
                    <Store className="mr-2 h-4 w-4 text-[#F5B800]" />
                    <span className="truncate">{space.name}</span>
                    {currentSpace?.id === space.id && (
                      <Check className="ml-auto h-4 w-4 text-[#F5B800]" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem onSelect={handleCreateSpace}>
                  <PlusCircle className="mr-2 h-4 w-4 text-black" />
                  Create Space
                </CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
