'use client'

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { useAppSelector } from "@/store/hooks"
import { formatPrice } from "@/utils/price-formatter"
import { formatCurrency } from "@/utils/currency"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"
import { Invoice, PaymentTransaction } from "@/services/types/models"
import { RootState } from "@/store"

const mpesaPaymentSchema = z.object({
  phoneNumber: z.string().min(10, {
    message: "Phone number must be at least 10 characters.",
  }),
  amount: z.number().min(1, {
    message: "Amount must be greater than 0.",
  }),
})

const cashPaymentSchema = z.object({
  amountGiven: z.number().min(1, {
    message: "Amount must be greater than 0.",
  }),
  receivedBy: z.string().min(2, {
    message: "Please enter who received the payment.",
  }),
})

interface PaymentFormProps {
  invoice: Invoice;
  onSuccess: (transaction: PaymentTransaction) => void;
  paymentIntegration?: {
    type: 'mpesa';
    paybillNumber?: string;
    tillNumber?: string;
  };
}

export function PaymentForm({ invoice, onSuccess, paymentIntegration }: PaymentFormProps) {
  const { currentSpace } = useAppSelector((state: RootState) => state.space);
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'mpesa' | 'cash'>('mpesa')

  const mpesaForm = useForm<z.infer<typeof mpesaPaymentSchema>>({
    resolver: zodResolver(mpesaPaymentSchema),
    defaultValues: {
      amount: invoice.total,
    },
  })

  const cashForm = useForm<z.infer<typeof cashPaymentSchema>>({
    resolver: zodResolver(cashPaymentSchema),
    defaultValues: {
      amountGiven: invoice.total,
    },
  })

  const formatInvoiceAmount = (amount: number) => {
    return formatPrice(amount, currentSpace);
  };

  async function onMpesaSubmit(data: z.infer<typeof mpesaPaymentSchema>) {
    try {
      setIsLoading(true)

      if (!paymentIntegration) {
        toast.error("M-PESA payment is not configured")
        return
      }

      // Here you would integrate with your payment service to initiate STK push
      // This is a placeholder for the actual implementation
      const response = await fetch('/api/payments/mpesa/stk-push', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: data.phoneNumber,
          amount: data.amount,
          invoiceId: invoice.id,
          paybillNumber: paymentIntegration.paybillNumber,
          tillNumber: paymentIntegration.tillNumber,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to initiate payment')
      }

      const result = await response.json()

      const transaction: PaymentTransaction = {
        id: result.transactionId,
        invoiceId: invoice.id,
        amount: data.amount,
        paymentMethod: 'mpesa',
        status: 'pending',
        mpesaDetails: {
          phoneNumber: data.phoneNumber,
          transactionCode: result.transactionCode,
          timestamp: new Date(),
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      onSuccess(transaction)
      setOpen(false)
      toast.success("Payment initiated. Please check your phone for the STK push.")
    } catch (error) {
      console.error('Error processing M-PESA payment:', error)
      toast.error("Failed to process payment")
    } finally {
      setIsLoading(false)
    }
  }

  async function onCashSubmit(data: z.infer<typeof cashPaymentSchema>) {
    try {
      setIsLoading(true)

      const transaction: PaymentTransaction = {
        id: Math.random().toString(36).substr(2, 9), // Replace with proper ID generation
        invoiceId: invoice.id,
        amount: invoice.total,
        paymentMethod: 'cash',
        status: 'completed',
        cashDetails: {
          amountGiven: data.amountGiven,
          change: data.amountGiven - invoice.total,
          receivedBy: data.receivedBy,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      onSuccess(transaction)
      setOpen(false)
      toast.success("Cash payment recorded successfully")
    } catch (error) {
      console.error('Error processing cash payment:', error)
      toast.error("Failed to record payment")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Record Payment</Button>
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] w-full sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Record Payment</DialogTitle>
          <DialogDescription>
            Record payment for invoice #{invoice.id}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label>Payment Method</Label>
            <Select
              value={paymentMethod}
              onValueChange={(value: 'mpesa' | 'cash') => setPaymentMethod(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                {paymentIntegration && (
                  <SelectItem value="mpesa">M-PESA</SelectItem>
                )}
                <SelectItem value="cash">Cash</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {paymentMethod === 'mpesa' ? (
            <Form {...mpesaForm}>
              <form onSubmit={mpesaForm.handleSubmit(onMpesaSubmit)} className="space-y-4">
                <FormField
                  control={mpesaForm.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter M-PESA phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={mpesaForm.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount (KES)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter amount"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Processing..." : "Send STK Push"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          ) : (
            <Form {...cashForm}>
              <form onSubmit={cashForm.handleSubmit(onCashSubmit)} className="space-y-4">
                <FormField
                  control={cashForm.control}
                  name="amountGiven"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount Given (KES)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter amount given"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="rounded-lg bg-muted p-3">
                  <div className="text-sm">
                    <div className="flex justify-between">
                      <span>Total Amount:</span>
                      <span>{formatInvoiceAmount(invoice.total)}</span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span>Change:</span>
                      <span>
                        {formatInvoiceAmount((cashForm.watch('amountGiven') || 0) - invoice.total)}
                      </span>
                    </div>
                  </div>
                </div>
                <FormField
                  control={cashForm.control}
                  name="receivedBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Received By</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter name of person receiving payment" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Recording..." : "Record Cash Payment"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}