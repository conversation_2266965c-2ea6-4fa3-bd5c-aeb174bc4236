import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { AlertCircle, RefreshCw, Home } from "lucide-react";
import Link from 'next/link';
import posthog from 'posthog-js';

interface Props {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  reportError?: boolean;
}

export function ErrorBoundary({ children, fallback, reportError = true }: Props) {
  const [error, setError] = useState<Error | null>(null);
  const [errorInfo, setErrorInfo] = useState<{ componentStack?: string } | null>(null);

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error("Error caught by boundary:", error);
      setError(error.error);
      
      // Report to analytics if enabled
      if (reportError && typeof posthog !== 'undefined') {
        posthog.capture('error_boundary_triggered', {
          error_message: error.error?.message || 'Unknown error',
          error_stack: error.error?.stack,
          url: window.location.href,
        });
      }
    };

    window.addEventListener("error", handleError);
    return () => window.removeEventListener("error", handleError);
  }, [reportError]);

  if (error) {
    // Use fallback if provided
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="text-center max-w-md border rounded-lg p-8 shadow-sm bg-white">
          <div className="flex justify-center">
            <AlertCircle className="h-16 w-16 text-red-500" />
          </div>
          <h1 className="mt-4 text-2xl font-bold tracking-tight">
            Something went wrong!
          </h1>
          <p className="mt-2 text-gray-600">
            {error.message || "An unexpected error occurred"}
          </p>
          
          <div className="mt-8 space-y-4">
            <Button
              className="w-full"
              onClick={() => {
                setError(null);
                setErrorInfo(null);
                window.location.reload();
              }}
            >
              <RefreshCw className="mr-2 h-4 w-4" /> Refresh Page
            </Button>
            
            <Link href="/" className="block">
              <Button variant="outline" className="w-full">
                <Home className="mr-2 h-4 w-4" /> Go to Home
              </Button>
            </Link>
          </div>
          
          {process.env.NODE_ENV !== 'production' && (
            <div className="mt-6 text-left">
              <details className="text-xs text-gray-500">
                <summary className="cursor-pointer hover:text-gray-700">Error details</summary>
                <pre className="mt-2 p-2 bg-gray-50 rounded overflow-x-auto">
                  {error.stack || error.message}
                </pre>
                {errorInfo?.componentStack && (
                  <pre className="mt-2 p-2 bg-gray-50 rounded overflow-x-auto">
                    {errorInfo.componentStack}
                  </pre>
                )}
              </details>
            </div>
          )}
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 