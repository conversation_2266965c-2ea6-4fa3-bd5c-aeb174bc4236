'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useRouter } from 'next/navigation';
import { CreateSpaceDialog } from '@/components/CreateSpaceDialog';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '@/utils/firebase';
import { toast } from 'sonner';

interface OnboardingStep {
  title: string;
  description: string;
  component: React.ReactNode;
}

const businessTypes = [
  { id: 'salon', label: 'Hair Salon' },
  { id: 'barbershop', label: 'Barbershop' },
  { id: 'spa', label: 'Spa & Wellness' },
  { id: 'nails', label: 'Nail Salon' },
  { id: 'beauty', label: 'Beauty Salon' },
  { id: 'other', label: 'Other' },
];

const referralSources = [
  { id: 'search', label: 'Search Engine' },
  { id: 'friend', label: 'Friend or Colleague' },
  { id: 'social', label: 'Social Media' },
  { id: 'ad', label: 'Advertisement' },
  { id: 'other', label: 'Other' },
];

export function OnboardingContainer() {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { user, authLoading } = useAppSelector((state) => state.auth);
  const [currentStep, setCurrentStep] = useState(0);
  const [showCreateSpace, setShowCreateSpace] = useState(false);
  const [formData, setFormData] = useState({
    businessType: '',
    referralSource: '',
  });

  // Redirect if no user, but only after auth state is confirmed (not loading)
  useEffect(() => {
    // Only redirect if auth is not in loading state and user is definitely null
    if (!authLoading && user === null) {
      console.log("No authenticated user found, redirecting to login");
      router.push('/login');
    } else if (user) {
      // Log authentication success for debugging
      console.log("User authenticated in onboarding:", user.uid);
    }
  }, [user, authLoading, router]);
  
  // Debug auth state changes
  useEffect(() => {
    console.log("Auth state in onboarding:", { 
      authLoading, 
      userAuthenticated: !!user,
      userID: user?.uid
    });
  }, [authLoading, user]);

  const steps: OnboardingStep[] = [
    {
      title: "What type of business are you setting up?",
      description: "This helps us tailor your experience.",
      component: (
        <div className="grid grid-cols-2 gap-4 mt-6">
          {businessTypes.map((type) => (
            <Button
              key={type.id}
              variant={formData.businessType === type.id ? "default" : "outline"}
              className="h-20 flex flex-col items-center justify-center gap-2"
              onClick={() => setFormData({ ...formData, businessType: type.id })}
            >
              {formData.businessType === type.id && (
                <CheckCircle className="h-4 w-4 text-white" />
              )}
              {type.label}
            </Button>
          ))}
        </div>
      ),
    },
    {
      title: "How did you hear about us?",
      description: "Help us understand how you found Groombook.",
      component: (
        <div className="grid grid-cols-2 gap-4 mt-6">
          {referralSources.map((source) => (
            <Button
              key={source.id}
              variant={formData.referralSource === source.id ? "default" : "outline"}
              className="h-20 flex flex-col items-center justify-center gap-2"
              onClick={() => setFormData({ ...formData, referralSource: source.id })}
            >
              {formData.referralSource === source.id && (
                <CheckCircle className="h-4 w-4 text-white" />
              )}
              {source.label}
            </Button>
          ))}
        </div>
      ),
    },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    if (!user) return;
    
    try {
      const timestamp = new Date().toISOString();
      
      // Save onboarding data directly to Firestore (user document)
      const userDoc = doc(db, 'users', user.uid);
      await setDoc(userDoc, {
        hasCompletedOnboarding: true,
        businessType: formData.businessType,
        referralSource: formData.referralSource,
        updatedAt: timestamp,
      }, { merge: true });
      
      // Save onboarding data to dedicated analytics collection
      const analyticsData = {
        userId: user.uid,
        email: user.email,
        displayName: user.displayName,
        businessType: formData.businessType,
        referralSource: formData.referralSource,
        completedAt: timestamp,
        userAgent: navigator.userAgent,
        // Add any additional fields useful for analytics
      };
      
      // Use a unique ID for the analytics record (user ID + timestamp)
      const onboardingAnalyticsDoc = doc(db, 'onboarding-analytics', `${user.uid}_${Date.now()}`);
      await setDoc(onboardingAnalyticsDoc, analyticsData);
      
      console.log("Onboarding analytics saved:", analyticsData);
    
      
      // Show CreateSpaceDialog after a short delay to ensure state updates
      setTimeout(() => {
        setShowCreateSpace(true);
        console.log("Showing space dialog");
      }, 100);
    } catch (error) {
      console.error('Error saving onboarding data:', error);
      toast.error("There was an error saving your preferences. Please try again.");
    }
  };

  const handleSpaceCreated = () => {
    console.log("Space created, preparing for redirect...");
    
    // Make sure user session is available before redirecting
    if (user) {
      console.log("User authenticated, proceeding with redirect");
      setShowCreateSpace(false);
      
      // Add a longer delay before redirecting to ensure state updates and session is preserved
      setTimeout(() => {
        // Use router.replace instead of push to avoid issues with the history stack
        console.log("Redirecting to dashboard...");
        router.replace('/dashboard');
      }, 300);
    } else {
      console.error("User not authenticated when trying to redirect after space creation");
      // If somehow the user is not authenticated, show an error
      toast.error("Authentication error. Please try refreshing the page.");
    }
  };

  const canProceed = 
    (currentStep === 0 && formData.businessType) ||
    (currentStep === 1 && formData.referralSource);

  return (
    <>
      <div className="min-h-screen flex items-center justify-center bg-muted/50 p-4">
        <Card className="w-full max-w-2xl p-8">
          <div className="space-y-8">
            <div className="text-center space-y-2">
              <h1 className="text-3xl font-bold">Welcome to GroomBook! 👋</h1>
              <p className="text-muted-foreground">
                Let's get your business set up in just a few steps
              </p>
            </div>

            {/* Progress Indicator */}
            <div className="flex justify-center gap-3 mb-8">
              {steps.map((_, index) => (
                <motion.div
                  key={index}
                  className={`h-1 w-12 rounded-full ${
                    index <= currentStep ? 'bg-primary' : 'bg-muted'
                  }`}
                  initial={false}
                  animate={{
                    backgroundColor: index <= currentStep ? 'hsl(var(--primary))' : 'hsl(var(--muted))',
                  }}
                />
              ))}
            </div>

            {/* Step Content */}
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-4"
            >
              <h2 className="text-xl font-semibold text-center">
                {steps[currentStep].title}
              </h2>
              <p className="text-muted-foreground text-center">
                {steps[currentStep].description}
              </p>
              {steps[currentStep].component}
            </motion.div>

            {/* Navigation */}
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 0}
              >
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!canProceed}
              >
                {currentStep === steps.length - 1 ? 'Complete' : 'Next'}
              </Button>
            </div>
          </div>
        </Card>
      </div>

      {showCreateSpace && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-background rounded-lg shadow-lg w-[95vw] max-w-2xl flex flex-col overflow-scroll">
            <CreateSpaceDialog 
              onClose={handleSpaceCreated}
            />
          </div>
        </div>
      )}
    </>
  );
}
