'use client';

import { useState, useEffect } from 'react';
import { useAppSelector } from '@/store/hooks';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Plus, Minus, Package, DollarSign, User, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { transactionService, PaymentRecordInput } from '@/services/transactionService';
import { serviceProductService, InventoryItem, ProductUsageInput } from '@/services/serviceProductService';
import { ActiveService } from '@/services/types/models';

interface ManualPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  service: ActiveService | null;
  onPaymentRecorded: () => void;
}

export function ManualPaymentDialog({
  open,
  onOpenChange,
  service,
  onPaymentRecorded,
}: ManualPaymentDialogProps) {
  const { user } = useAppSelector((state) => state.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'mpesa' | 'bank_transfer' | 'card'>('cash');
  const [notes, setNotes] = useState('');
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [productUsages, setProductUsages] = useState<ProductUsageInput[]>([]);

  // Load inventory when dialog opens
  useEffect(() => {
    if (open && service) {
      loadInventory();
    }
  }, [open, service]);

  const loadInventory = async () => {
    if (!service) return;
    
    try {
      const items = await serviceProductService.getSpaceInventory(service.spaceId);
      setInventory(items);
    } catch (error) {
      console.error('Error loading inventory:', error);
      toast.error('Failed to load inventory items');
    }
  };

  const addProductUsage = () => {
    setProductUsages([...productUsages, { productId: '', quantityUsed: 1 }]);
  };

  const removeProductUsage = (index: number) => {
    setProductUsages(productUsages.filter((_, i) => i !== index));
  };

  const updateProductUsage = (index: number, field: keyof ProductUsageInput, value: any) => {
    const updated = [...productUsages];
    updated[index] = { ...updated[index], [field]: value };
    setProductUsages(updated);
  };

  const getProductCost = (productId: string, quantity: number): number => {
    const product = inventory.find(item => item.id === productId);
    return product ? product.costPerUnit * quantity : 0;
  };

  const getTotalProductCost = (): number => {
    return productUsages.reduce((total, usage) => {
      return total + getProductCost(usage.productId, usage.quantityUsed);
    }, 0);
  };

  const handleRecordPayment = async () => {
    if (!service || !user) {
      toast.error('Missing required information');
      return;
    }

    // Validate product usages
    const validProductUsages = productUsages.filter(usage => 
      usage.productId && usage.quantityUsed > 0
    );

    try {
      setIsLoading(true);

      const paymentInput: PaymentRecordInput = {
        serviceId: service.id,
        paymentMethod,
        productUsages: validProductUsages.length > 0 ? validProductUsages : undefined,
        notes: notes.trim() || undefined,
        recordedBy: user.uid,
      };

      await transactionService.recordPayment(paymentInput);

      toast.success('Payment recorded successfully');
      onPaymentRecorded();
      onOpenChange(false);
      
      // Reset form
      setPaymentMethod('cash');
      setNotes('');
      setProductUsages([]);
    } catch (error) {
      console.error('Error recording payment:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to record payment');
    } finally {
      setIsLoading(false);
    }
  };

  if (!service) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Record Payment
          </DialogTitle>
          <DialogDescription>
            Record payment for completed service and track product usage
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Service Details */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Service Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{service.customerName}</span>
                </div>
                <Badge variant="outline">{service.staffName}</Badge>
              </div>
              
              <div className="space-y-2">
                {service.services.map((svc, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span>{svc.name}</span>
                    <span className="font-medium">KES {svc.price}</span>
                  </div>
                ))}
              </div>
              
              <Separator />
              
              <div className="flex justify-between font-medium">
                <span>Total Amount</span>
                <span>KES {service.totalAmount}</span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Method */}
          <div className="space-y-2">
            <Label>Payment Method</Label>
            <Select value={paymentMethod} onValueChange={(value: any) => setPaymentMethod(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="mpesa">M-PESA</SelectItem>
                <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                <SelectItem value="card">Card</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Product Usage */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Product Usage (Optional)
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addProductUsage}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Product
              </Button>
            </div>

            {productUsages.map((usage, index) => (
              <Card key={index}>
                <CardContent className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Product</Label>
                      <Select
                        value={usage.productId}
                        onValueChange={(value) => updateProductUsage(index, 'productId', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {inventory.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              {item.name} (Available: {item.quantity})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Quantity Used</Label>
                      <Input
                        type="number"
                        min="1"
                        value={usage.quantityUsed}
                        onChange={(e) => updateProductUsage(index, 'quantityUsed', parseInt(e.target.value) || 1)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Cost</Label>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          KES {getProductCost(usage.productId, usage.quantityUsed).toFixed(2)}
                        </span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeProductUsage(index)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {productUsages.length > 0 && (
              <div className="flex justify-between text-sm font-medium">
                <span>Total Product Cost:</span>
                <span>KES {getTotalProductCost().toFixed(2)}</span>
              </div>
            )}
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label>Notes (Optional)</Label>
            <Textarea
              placeholder="Add any additional notes about this payment..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleRecordPayment} disabled={isLoading}>
            {isLoading ? 'Recording...' : 'Record Payment'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
