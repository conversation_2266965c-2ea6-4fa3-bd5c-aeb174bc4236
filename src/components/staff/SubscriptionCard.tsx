'use client';

import { useState } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { updateStaff } from '@/store/slices/staffSlice';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import { Staff } from '@/services/types/models';
import { toast } from 'sonner';

interface PlanPricing {
  monthly: {
    price: number;
    features: string[];
  };
  yearly: {
    price: number;
    features: string[];
    savings: number;
  };
}

const planPricing: PlanPricing = {
  monthly: {
    price: 29.99,
    features: [
      'Full profile customization',
      'Service management',
      'Performance analytics',
      'Client reviews',
      'Priority support',
    ],
  },
  yearly: {
    price: 299.99,
    features: [
      'Everything in monthly plan',
      '2 months free ($60 value)',
      'Advanced analytics',
      'Priority support',
      'Early access to new features',
    ],
    savings: 60, // 2 months free
  },
};

interface SubscriptionCardProps {
  staff: Staff | null;
}

export function SubscriptionCard({ staff }: SubscriptionCardProps) {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubscribe = async (plan: 'monthly' | 'yearly') => {
    if (!staff) return;

    setIsLoading(true);
    try {
      // Calculate subscription dates
      const startDate = new Date().toISOString();
      const endDate = new Date();
      
      // If yearly plan, add 14 months (12 + 2 free)
      if (plan === 'yearly') {
        endDate.setMonth(endDate.getMonth() + 14);
      } else {
        endDate.setMonth(endDate.getMonth() + 1);
      }

      const subscription = {
        plan,
        startDate,
        endDate: endDate.toISOString(),
        status: 'active' as const,
        isTrial: false,
      };

      await dispatch(updateStaff({ 
        id: staff.id, 
        data: { subscription } 
      })).unwrap();

      toast.success(`Successfully subscribed to \${plan} plan`);
    } catch (error) {
      console.error('Subscription error:', error);
      toast.error('Failed to process subscription');
    } finally {
      setIsLoading(false);
    }
  };

  if (!staff) return null;

  const currentPlan = staff.subscription?.plan;
  const isExpired = staff.subscription?.status === 'expired';

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Plan</CardTitle>
        <CardDescription>Choose the plan that best fits your needs</CardDescription>
      </CardHeader>
      <CardContent>
        {isExpired && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertTitle className="text-red-600">Subscription Expired</AlertTitle>
            <AlertDescription className="text-red-600">
              Your subscription has expired. Please renew to continue accessing all features.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6 md:grid-cols-2">
          {/* Monthly Plan */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Plan</CardTitle>
              <CardDescription>
                Flexible month-to-month billing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-3xl font-bold">${planPricing.monthly.price}/mo</div>
              <ul className="space-y-2 text-sm">
                {planPricing.monthly.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="mr-2 h-4 w-4 text-green-500"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
              <Button
                className="w-full"
                onClick={() => handleSubscribe('monthly')}
                disabled={isLoading || currentPlan === 'monthly'}
              >
                {currentPlan === 'monthly'
                  ? 'Current Plan'
                  : isLoading
                  ? 'Processing...'
                  : 'Subscribe Monthly'}
              </Button>
            </CardContent>
          </Card>

          {/* Yearly Plan */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle>Yearly Plan</CardTitle>
              <CardDescription>
                Save with annual billing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-3xl font-bold">${planPricing.yearly.price}/yr</div>
                <p className="text-sm text-green-600">
                  Save ${planPricing.yearly.savings} with 2 months free!
                </p>
              </div>
              <ul className="space-y-2 text-sm">
                {planPricing.yearly.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="mr-2 h-4 w-4 text-green-500"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
              <Button
                className="w-full"
                variant="default"
                onClick={() => handleSubscribe('yearly')}
                disabled={isLoading || currentPlan === 'yearly'}
              >
                {currentPlan === 'yearly'
                  ? 'Current Plan'
                  : isLoading
                  ? 'Processing...'
                  : 'Subscribe Yearly'}
              </Button>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
}
