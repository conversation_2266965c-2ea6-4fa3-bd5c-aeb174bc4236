'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  Clock, 
  RefreshCw,
  CheckCircle,
  AlertCircle 
} from 'lucide-react';
import { commissionService } from '@/services/commissionService';
import { transactionService } from '@/services/transactionService';
import { CommissionRecord, ServiceTransaction, StaffEarnings } from '@/services/types/models';
import { toast } from 'sonner';
import { format, startOfMonth, endOfMonth, subMonths } from 'date-fns';

interface EarningsTabProps {
  staffId: string;
  spaceId: string;
}

export function EarningsTab({ staffId, spaceId }: EarningsTabProps) {
  const [earnings, setEarnings] = useState<StaffEarnings | null>(null);
  const [commissions, setCommissions] = useState<CommissionRecord[]>([]);
  const [transactions, setTransactions] = useState<ServiceTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('current_month');

  useEffect(() => {
    fetchEarningsData();
  }, [staffId, selectedPeriod]);

  const getPeriodDates = () => {
    const now = new Date();
    
    switch (selectedPeriod) {
      case 'current_month':
        return {
          start: startOfMonth(now).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
      case 'last_month':
        const lastMonth = subMonths(now, 1);
        return {
          start: startOfMonth(lastMonth).toISOString(),
          end: endOfMonth(lastMonth).toISOString(),
        };
      case 'last_3_months':
        const threeMonthsAgo = subMonths(now, 3);
        return {
          start: startOfMonth(threeMonthsAgo).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
      default:
        return {
          start: startOfMonth(now).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
    }
  };

  const fetchEarningsData = async () => {
    try {
      setIsLoading(true);
      const { start, end } = getPeriodDates();

      // Fetch earnings summary
      const earningsData = await commissionService.getStaffEarnings(staffId, start, end);
      setEarnings(earningsData);

      // Fetch commission records
      const commissionsData = await commissionService.getStaffCommissions(staffId, start, end);
      setCommissions(commissionsData);

      // Fetch transactions
      const transactionsData = await transactionService.getStaffTransactions(staffId, start, end);
      setTransactions(transactionsData);

    } catch (error) {
      console.error('Error fetching earnings data:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setIsLoading(false);
    }
  };

  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case 'current_month':
        return format(new Date(), 'MMMM yyyy');
      case 'last_month':
        return format(subMonths(new Date(), 1), 'MMMM yyyy');
      case 'last_3_months':
        return 'Last 3 Months';
      default:
        return 'Current Month';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Period Selection */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Earnings</h2>
          <p className="text-muted-foreground">Track your commissions and performance</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current_month">Current Month</SelectItem>
              <SelectItem value="last_month">Last Month</SelectItem>
              <SelectItem value="last_3_months">Last 3 Months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={fetchEarningsData}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Commissions</p>
                <p className="text-2xl font-bold">KES {earnings?.totalCommissions.toFixed(2) || '0.00'}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Paid Commissions</p>
                <p className="text-2xl font-bold text-green-600">KES {earnings?.paidCommissions.toFixed(2) || '0.00'}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Commissions</p>
                <p className="text-2xl font-bold text-orange-600">KES {earnings?.pendingCommissions.toFixed(2) || '0.00'}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Service Breakdown */}
      {earnings && earnings.serviceBreakdown.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Service Performance - {getPeriodLabel()}</CardTitle>
            <CardDescription>
              Breakdown of your services and earnings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {earnings.serviceBreakdown.map((service, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{service.serviceName}</h4>
                    <p className="text-sm text-muted-foreground">
                      {service.count} service{service.count !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">KES {service.commissions.toFixed(2)}</p>
                    <p className="text-sm text-muted-foreground">
                      Revenue: KES {service.revenue.toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Commissions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Commissions</CardTitle>
          <CardDescription>
            Your latest commission records
          </CardDescription>
        </CardHeader>
        <CardContent>
          {commissions.length === 0 ? (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No commissions found for this period</p>
            </div>
          ) : (
            <div className="space-y-4">
              {commissions.slice(0, 10).map((commission) => (
                <div key={commission.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{commission.serviceName}</h4>
                      <Badge 
                        variant={commission.status === 'paid' ? 'default' : 'secondary'}
                      >
                        {commission.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(commission.createdAt), 'MMM d, yyyy')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">KES {commission.commissionAmount.toFixed(2)}</p>
                    <p className="text-sm text-muted-foreground">
                      {commission.commissionRate}% of KES {commission.serviceAmount}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
