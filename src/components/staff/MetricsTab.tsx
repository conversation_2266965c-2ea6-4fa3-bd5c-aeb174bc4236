'use client';

import { useState, useEffect } from 'react';
import { Staff } from '@/services/types/models';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { AlertCircle, TrendingUp, Users, Star } from 'lucide-react';
import { staffPortalService, StaffMetrics } from '@/services/staffPortalService';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

interface MetricsTabProps {
  staff: Staff | null;
}

export function MetricsTab({ staff }: MetricsTabProps) {
  const [timeRange, setTimeRange] = useState('this-month');
  const [metrics, setMetrics] = useState<StaffMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadMetrics = async () => {
    if (!staff?.id) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const staffMetrics = await staffPortalService.getStaffMetrics(staff.id, timeRange);
      setMetrics(staffMetrics);
    } catch (err) {
      console.error('Error loading staff metrics:', err);
      setError('Failed to load metrics. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load metrics when time range or staff changes
  useEffect(() => {
    loadMetrics();
  }, [timeRange, staff?.id]);

  if (!staff) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-muted-foreground">
            <AlertCircle className="h-5 w-5" />
            <p>Unable to load staff metrics.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center gap-4 py-8 text-center">
            <AlertCircle className="h-10 w-10 text-destructive" />
            <div>
              <p className="font-semibold text-lg">Error Loading Metrics</p>
              <p className="text-muted-foreground">{error}</p>
            </div>
            <Button onClick={loadMetrics}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <Select
          value={timeRange}
          onValueChange={setTimeRange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="this-week">This Week</SelectItem>
            <SelectItem value="this-month">This Month</SelectItem>
            <SelectItem value="last-month">Last Month</SelectItem>
            <SelectItem value="last-3-months">Last 3 Months</SelectItem>
            <SelectItem value="all-time">All Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        {/* Earnings Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">
                  ${metrics?.totalEarnings.toFixed(2) || '0.00'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {metrics?.completedAppointments || 0} completed appointments
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Appointments Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Appointments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{metrics?.totalAppointments || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {((metrics?.completedAppointments || 0) / (metrics?.totalAppointments || 1) * 100).toFixed(1)}% completion rate
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Rating Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{metrics?.averageRating?.toFixed(1) || 'N/A'}</div>
                <p className="text-xs text-muted-foreground">
                  Based on {metrics?.recentReviews.length || 0} reviews
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Services Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Services Breakdown</CardTitle>
          <CardDescription>Performance metrics by service</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Service</TableHead>
                  <TableHead>Appointments</TableHead>
                  <TableHead className="text-right">Earnings</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {metrics?.serviceBreakdown.map((service) => (
                  <TableRow key={service.name}>
                    <TableCell className="font-medium">{service.name}</TableCell>
                    <TableCell>{service.count}</TableCell>
                    <TableCell className="text-right">${service.earnings.toFixed(2)}</TableCell>
                  </TableRow>
                ))}
                {(!metrics?.serviceBreakdown || metrics.serviceBreakdown.length === 0) && (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center text-muted-foreground">
                      No data available
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Recent Reviews */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Reviews</CardTitle>
          <CardDescription>Latest feedback from your clients</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          ) : (
            <div className="space-y-4">
              {metrics?.recentReviews.map((review) => (
                <div key={review.id} className="border-b pb-4 last:border-0 last:pb-0">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-semibold">{review.customerName}</p>
                      <p className="text-sm text-muted-foreground">{review.date}</p>
                    </div>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 fill-current text-yellow-400" />
                      <span className="ml-1">{review.rating}</span>
                    </div>
                  </div>
                  <p className="mt-2 text-sm">{review.comment}</p>
                </div>
              ))}
              {(!metrics?.recentReviews || metrics.recentReviews.length === 0) && (
                <div className="text-center text-muted-foreground">
                  No reviews yet
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
