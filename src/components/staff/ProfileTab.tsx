'use client';

import { useState } from 'react';
import { Staff } from '@/services/types/models';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { useAppDispatch } from '@/store/hooks';
import { updateStaff } from '@/store/slices/staffSlice';
import { staffPortalService } from '@/services/staffPortalService';

const staffProfileSchema = z.object({
  displayName: z.string().min(2, 'Name must be at least 2 characters'),
  bio: z.string().optional(),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  experience: z.object({
    years: z.number().min(0, 'Years must be 0 or greater'),
    since: z.string(),
    previousWorkplaces: z.array(z.string()).optional()
  }).optional(),
  certifications: z.array(z.string())
});

type StaffProfileFormData = z.infer<typeof staffProfileSchema>;

interface ProfileTabProps {
  staff: Staff | null;
}

export function ProfileTab({ staff }: ProfileTabProps) {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [photoURL, setPhotoURL] = useState(staff?.photoURL);

  const form = useForm<StaffProfileFormData>({
    resolver: zodResolver(staffProfileSchema),
    defaultValues: {
      displayName: staff?.displayName || '',
      bio: staff?.bio || '',
      experience: staff?.experience || { years: 0, since: new Date().getFullYear().toString(), previousWorkplaces: [] },
      certifications: staff?.certifications || []
    }
  });

  const onSubmit = async (data: StaffProfileFormData) => {
    if (!staff?.id) return;
    
    try {
      setIsLoading(true);
      
      // Update profile using staffPortalService
      const success = await staffPortalService.updateStaffProfile(staff.id, {
        displayName: data.displayName,
        bio: data.bio,
        phoneNumber: data.phoneNumber,
        experience: data.experience,
        certifications: data.certifications,
        photoURL: photoURL,
      });
      
      if (!success) {
        throw new Error('Failed to update profile');
      }
      
      // Update Redux store
      await dispatch(updateStaff({
        id: staff.id,
        data: {
          displayName: data.displayName,
          bio: data.bio,
          phoneNumber: data.phoneNumber,
          experience: data.experience,
          certifications: data.certifications,
          photoURL: photoURL,
        }
      }));
      
      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // TODO: Implement photo upload logic
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoURL(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>My Profile</CardTitle>
        <CardDescription>Manage your personal information and expertise</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Profile Photo */}
            <div className="flex items-center gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={photoURL || ''} />
                <AvatarFallback>
                  {staff?.displayName?.split(' ').map(n => n[0]).join('') || 'ST'}
                </AvatarFallback>
              </Avatar>
              <div>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => document.getElementById('photo-upload')?.click()}
                >
                  Change Photo
                </Button>
                <input
                  id="photo-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handlePhotoUpload}
                />
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid gap-4">
              <FormField
                control={form.control}
                name="displayName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Your full name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bio</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Tell clients about yourself and your expertise..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Your contact number" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Experience and Certifications */}
            <div className="grid gap-4">
              <FormField
                control={form.control}
                name="experience.years"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Years of Experience</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={e => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <FormLabel>Certifications</FormLabel>
                <div className="flex flex-wrap gap-2 mt-2">
                  {staff?.certifications?.map((cert, index) => (
                    <Badge key={index} variant="secondary">
                      {cert}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
