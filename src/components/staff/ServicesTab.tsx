'use client';

import { useState, useEffect } from 'react';
import { Staff, Service } from '@/services/types/models';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { toast } from 'sonner';
import { AlertCircle, Loader2 } from 'lucide-react';
import { serviceService } from '@/services/firestore';
import { useAppDispatch } from '@/store/hooks';
import { updateStaff } from '@/store/slices/staffSlice';

interface ServicesTabProps {
  staff: Staff | null;
}

export function ServicesTab({ staff }: ServicesTabProps) {
  const dispatch = useAppDispatch();
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [updatingService, setUpdatingService] = useState<string | null>(null);

  useEffect(() => {
    async function loadServices() {
      if (!staff) return;
      
      try {
        setIsLoading(true);
        // Load services for the staff's space
        const spaceServices = await serviceService.getBySpace(staff.spaceId);
        setServices(spaceServices);
      } catch (error) {
        console.error('Error loading services:', error);
        toast.error('Failed to load services');
      } finally {
        setIsLoading(false);
      }
    }

    loadServices();
  }, [staff]);

  const handleServiceToggle = async (serviceId: string, enabled: boolean) => {
    if (!staff?.id) return;
    
    try {
      setUpdatingService(serviceId);
      
      // Create a new array of services based on the toggle action
      let updatedServices = [...staff.services];
      
      if (enabled && !updatedServices.includes(serviceId)) {
        // Add service
        updatedServices.push(serviceId);
      } else if (!enabled && updatedServices.includes(serviceId)) {
        // Remove service
        updatedServices = updatedServices.filter(id => id !== serviceId);
      }
      
      // Update staff in database and Redux
      await dispatch(updateStaff({
        id: staff.id,
        data: { services: updatedServices }
      })).unwrap();

      toast.success(`Service ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error toggling service:', error);
      toast.error('Failed to update service');
    } finally {
      setUpdatingService(null);
    }
  };

  if (!staff) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-muted-foreground">
            <AlertCircle className="h-5 w-5" />
            <p>Unable to load staff services.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex justify-center items-center py-6">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>My Services</CardTitle>
        <CardDescription>Manage the services you offer to clients</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Service</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Enable/Disable</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {services.map((service) => (
              <TableRow key={service.id}>
                <TableCell className="font-medium">{service.name}</TableCell>
                <TableCell>{service.duration} mins</TableCell>
                <TableCell>\${service.price.toFixed(2)}</TableCell>
                <TableCell>
                  <Badge variant={staff.services.includes(service.id) ? "default" : "secondary"}>
                    {staff.services.includes(service.id) ? "Active" : "Inactive"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Switch
                    checked={staff.services.includes(service.id)}
                    onCheckedChange={(checked) => handleServiceToggle(service.id, checked)}
                    disabled={updatingService === service.id}
                  />
                </TableCell>
              </TableRow>
            ))}
            {services.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-muted-foreground">
                  No services available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
