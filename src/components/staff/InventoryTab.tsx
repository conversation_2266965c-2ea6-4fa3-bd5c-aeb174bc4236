'use client';

import { useState, useEffect } from 'react';
import { Staff } from '@/services/types/models';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { AlertCircle, PackagePlus, X } from 'lucide-react';
import { db } from '@/utils/firebase';
import { collection, query, where, getDocs, updateDoc, doc, addDoc } from 'firebase/firestore';

interface InventoryItem {
  id: string;
  name: string;
  type: string;
  quantity: number;
  thresholdLow: number;
  thresholdCritical: number;
  status?: "ok" | "low" | "critical";
  lastUpdated: string;
  spaceId: string;
  notes?: string;
}

interface InventoryTabProps {
  staff: Staff | null;
}

export function InventoryTab({ staff }: InventoryTabProps) {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [updating, setUpdating] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [noteInput, setNoteInput] = useState<{[key: string]: string}>({});

  // Load inventory items when staff changes
  useEffect(() => {
    // Make sure staff is not null before accessing properties
    if (!staff) return;
    const spaceId = staff.spaceId;
    if (!spaceId) return;
    
    async function loadInventory() {
      try {
        setIsLoading(true);
        // Now we're safe to use spaceId since we've verified it's not null/undefined
        const q = query(collection(db, "inventory"), where("spaceId", "==", spaceId));
        const snapshot = await getDocs(q);
        const data = snapshot.docs.map(doc => ({ 
          id: doc.id, 
          ...doc.data(),
          status: getStatus(
            doc.data().quantity, 
            doc.data().thresholdLow, 
            doc.data().thresholdCritical
          )
        })) as InventoryItem[];
        
        setItems(data);
      } catch (error) {
        console.error('Error loading inventory:', error);
        toast.error('Failed to load inventory items');
      } finally {
        setIsLoading(false);
      }
    }
    
    loadInventory();
  }, [staff]);

  // Helper function to determine item status
  const getStatus = (quantity: number, low: number, critical: number): "ok" | "low" | "critical" => {
    if (quantity <= critical) return "critical";
    if (quantity <= low) return "low";
    return "ok";
  };

  // Helper function for status badge styling
  const getStatusColor = (status: string) => {
    if (status === "critical") return "bg-red-100 text-red-700";
    if (status === "low") return "bg-yellow-100 text-yellow-700";
    return "bg-green-100 text-green-700";
  };

  // Update item quantity
  const handleUpdateQuantity = async (id: string, quantity: number, item: InventoryItem) => {
    // Make sure staff is not null and has a spaceId before proceeding
    if (!staff || !staff.spaceId) return;
    
    setUpdating(id);
    try {
      const status = getStatus(quantity, item.thresholdLow, item.thresholdCritical);
      const itemRef = doc(db, "inventory", id);
      await updateDoc(itemRef, { 
        quantity, 
        status, 
        lastUpdated: new Date().toISOString() 
      });
      
      // Update local state
      setItems(items => items.map(i => 
        i.id === id ? { ...i, quantity, status } : i
      ));
      
      toast.success("Quantity updated");
    } catch (error) {
      console.error('Error updating quantity:', error);
      toast.error("Failed to update quantity");
    } finally {
      setUpdating(null);
    }
  };

  // Add a note to an inventory item
  const handleAddNote = async (id: string) => {
    // Make sure staff is not null and has a spaceId before proceeding
    // Also ensure the note input exists and is not empty
    if (!staff || !staff.spaceId || !noteInput[id]?.trim()) return;
    
    const note = noteInput[id].trim();
    setUpdating(id);
    
    try {
      const itemRef = doc(db, "inventory", id);
      await updateDoc(itemRef, { 
        notes: note,
        lastUpdated: new Date().toISOString() 
      });
      
      // Update local state
      setItems(items => items.map(i => 
        i.id === id ? { ...i, notes: note } : i
      ));
      
      // Clear input
      setNoteInput({...noteInput, [id]: ''});
      
      toast.success("Note added");
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error("Failed to add note");
    } finally {
      setUpdating(null);
    }
  };

  // Filter items by status and search term
  const filteredItems = items.filter(item => {
    // First filter by status if a status filter is selected
    if (filterStatus && item.status !== filterStatus) return false;
    
    // Then filter by search term if one exists
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return (
        item.name.toLowerCase().includes(term) ||
        item.type.toLowerCase().includes(term)
      );
    }
    
    return true;
  });

  if (!staff) {
    return (
      <div className="flex justify-center items-center p-8">
        <p>Staff information not available.</p>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Inventory Management</span>
        </CardTitle>
        <CardDescription>
          Update product quantities and report product status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex flex-wrap gap-3 justify-between">
          {/* Search and filter controls */}
          <div className="flex gap-2 flex-wrap">
            <Input
              placeholder="Search inventory..."
              className="w-[250px]"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
            <div className="flex gap-1">
              <Button 
                variant={filterStatus === null ? "default" : "outline"} 
                size="sm"
                onClick={() => setFilterStatus(null)}
              >
                All
              </Button>
              <Button 
                variant={filterStatus === "low" ? "default" : "outline"} 
                className={filterStatus === "low" ? "" : "text-yellow-600"}
                size="sm"
                onClick={() => setFilterStatus("low")}
              >
                Low Stock
              </Button>
              <Button 
                variant={filterStatus === "critical" ? "default" : "outline"} 
                className={filterStatus === "critical" ? "" : "text-red-600"}
                size="sm"
                onClick={() => setFilterStatus("critical")}
              >
                Critical
              </Button>
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="space-y-3">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <div className="bg-blue-50 p-4 rounded-full">
              <PackagePlus className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-4 space-y-1">
              <h3 className="font-medium text-lg">No inventory items found</h3>
              <p className="text-muted-foreground text-sm">
                {searchTerm || filterStatus 
                  ? "Try a different search term or filter" 
                  : "Inventory items will appear here when they're added"}
              </p>
            </div>
          </div>
        ) : (
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Notes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.map(item => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{item.type}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(item.status || 'ok')}`}>
                        {(item.status || 'OK').toUpperCase()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          className="w-20"
                          value={item.quantity}
                          min={0}
                          onChange={e => handleUpdateQuantity(item.id, Number(e.target.value), item)}
                          disabled={updating === item.id}
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Input
                          placeholder="Add note..."
                          className="w-[200px]"
                          value={noteInput[item.id] || ''}
                          onChange={e => setNoteInput({...noteInput, [item.id]: e.target.value})}
                          disabled={updating === item.id}
                        />
                        <Button 
                          size="sm" 
                          onClick={() => handleAddNote(item.id)}
                          disabled={!noteInput[item.id]?.trim() || updating === item.id}
                        >
                          Add
                        </Button>
                        {item.notes && (
                          <div className="ml-2 text-sm text-muted-foreground">
                            {item.notes}
                          </div>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="mt-4 flex items-center gap-2 text-sm text-muted-foreground">
              <AlertCircle className="h-4 w-4" />
              <p>
                When a product quantity becomes low or runs out, update it here. 
                Add helpful notes about product condition or needs.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
