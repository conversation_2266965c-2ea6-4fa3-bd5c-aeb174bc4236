'use client';

import { useState } from 'react';
import { Customer } from '@/services/types/models';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';
import { 
  Share2, 
  Copy, 
  Mail, 
  MessageSquare,
  AlertCircle
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';

interface ReferralSystemProps {
  customer: Customer;
  onReferralSent?: () => void;
}

export function ReferralSystem({ customer, onReferralSent }: ReferralSystemProps) {
  return (
    <FeatureGate
      feature={RestrictedFeature.REFERRAL_SYSTEM}
      title="Referral System"
      description="Grow your business through customer referrals"
    >
      <ReferralSystemContent customer={customer} onReferralSent={onReferralSent} />
    </FeatureGate>
  );
}

function ReferralSystemContent({ customer, onReferralSent }: ReferralSystemProps) {
  const [isSending, setIsSending] = useState(false);
  const [referralEmail, setReferralEmail] = useState('');
  const [referralMessage, setReferralMessage] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const referralCode = customer.loyaltyPoints?.referralCode || '';
  const referralUrl = `${window.location.origin}/referral/${referralCode}`;
  
  if (!referralCode) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referral Program</CardTitle>
          <CardDescription>Share with friends and earn rewards</CardDescription>
        </CardHeader>
        <CardContent className="text-center py-6">
          <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Not enrolled in loyalty program</h3>
          <p className="text-muted-foreground mb-4">
            The customer needs to be enrolled in the loyalty program to participate in referrals.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralUrl)
      .then(() => {
        toast.success('Referral link copied to clipboard');
      })
      .catch((error) => {
        console.error('Failed to copy referral link:', error);
        toast.error('Failed to copy referral link');
      });
  };
  
  const shareReferral = async () => {
    if (!referralEmail) {
      toast.error('Please enter an email address');
      return;
    }
    
    setIsSending(true);
    
    try {
      // Send referral invitation
      await fetch('/api/send-referral', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: referralEmail,
          message: referralMessage || `Hi, I thought you might enjoy services at my space. Use my referral code ${referralCode} to get a welcome bonus!`,
          senderName: customer.displayName,
          referralCode,
          referralUrl
        }),
      });
      
      toast.success('Referral invitation sent!');
      setReferralEmail('');
      setReferralMessage('');
      setIsDialogOpen(false);
      
      if (onReferralSent) {
        onReferralSent();
      }
    } catch (error) {
      console.error('Error sending referral:', error);
      toast.error('Failed to send referral invitation');
    } finally {
      setIsSending(false);
    }
  };
  
  const shareViaApp = (app: 'whatsapp' | 'messenger') => {
    const message = encodeURIComponent(`Join me at this fantastic space! Use my referral code ${referralCode} or sign up with this link: ${referralUrl}`);
    
    let url = '';
    if (app === 'whatsapp') {
      url = `https://wa.me/?text=${message}`;
    } else if (app === 'messenger') {
      url = `https://www.facebook.com/dialog/send?link=${encodeURIComponent(referralUrl)}&app_id=YOUR_FACEBOOK_APP_ID&redirect_uri=${encodeURIComponent(window.location.origin)}`;
    }
    
    if (url) {
      window.open(url, '_blank');
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Referral Program</CardTitle>
        <CardDescription>Share with friends and earn rewards</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="bg-muted p-4 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium">Your Referral Code</p>
            <Badge variant="outline" className="text-lg font-mono">
              {referralCode}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2 mt-4">
            <Input 
              value={referralUrl}
              readOnly
              className="font-mono text-sm"
            />
            <Button
              variant="outline"
              size="icon"
              onClick={copyReferralLink}
              title="Copy referral link"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Share Your Referral</h3>
          <p className="text-sm text-muted-foreground mb-4">
            For each friend who joins using your referral code, you'll both receive loyalty points.
          </p>
          
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" className="w-full" onClick={() => shareViaApp('whatsapp')}>
              <MessageSquare className="mr-2 h-4 w-4" />
              WhatsApp
            </Button>
            <Button variant="outline" className="w-full" onClick={() => shareViaApp('messenger')}>
              <MessageSquare className="mr-2 h-4 w-4" />
              Messenger
            </Button>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="w-full col-span-2">
                  <Mail className="mr-2 h-4 w-4" />
                  Send Email Invitation
                </Button>
              </DialogTrigger>
              
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Send Referral Invitation</DialogTitle>
                  <DialogDescription>
                    Invite a friend via email. They'll receive your referral code and a link to sign up.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Friend's Email</label>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={referralEmail}
                      onChange={(e) => setReferralEmail(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Personalized Message (Optional)
                    </label>
                    <Textarea
                      placeholder="Hey, I thought you might like this space..."
                      value={referralMessage}
                      onChange={(e) => setReferralMessage(e.target.value)}
                      rows={3}
                    />
                  </div>
                </div>
                
                <DialogFooter>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={shareReferral}
                    disabled={!referralEmail || isSending}
                  >
                    {isSending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Share2 className="mr-2 h-4 w-4" />
                        Send Invitation
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="border-t pt-4">
        <p className="text-xs text-muted-foreground">
          Earn 100 points for each successful referral that joins and makes their first appointment.
        </p>
      </CardFooter>
    </Card>
  );
}