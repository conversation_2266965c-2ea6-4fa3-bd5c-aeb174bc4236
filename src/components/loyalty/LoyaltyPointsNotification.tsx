import React, { useEffect, useState } from 'react';
import { 
  <PERSON>, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowUp, Coins, Gift, X } from 'lucide-react';
import { LoyaltyTransaction } from '@/services/loyaltyService';
import { formatDistance } from 'date-fns';

interface LoyaltyPointsNotificationProps {
  transaction: LoyaltyTransaction;
  onClose: () => void;
  autoCloseAfterMs?: number;
}

export function LoyaltyPointsNotification({ 
  transaction, 
  onClose,
  autoCloseAfterMs = 5000 
}: LoyaltyPointsNotificationProps) {
  const [visible, setVisible] = useState(true);
  
  useEffect(() => {
    // Auto-close after the specified time
    const timeout = setTimeout(() => {
      setVisible(false);
      setTimeout(onClose, 300); // Allow time for fade-out animation
    }, autoCloseAfterMs);
    
    return () => clearTimeout(timeout);
  }, [autoCloseAfterMs, onClose]);
  
  if (!visible) return null;
  
  const getSourceLabel = (source: string) => {
    switch (source) {
      case 'appointment': return 'appointment completion';
      case 'referral': return 'referring a friend';
      case 'signup': return 'joining our loyalty program';
      case 'birthday': return 'your birthday';
      default: return source;
    }
  };
  
  return (
    <div className="fixed bottom-4 right-4 max-w-sm z-50 animate-slide-up">
      <Card className="border-2 border-green-200 shadow-lg">
        <CardHeader className="pb-2 space-y-0">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="bg-green-100 p-2 rounded-full">
                <Coins className="h-4 w-4 text-green-700" />
              </div>
              <CardTitle className="text-lg">Points Earned!</CardTitle>
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-6 w-6" 
              onClick={() => {
                setVisible(false);
                setTimeout(onClose, 300);
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            {formatDistance(new Date(transaction.createdAt), new Date(), { addSuffix: true })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-2">
            <p>{transaction.description}</p>
            <span className="font-bold text-green-700">+{transaction.points}</span>
          </div>
          <p className="text-sm text-muted-foreground">
            Thank you for {getSourceLabel(transaction.source)}!
          </p>
          {transaction.points >= 100 && (
            <div className="mt-2 p-2 bg-amber-50 rounded text-sm flex items-center gap-2">
              <Gift className="h-4 w-4 text-amber-700" />
              <p>You're getting closer to your next reward!</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

interface LoyaltyPointsManagerProps {
  customerId: string;
  transactions: LoyaltyTransaction[];
}

export function LoyaltyPointsManager({ 
  customerId, 
  transactions 
}: LoyaltyPointsManagerProps) {
  const [activeNotification, setActiveNotification] = useState<LoyaltyTransaction | null>(null);
  
  useEffect(() => {
    // Find the most recent points earned transaction from the last hour
    const recentTransaction = transactions
      .filter(t => t.type === 'earn' && t.customerId === customerId)
      .filter(t => {
        const transactionTime = new Date(t.createdAt).getTime();
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        return transactionTime > oneHourAgo;
      })
      .sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
    
    if (recentTransaction) {
      setActiveNotification(recentTransaction);
    }
  }, [transactions, customerId]);
  
  return (
    <>
      {activeNotification && (
        <LoyaltyPointsNotification 
          transaction={activeNotification} 
          onClose={() => setActiveNotification(null)} 
        />
      )}
    </>
  );
}
