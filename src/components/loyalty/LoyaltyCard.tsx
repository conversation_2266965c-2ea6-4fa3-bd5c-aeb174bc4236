'use client';

import { useState, useEffect } from 'react';
import { Customer } from '@/services/types/models';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  Ta<PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Gift, 
  Calendar, 
  ArrowUp, 
  ArrowDown, 
  Clock,
  Award,
  Coins
} from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';
import { 
  loyaltyService, 
  LoyaltyTransaction, 
  LoyaltyReward 
} from '@/services/loyaltyService';
import { toast } from 'sonner';

// Environment-aware logger
const logger = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[INFO] ${message}`, data);
    }
    // In production, you'd integrate with Sentry or another logging service here
  },
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.error(`[ERROR] ${message}`, error);
    }
    // In production, you'd integrate with Sentry or another logging service here
  }
};

interface LoyaltyCardProps {
  customer: Customer;
  onPointsUpdate?: () => void;
}

export function LoyaltyCard({ customer, onPointsUpdate }: LoyaltyCardProps) {
  return (
    <FeatureGate
      feature={RestrictedFeature.LOYALTY_PROGRAM}
      title="Loyalty Program"
      description="Reward your loyal customers with points and perks"
    >
      <LoyaltyCardContent customer={customer} onPointsUpdate={onPointsUpdate} />
    </FeatureGate>
  );
}

function LoyaltyCardContent({ customer, onPointsUpdate }: LoyaltyCardProps) {
  const [transactions, setTransactions] = useState<LoyaltyTransaction[]>([]);
  const [rewards, setRewards] = useState<LoyaltyReward[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [redeemingRewardId, setRedeemingRewardId] = useState<string | null>(null);

  const loyaltyPoints = customer.loyaltyPoints?.balance || 0;
  const loyaltyTier = customer.loyaltyPoints?.tier || 'bronze';
  
  // Calculate next tier threshold
  const tierThresholds = {
    bronze: 0,
    silver: 500,
    gold: 1500,
    platinum: 5000
  };
  
  // Determine tier progress
  const getCurrentTierProgress = () => {
    if (loyaltyTier === 'platinum') return 100;
    
    const currentThreshold = tierThresholds[loyaltyTier];
    const nextTier = loyaltyTier === 'bronze' ? 'silver' : 
                      loyaltyTier === 'silver' ? 'gold' : 'platinum';
    const nextThreshold = tierThresholds[nextTier];
    
    if (nextThreshold === currentThreshold) return 0;
    const progress = ((loyaltyPoints - currentThreshold) / (nextThreshold - currentThreshold)) * 100;
    return Math.min(Math.max(progress, 0), 100);
  };
  
  const nextTierName = 
    loyaltyTier === 'bronze' ? 'Silver' : 
    loyaltyTier === 'silver' ? 'Gold' : 
    loyaltyTier === 'gold' ? 'Platinum' : 'Platinum';
    
  const pointsToNextTier = 
    loyaltyTier === 'platinum' 
      ? 0 
      : Math.max(0, tierThresholds[nextTierName.toLowerCase() as keyof typeof tierThresholds] - loyaltyPoints);

  useEffect(() => {
    const loadTransactionsAndRewards = async () => {
      if (!customer.id) return;
      
      setIsLoading(true);
      try {
        const [transactionHistory, rewardsData] = await Promise.all([
          loyaltyService.getCustomerTransactionHistory(customer.id),
          loyaltyService.getAvailableRewards(customer.id)
        ]);
        
        setTransactions(transactionHistory);
        setRewards(rewardsData.rewards);
      } catch (error) {
        logger.error('Error loading loyalty data:', error);
        toast.error('Failed to load loyalty data');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadTransactionsAndRewards();
  }, [customer.id]);

  const handleRedeemReward = async (rewardId: string) => {
    if (!customer.id) return;
    
    setIsRedeeming(true);
    setRedeemingRewardId(rewardId);
    
    try {
      await loyaltyService.redeemPointsForReward(customer.id, rewardId);
      toast.success('Reward redeemed successfully!');
      
      // Refresh data
      const updatedTransactions = await loyaltyService.getCustomerTransactionHistory(customer.id);
      const updatedRewardsData = await loyaltyService.getAvailableRewards(customer.id);
      
      setTransactions(updatedTransactions);
      setRewards(updatedRewardsData.rewards);
      
      // Notify parent component that points were updated
      if (onPointsUpdate) {
        onPointsUpdate();
      }
    } catch (error) {
      logger.error('Error redeeming reward:', error);
      toast.error('Failed to redeem reward');
    } finally {
      setIsRedeeming(false);
      setRedeemingRewardId(null);
    }
  };

  const getTierColorClass = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'bronze':
        return 'bg-amber-700 text-white';
      case 'silver':
        return 'bg-gray-400 text-white';
      case 'gold':
        return 'bg-yellow-500 text-white';
      case 'platinum':
        return 'bg-indigo-500 text-white';
      default:
        return 'bg-primary text-primary-foreground';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Loyalty Program</CardTitle>
            <CardDescription>Earn and redeem loyalty points</CardDescription>
          </div>
          <Badge className={getTierColorClass(loyaltyTier)}>
            {loyaltyTier.charAt(0).toUpperCase() + loyaltyTier.slice(1)} Tier
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Current Points</span>
            <span className="text-2xl font-bold flex items-center">
              <Coins className="mr-2 h-5 w-5 text-yellow-500" />
              {loyaltyPoints}
            </span>
          </div>
          
          {loyaltyTier !== 'platinum' && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>{loyaltyTier.charAt(0).toUpperCase() + loyaltyTier.slice(1)}</span>
                <span>{nextTierName}</span>
              </div>
              <Progress value={getCurrentTierProgress()} className="h-2" />
              <p className="text-xs text-muted-foreground text-center mt-1">
                {pointsToNextTier} points to {nextTierName} tier
              </p>
            </div>
          )}
        </div>
        
        <Tabs defaultValue="rewards">
          <TabsList className="w-full overflow-x-auto items-center justify-start">
            <TabsTrigger value="rewards" className="whitespace-nowrap">Rewards</TabsTrigger>
            <TabsTrigger value="history" className="whitespace-nowrap">History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="rewards" className="mt-4">
            {isLoading ? (
              <div className="flex justify-center py-6">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : rewards.length === 0 ? (
              <div className="text-center py-6">
                <Gift className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">No rewards available</p>
              </div>
            ) : (
              <div className="space-y-4">
                {rewards.map((reward) => (
                  <Card key={reward.id} className="overflow-hidden">
                    <div className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold">{reward.name}</h3>
                          <p className="text-sm text-muted-foreground">{reward.description}</p>
                        </div>
                        <Badge variant="outline" className="flex items-center">
                          <Coins className="mr-1 h-3 w-3" />
                          {reward.pointsCost}
                        </Badge>
                      </div>
                      
                      <Button
                        variant={loyaltyPoints >= reward.pointsCost ? "default" : "outline"}
                        className="w-full mt-2"
                        disabled={loyaltyPoints < reward.pointsCost || isRedeeming}
                        onClick={() => handleRedeemReward(reward.id)}
                      >
                        {isRedeeming && redeemingRewardId === reward.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        ) : (
                          <Gift className="mr-2 h-4 w-4" />
                        )}
                        Redeem Reward
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="history" className="mt-4 max-h-[400px] overflow-auto">
            {isLoading ? (
              <div className="flex justify-center py-6">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : transactions.length === 0 ? (
              <div className="text-center py-6">
                <Clock className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">No transaction history</p>
              </div>
            ) : (
              <div className="space-y-3">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center py-2 border-b last:border-0">
                    <div className={`rounded-full p-2 mr-3 ${
                      transaction.type === 'earn' ? 'bg-green-100' : 'bg-orange-100'
                    }`}>
                      {transaction.type === 'earn' ? (
                        <ArrowUp className={`h-4 w-4 text-green-700`} />
                      ) : (
                        <ArrowDown className={`h-4 w-4 text-orange-700`} />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <p className="font-medium text-sm">{transaction.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {format(parseISO(transaction.createdAt), 'MMM d, yyyy')}
                      </p>
                    </div>
                    
                    <p className={`font-semibold ${
                      transaction.type === 'earn' ? 'text-green-700' : 'text-orange-700'
                    }`}>
                      {transaction.type === 'earn' ? '+' : '-'}{Math.abs(transaction.points)}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="flex flex-col items-start space-y-2 border-t pt-4">
        <div className="flex items-center text-xs text-muted-foreground">
          <Calendar className="h-3 w-3 mr-1" />
          <span>Points are awarded for appointments, referrals, and special events</span>
        </div>
        <div className="flex items-center text-xs text-muted-foreground">
          <Award className="h-3 w-3 mr-1" />
          <span>Reach higher tiers for exclusive rewards and benefits</span>
        </div>
      </CardFooter>
    </Card>
  );
}
