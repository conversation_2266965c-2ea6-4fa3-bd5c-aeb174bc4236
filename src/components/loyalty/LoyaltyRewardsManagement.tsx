'use client';

import { useState, useEffect } from 'react';
import { 
  loyaltyService, 
  LoyaltyReward 
} from '@/services/loyaltyService';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  PlusCircle, 
  Edit, 
  Trash, 
  Gift, 
  CircleDollarSign, 
  Package,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// Environment-aware logger
const logger = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[INFO] ${message}`, data);
    }
    // In production, you'd integrate with Sentry or another logging service here
  },
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.error(`[ERROR] ${message}`, error);
    }
    // In production, you'd integrate with Sentry or another logging service here
  }
};

const rewardFormSchema = z.object({
  name: z.string().min(2, {
    message: 'Name must be at least 2 characters.',
  }),
  description: z.string().min(5, {
    message: 'Description must be at least 5 characters.',
  }),
  pointsCost: z.coerce.number().positive({
    message: 'Points cost must be a positive number.',
  }),
  type: z.enum(['discount', 'free_service', 'product']),
  value: z.coerce.number().nonnegative({
    message: 'Value must be a non-negative number.',
  }),
  isActive: z.boolean(),
});

type RewardFormValues = z.infer<typeof rewardFormSchema>;

interface LoyaltyRewardsManagementProps {
  spaceId: string;
}

export function LoyaltyRewardsManagement({ spaceId }: LoyaltyRewardsManagementProps) {
  const [rewards, setRewards] = useState<LoyaltyReward[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingReward, setEditingReward] = useState<LoyaltyReward | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<RewardFormValues>({
    resolver: zodResolver(rewardFormSchema),
    defaultValues: {
      name: '',
      description: '',
      pointsCost: 100,
      type: 'discount',
      value: 10,
      isActive: true,
    },
  });
  
  useEffect(() => {
    loadRewards();
  }, []);
  
  const loadRewards = async () => {
    setIsLoading(true);
    try {
      const rewardsData = await loyaltyService.getAllRewards();
      setRewards(rewardsData);
    } catch (error) {
      logger.error('Error loading rewards:', error);
      toast.error('Failed to load rewards');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDialogOpen = (open: boolean) => {
    if (!open) {
      setEditingReward(null);
      form.reset({
        name: '',
        description: '',
        pointsCost: 100,
        type: 'discount',
        value: 10,
        isActive: true,
      });
    }
    setIsDialogOpen(open);
  };
  
  const handleEditReward = (reward: LoyaltyReward) => {
    setEditingReward(reward);
    form.reset({
      name: reward.name,
      description: reward.description,
      pointsCost: reward.pointsCost,
      type: reward.type,
      value: reward.value,
      isActive: reward.isActive,
    });
    setIsDialogOpen(true);
  };
  
  const onSubmit = async (data: RewardFormValues) => {
    setIsSubmitting(true);
    
    try {
      if (editingReward) {
        // Update existing reward
        await loyaltyService.updateReward(editingReward.id, {
          ...data,
          // Include any additional metadata or fields you need
          metadata: {
            spaceId,
            updatedAt: new Date().toISOString(),
          },
        });
        
        toast.success('Reward updated successfully');
      } else {
        // Create new reward
        await loyaltyService.createReward({
          ...data,
          expiresAt: undefined, // Set expiration if needed
          metadata: {
            spaceId,
            createdAt: new Date().toISOString(),
          },
        });
        
        toast.success('Reward created successfully');
      }
      
      // Close dialog and refresh list
      setIsDialogOpen(false);
      loadRewards();
    } catch (error) {
      logger.error('Error saving reward:', error);
      toast.error(editingReward ? 'Failed to update reward' : 'Failed to create reward');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const getRewardTypeIcon = (type: string) => {
    switch (type) {
      case 'discount':
        return <CircleDollarSign className="h-4 w-4" />;
      case 'free_service':
        return <Gift className="h-4 w-4" />;
      case 'product':
        return <Package className="h-4 w-4" />;
      default:
        return <Gift className="h-4 w-4" />;
    }
  };
  
  const getRewardTypeLabel = (type: string) => {
    switch (type) {
      case 'discount':
        return 'Discount';
      case 'free_service':
        return 'Free Service';
      case 'product':
        return 'Product';
      default:
        return type;
    }
  };
  
  const formatRewardValue = (type: string, value: number) => {
    switch (type) {
      case 'discount':
        return `${value}% off`;
      case 'free_service':
        return 'Free service';
      case 'product':
        return 'Free product';
      default:
        return `${value}`;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Loyalty Rewards</h2>
          <p className="text-muted-foreground">Manage rewards for your loyalty program</p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={handleDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <PlusCircle className="h-4 w-4" /> Add New Reward
            </Button>
          </DialogTrigger>
          
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{editingReward ? 'Edit Reward' : 'Create New Reward'}</DialogTitle>
              <DialogDescription>
                {editingReward 
                  ? 'Update the details of this loyalty reward.' 
                  : 'Fill in the details to create a new loyalty reward.'}
              </DialogDescription>
            </DialogHeader>
            
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reward Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="10% Off Next Service" />
                      </FormControl>
                      <FormDescription>
                        A clear, concise name for the reward.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          {...field} 
                          placeholder="Get 10% off your next service booking" 
                        />
                      </FormControl>
                      <FormDescription>
                        Detailed description of what the customer gets.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reward Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select reward type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="discount">Discount</SelectItem>
                            <SelectItem value="free_service">Free Service</SelectItem>
                            <SelectItem value="product">Free Product</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The type of reward being offered.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {form.watch('type') === 'discount' ? 'Discount Percentage' : 'Value'}
                        </FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field} 
                            min={0}
                            max={form.watch('type') === 'discount' ? 100 : undefined} 
                          />
                        </FormControl>
                        <FormDescription>
                          {form.watch('type') === 'discount' 
                            ? 'Percentage discount (0-100%)' 
                            : 'Monetary value of the reward'}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="pointsCost"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Points Cost</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          min={1} 
                        />
                      </FormControl>
                      <FormDescription>
                        How many loyalty points customers need to redeem this reward.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Active Status</FormLabel>
                        <FormDescription>
                          Make this reward available to customers
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      editingReward ? 'Update Reward' : 'Create Reward'
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Available Rewards</CardTitle>
          <CardDescription>
            View and manage loyalty rewards for your customers
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : rewards.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Gift className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No rewards yet</h3>
              <p className="text-muted-foreground mb-4">
                Create rewards to engage your customers and build loyalty
              </p>
              <Button onClick={() => setIsDialogOpen(true)}>
                <PlusCircle className="h-4 w-4 mr-2" /> 
                Create First Reward
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Reward</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Points</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              
              <TableBody>
                {rewards.map((reward) => (
                  <TableRow key={reward.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{reward.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {reward.description}
                        </p>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center">
                        <Badge 
                          variant="outline" 
                          className="flex gap-1 items-center"
                        >
                          {getRewardTypeIcon(reward.type)}
                          {getRewardTypeLabel(reward.type)}
                        </Badge>
                        <span className="ml-2 text-sm">
                          {formatRewardValue(reward.type, reward.value)}
                        </span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge variant="secondary">
                        {reward.pointsCost} points
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      {reward.isActive ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-muted-foreground">
                          Inactive
                        </Badge>
                      )}
                    </TableCell>
                    
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditReward(reward)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        {/* Add delete functionality if needed */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
