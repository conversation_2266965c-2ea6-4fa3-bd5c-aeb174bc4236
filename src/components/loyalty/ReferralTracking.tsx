'use client';

import { useState, useEffect } from 'react';
import { Customer } from '@/services/types/models';
import { customerService } from '@/services/firestore';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users,
  Share2,
  CheckCircle2,
  ExternalLink,
  Calendar
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

interface ReferralTrackingProps {
  customer: Customer;
}

export function ReferralTracking({ customer }: ReferralTrackingProps) {
  const [referrals, setReferrals] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  const referralCode = customer.loyaltyPoints?.referralCode || '';
  
  useEffect(() => {
    if (customer.id && referralCode) {
      loadReferrals();
    } else {
      setIsLoading(false);
    }
  }, [customer.id, referralCode]);
  
  const loadReferrals = async () => {
    if (!customer.id || !referralCode) return;
    
    setIsLoading(true);
    try {
      // Query customers referred by this customer
      const customersSnapshot = await customerService.queryCustomers([
        { field: 'metadata.referredBy', operator: '==', value: referralCode }
      ]);
      
      setReferrals(customersSnapshot);
    } catch (error) {
      console.error('Error loading referrals:', error);
      toast.error('Failed to load referral data');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!referralCode) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referral Tracking</CardTitle>
          <CardDescription>Track customers you've referred</CardDescription>
        </CardHeader>
        <CardContent className="text-center py-6">
          <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Not enrolled in loyalty program</h3>
          <p className="text-muted-foreground mb-4">
            The customer needs to be enrolled in the loyalty program to participate in referrals.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Referral Tracking</CardTitle>
        <CardDescription>Track customers referred to the space</CardDescription>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        ) : referrals.length === 0 ? (
          <div className="text-center py-6">
            <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No referrals yet</h3>
            <p className="text-muted-foreground mb-4">
              No customers have signed up using this referral code yet.
            </p>
            <Badge className="mb-4">
              Referral Code: {referralCode}
            </Badge>
          </div>
        ) : (
          <>
            <div className="mb-4 flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              <span className="text-sm">
                Successfully referred <strong>{referrals.length}</strong> {referrals.length === 1 ? 'customer' : 'customers'}
              </span>
            </div>
            
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead>Visits</TableHead>
                  <TableHead className="text-right">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {referrals.map((referral) => (
                  <TableRow key={referral.id}>
                    <TableCell className="font-medium">
                      {referral.displayName}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        {new Date(referral.createdAt).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      {referral.visits} visits
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/dashboard/customers/${referral.id}`}>
                          <ExternalLink className="h-4 w-4" />
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </>
        )}
      </CardContent>
    </Card>
  );
}