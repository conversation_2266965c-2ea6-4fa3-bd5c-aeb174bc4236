'use client';

import { useState, useEffect } from 'react';
import { Customer } from '@/services/types/models';
import { loyaltyService } from '@/services/loyaltyService';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Share2, 
  Copy, 
  Mail, 
  MessageSquare,
  CheckCircle2,
  ExternalLink,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { customerService } from '@/services/firestore';

// Environment-aware logger
const logger = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[INFO] ${message}`, data);
    }
  },
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.error(`[ERROR] ${message}`, error);
    }
  }
};

interface CustomerReferralsProps {
  customer: Customer;
  onReferralSuccess?: () => void;
}

export function CustomerReferrals({ customer, onReferralSuccess }: CustomerReferralsProps) {
  return (
    <FeatureGate
      feature={RestrictedFeature.REFERRAL_SYSTEM}
      title="Customer Referrals"
      description="Grow your business through word-of-mouth marketing"
    >
      <CustomerReferralsContent customer={customer} onReferralSuccess={onReferralSuccess} />
    </FeatureGate>
  );
}

function CustomerReferralsContent({ customer, onReferralSuccess }: CustomerReferralsProps) {
  const [referrals, setReferrals] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [referralEmail, setReferralEmail] = useState('');
  const [referralMessage, setReferralMessage] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const referralCode = customer.loyaltyPoints?.referralCode || '';
  const referralUrl = `${window.location.origin}/referral/${referralCode}`;
  
  useEffect(() => {
    if (customer.id && referralCode) {
      loadReferrals();
    }
  }, [customer.id, referralCode]);
  
  const loadReferrals = async () => {
    if (!customer.id || !referralCode) return;
    
    setIsLoading(true);
    try {
      // Query customers referred by this customer
      const customersSnapshot = await customerService.queryCustomers([
        { field: 'metadata.referredBy', operator: '==', value: referralCode }
      ]);
      
      setReferrals(customersSnapshot);
    } catch (error) {
      logger.error('Error loading referrals:', error);
      toast.error('Failed to load referral data');
    } finally {
      setIsLoading(false);
    }
  };
  
  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralUrl)
      .then(() => {
        toast.success('Referral link copied to clipboard');
      })
      .catch((error) => {
        logger.error('Failed to copy referral link:', error);
        toast.error('Failed to copy referral link');
      });
  };
  
  const shareReferral = async () => {
    if (!referralEmail) {
      toast.error('Please enter an email address');
      return;
    }
    
    setIsSending(true);
    
    try {
      // Setup default message if empty
      const message = referralMessage || 
        `Hi, I thought you might enjoy services at my space. Use my referral code ${referralCode} to get a welcome bonus!`;
      
      // Send referral invitation
      await fetch('/api/send-referral', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: referralEmail,
          message,
          senderName: customer.displayName,
          referralCode,
          referralUrl
        }),
      });
      
      toast.success('Referral invitation sent!');
      setReferralEmail('');
      setReferralMessage('');
      setIsDialogOpen(false);
      
      if (onReferralSuccess) {
        onReferralSuccess();
      }
    } catch (error) {
      logger.error('Error sending referral:', error);
      toast.error('Failed to send referral invitation');
    } finally {
      setIsSending(false);
    }
  };
  
  const shareViaApp = (app: 'whatsapp' | 'messenger') => {
    const message = encodeURIComponent(`Join me at this fantastic space! Use my referral code ${referralCode} or sign up with this link: ${referralUrl}`);
    
    let url = '';
    if (app === 'whatsapp') {
      url = `https://wa.me/?text=${message}`;
    } else if (app === 'messenger') {
      // Todo: Remember to replace the Facebook APP Id
      url = `https://www.facebook.com/dialog/send?link=${encodeURIComponent(referralUrl)}&app_id=groombook&redirect_uri=${encodeURIComponent(window.location.origin)}`;
    }
    
    if (url) {
      window.open(url, '_blank');
    }
  };
  
  if (!referralCode) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referral Program</CardTitle>
          <CardDescription>Share with friends and earn rewards</CardDescription>
        </CardHeader>
        <CardContent className="text-center py-6">
          <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Not enrolled in loyalty program</h3>
          <p className="text-muted-foreground mb-4">
            The customer needs to be enrolled in the loyalty program to participate in referrals.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Referral Program</CardTitle>
        <CardDescription>Share with friends and earn rewards</CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="share">
          <TabsList className="w-full overflow-x-auto items-center justify-start">
            <TabsTrigger value="share" className="whitespace-nowrap">Share</TabsTrigger>
            <TabsTrigger value="referrals" className="whitespace-nowrap">My Referrals</TabsTrigger>
          </TabsList>
          
          <TabsContent value="share" className="space-y-4 mt-4">
            <div className="bg-muted p-4 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium">Your Referral Code</p>
                <Badge variant="outline" className="text-lg font-mono">
                  {referralCode}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2 mt-4">
                <Input 
                  value={referralUrl}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={copyReferralLink}
                  title="Copy referral link"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Share Your Referral</h3>
              <p className="text-sm text-muted-foreground mb-4">
                For each friend who joins using your referral code, you'll both receive loyalty points.
              </p>
              
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" className="w-full" onClick={() => shareViaApp('whatsapp')}>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  WhatsApp
                </Button>
                <Button variant="outline" className="w-full" onClick={() => shareViaApp('messenger')}>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Messenger
                </Button>
                
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="w-full col-span-2">
                      <Mail className="mr-2 h-4 w-4" />
                      Send Email Invitation
                    </Button>
                  </DialogTrigger>
                  
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Send Referral Invitation</DialogTitle>
                      <DialogDescription>
                        Invite a friend via email. They'll receive your referral code and a link to sign up.
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Friend's Email</label>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          value={referralEmail}
                          onChange={(e) => setReferralEmail(e.target.value)}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Personalized Message (Optional)
                        </label>
                        <Textarea
                          placeholder="Hey, I thought you might like this salon..."
                          value={referralMessage}
                          onChange={(e) => setReferralMessage(e.target.value)}
                          rows={3}
                        />
                      </div>
                    </div>
                    
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setIsDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={shareReferral}
                        disabled={!referralEmail || isSending}
                      >
                        {isSending ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                            Sending...
                          </>
                        ) : (
                          <>
                            <Share2 className="mr-2 h-4 w-4" />
                            Send Invitation
                          </>
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="referrals" className="mt-4">
            {isLoading ? (
              <div className="flex justify-center py-6">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : referrals.length === 0 ? (
              <div className="text-center py-6">
                <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No referrals yet</h3>
                <p className="text-muted-foreground mb-4">
                  Share your referral code to start earning bonus points
                </p>
                <Button 
                  variant="outline" 
                  onClick={() => (document.querySelector('[data-value="share"]') as HTMLElement)?.click()}
                >
                  <Share2 className="mr-2 h-4 w-4" />
                  Share your code
                </Button>
              </div>
            ) : (
              <div>
                <div className="mb-4 flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <span className="text-sm">
                    You've referred <strong>{referrals.length}</strong> {referrals.length === 1 ? 'person' : 'people'}
                  </span>
                </div>
                
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Joined</TableHead>
                      <TableHead className="text-right">Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {referrals.map((referral) => (
                      <TableRow key={referral.id}>
                        <TableCell className="font-medium">
                          {referral.displayName}
                        </TableCell>
                        <TableCell>
                          {new Date(referral.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm" asChild>
                            <a href={`/dashboard/customers/${referral.id}`}>
                              <ExternalLink className="h-4 w-4" />
                            </a>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="border-t pt-4">
        <p className="text-xs text-muted-foreground">
          Earn {loyaltyService ? '100' : 'bonus'} points for each successful referral that joins and makes their first appointment.
        </p>
      </CardFooter>
    </Card>
  );
}
