import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { loyaltyService } from '@/services/loyaltyService';
import { FailedOperationsQueue } from '@/utils/retryUtils';

export function LoyaltySystemHealth() {
  const [failedOperations, setFailedOperations] = useState<any[]>([]);
  const [recovering, setRecovering] = useState(false);
  const [recoveryResults, setRecoveryResults] = useState<{ recovered: number; failed: number } | null>(null);
  const [recoverySuccess, setRecoverySuccess] = useState(false);

  useEffect(() => {
    loadFailedOperations();
  }, []);

  const loadFailedOperations = () => {
    const operations = FailedOperationsQueue.getInstance().getFailedOperations();
    setFailedOperations(operations);
  };

  const handleRecoverOperations = async () => {
    try {
      setRecovering(true);
      setRecoveryResults(null);
      
      const results = await loyaltyService.recoverFailedOperations();
      
      setRecoveryResults(results);
      setRecoverySuccess(results.recovered > 0);
      
      // Refresh the list
      loadFailedOperations();
    } catch (error) {
      console.error('Failed to recover operations:', error);
    } finally {
      setRecovering(false);
    }
  };

  const handleClearAll = () => {
    FailedOperationsQueue.getInstance().clearAll();
    loadFailedOperations();
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Loyalty System Health</CardTitle>
        <CardDescription>
          Monitor and recover failed loyalty operations
        </CardDescription>
      </CardHeader>
      <CardContent>
        {failedOperations.length > 0 ? (
          <>
            <div className="mb-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Failed Loyalty Operations</AlertTitle>
                <AlertDescription>
                  There are {failedOperations.length} failed loyalty operations that need attention.
                </AlertDescription>
              </Alert>
            </div>
            
            <div className="max-h-96 overflow-y-auto border rounded-md">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Operation
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timestamp
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Error
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {failedOperations.map((op, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {op.operation}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(op.timestamp).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 truncate max-w-xs">
                        {op.error}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {recoveryResults && (
              <div className="mt-4">
                <Alert variant={recoverySuccess ? "default" : "destructive"}>
                  {recoverySuccess ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                  <AlertTitle>Recovery Results</AlertTitle>
                  <AlertDescription>
                    Successfully recovered {recoveryResults.recovered} operations.
                    {recoveryResults.failed > 0 && ` Failed to recover ${recoveryResults.failed} operations.`}
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </>
        ) : (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>System Healthy</AlertTitle>
            <AlertDescription>
              All loyalty operations are working correctly. No failed operations detected.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={loadFailedOperations}
        >
          Refresh
        </Button>
        
        <div className="space-x-2">
          {failedOperations.length > 0 && (
            <Button 
              variant="outline" 
              onClick={handleClearAll}
              disabled={recovering}
            >
              Clear All
            </Button>
          )}
          
          <Button 
            onClick={handleRecoverOperations} 
            disabled={recovering || failedOperations.length === 0}
          >
            {recovering ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Recovering...
              </>
            ) : (
              'Recover Operations'
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
