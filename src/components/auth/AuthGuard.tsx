"use client"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/utils/auth"

// Pages that authenticated users should not access
const PUBLIC_ONLY_PATHS = ["/login", "/register", "/"]

// Pages that don't require authentication
const PUBLIC_PATHS = ["/login", "/register", "/", "/book"]

export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // If user is authenticated and tries to access public-only pages
    if (user && PUBLIC_ONLY_PATHS.includes(pathname)) {
      router.replace("/dashboard") // Redirect to dashboard
    }

    // If user is not authenticated and tries to access a protected page
    if (!user && !pathname.startsWith('/book') && !PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
      router.replace("/login") // Redirect to login
    }
  }, [user, pathname, router])

  return <>{children}</>
}