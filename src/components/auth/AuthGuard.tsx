"use client"

import { useEffect, useRef } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/utils/auth"

// Pages that authenticated users should not access
const PUBLIC_ONLY_PATHS = ["/login", "/register", "/"]

// Pages that don't require authentication
const PUBLIC_PATHS = ["/login", "/register", "/", "/book", "/about", "/contact", "/privacy", "/terms", "/cookies"]

export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const hasRedirected = useRef(false)

  useEffect(() => {
    // Don't redirect while auth is still loading
    if (loading) return

    // Reset redirect flag when pathname changes
    if (hasRedirected.current) {
      hasRedirected.current = false
      return
    }

    // If user is authenticated and tries to access public-only pages
    if (user && PUBLIC_ONLY_PATHS.includes(pathname)) {
      hasRedirected.current = true
      router.replace("/dashboard")
      return
    }

    // If user is not authenticated and tries to access a protected page
    if (!user && !pathname.startsWith('/book') && !PUBLIC_PATHS.some(path => pathname.startsWith(path))) {
      hasRedirected.current = true
      router.replace("/login")
      return
    }
  }, [user, loading, pathname, router])

  // Show loading state while auth is being determined
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <>{children}</>
}