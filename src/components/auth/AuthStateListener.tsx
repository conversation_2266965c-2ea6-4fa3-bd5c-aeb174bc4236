'use client';

import { useEffect } from 'react';
import { onAuthStateChanged, getRedirectResult, GoogleAuthProvider } from 'firebase/auth';
import { auth, db } from '@/utils/firebase';
import { useAppDispatch } from '@/store/hooks';
import { setUser, setAuthLoading } from '@/store/slices/authSlice';
import posthog from 'posthog-js';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

export function AuthStateListener({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();
  const router = useRouter();

  // Handle Google redirect result
  useEffect(() => {
    const handleRedirectResult = async () => {
      try {
        console.log("Checking for redirect results...");
        const result = await getRedirectResult(auth);
        
        if (result) {
          console.log("Processing Google redirect result");
          const credential = GoogleAuthProvider.credentialFromResult(result);
          const user = result.user;
          
          // Check if user exists in Firestore
          const userDocRef = doc(db, 'users', user.uid);
          const userDocSnap = await getDoc(userDocRef);
          
          // Check if user is new
          const isNewUser = !userDocSnap.exists();
          
          if (isNewUser) {
            // Create new user document for redirect sign-in
            const userData = {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
              photoURL: user.photoURL,
              role: 'owner',
              hasCompletedOnboarding: false,
              businessType: null,
              referralSource: null,
              createdAt: new Date().toISOString()
            };
            
            await setDoc(userDocRef, userData);
            toast.success('Account created successfully!');
            router.push('/onboarding');
          } else {
            // User already exists
            const userData = userDocSnap.data();
            toast.success('Signed in successfully!');
            
            // Route to the appropriate page based on onboarding status
            if (userData.hasCompletedOnboarding === false) {
              router.push('/onboarding');
            } else {
              router.push('/dashboard');
            }
          }
        }
      } catch (error) {
        console.error("Error processing redirect result:", error);
        
        // Show a helpful message
        if (error && typeof error === 'object' && 'code' in error) {
          const errorCode = (error as any).code;
          if (errorCode === 'auth/account-exists-with-different-credential') {
            toast.error('An account already exists with the same email address but different sign-in credentials.');
          } else {
            toast.error('Authentication failed. Please try again.');
          }
        }
      }
    };
    
    // Process redirect result when the component mounts
    handleRedirectResult();
  }, [dispatch, router]);

  useEffect(() => {
    // Set loading state while we initialize the auth listener
    dispatch(setAuthLoading(true));
    
    // Set up auth state listener with better error handling
    const unsubscribe = onAuthStateChanged(auth, async (authUser) => {
      try {
        if (authUser) {
          // Fetch user data from firestore
          const userDoc = await getDoc(doc(db, 'users', authUser.uid));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            // Merge Firestore data with essential auth data
            const user = {
              uid: authUser.uid,
              email: authUser.email,
              displayName: userData.displayName || authUser.displayName,
              photoURL: userData.photoURL || authUser.photoURL,
              role: userData.role || 'owner',
              // Add any additional fields from Firestore
              phoneNumber: userData.phoneNumber,
              createdAt: userData.createdAt,
              updatedAt: userData.updatedAt,
              // Include plan details if available
              planDetails: userData.planDetails,
              // Include onboarding status
              hasCompletedOnboarding: userData.hasCompletedOnboarding === true,
              businessType: userData.businessType || null,
              referralSource: userData.referralSource || null,
              // Add any other fields you store in Firestore
            };

            dispatch(setUser(user));
        
            // Identify user in PostHog (debounced)
            setTimeout(() => {
              posthog.identify(user.uid, {
                email: user.email,
                name: user.displayName,
                role: 'owner',
              });
            }, 500);
          } else {
            console.error("User document doesn't exist in Firestore for uid:", authUser.uid);
            dispatch(setUser(null));
          }
        } else {
          // User is signed out
          dispatch(setUser(null));
          // Clear user identification when signed out (debounced)
          setTimeout(() => {
            posthog.reset();
          }, 100);
        }
      } catch (error) {
        console.error("Error in auth state change handler:", error);
        // Don't set user to null here - that could cause unnecessary redirects
        // Just log the error but maintain the current auth state
      } finally {
        // Always set loading to false when we're done processing
        dispatch(setAuthLoading(false));
      }
    }, (error) => {
      // This is the error handler for the onAuthStateChanged function
      console.error("Auth state listener error:", error);
      dispatch(setAuthLoading(false));
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [dispatch]);

  return <>{children}</>;
}
