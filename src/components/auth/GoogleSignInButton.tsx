'use client';


import { useAppDispatch } from '@/store/hooks';
import { loginWithGoogle, loginWithGoogleRedirect } from '@/store/slices/authSlice';
import { FcGoogle } from 'react-icons/fc';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import posthog from 'posthog-js';
import { sendWelcomeEmail } from '@/utils/email-client';
import { Button } from '../ui/button';
import { trackRegistrationConversion } from '@/utils/googleAdsTracking';

interface GoogleSignInButtonProps {
  mode?: 'sign-in' | 'sign-up';
  className?: string;
  planDetails?: {
    plan: string;
    billingCycle: string;
    price: string;
  };
}

export function GoogleSignInButton({ 
  mode = 'sign-in',
  className = '',
  planDetails
}: GoogleSignInButtonProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const handleGoogleSignIn = async () => {
    try {
      // Track the attempt in PostHog
      posthog.capture(mode === 'sign-in' ? 'google_signin_attempted' : 'google_signup_attempted', {
        method: 'google',
        ...(planDetails && { planDetails })
      });
      
      
      
      // For browsers that work well with popups
      // Add a timeout to ensure the popup works correctly
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Focus the window to improve popup experience 
      window.focus();
      
      const resultAction = await dispatch(loginWithGoogle({ planDetails })).unwrap();
      
      // Track the successful event in PostHog
      posthog.capture(mode === 'sign-in' ? 'user_google_signin' : 'user_google_signup', {
        method: 'google',
        ...(planDetails && { planDetails })
      });
      
      // For sign-ups, track Google Ads conversion and send welcome email
      if (mode === 'sign-up') {
        // Track Google Ads conversion
        trackRegistrationConversion();
        
        try {
          const auth = (await import('firebase/auth')).getAuth();
          const currentUser = auth.currentUser;
          
          if (currentUser && currentUser.email) {
            await sendWelcomeEmail({
              email: currentUser.email,
              name: currentUser.displayName || '',
              dashboardUrl: `${window.location.origin}/dashboard`
            });
            console.log('Welcome email sent successfully to Google user');
          }
        } catch (emailError) {
          console.error('Failed to send welcome email to Google user:', emailError);
          // Don't block the sign-in flow if email fails
        }
      }
      
      toast.success(mode === 'sign-in' ? 'Signed in successfully!' : 'Account created successfully!');
      
      // Log the returned data from Google login
      console.log("Google login successful, user data:", resultAction);
      
      // Check if user needs to complete onboarding
      if (resultAction.hasCompletedOnboarding === false) {
        console.log("User needs to complete onboarding");
        router.push('/onboarding');
      } else {
        console.log("User has completed onboarding, going to dashboard");
        router.push('/dashboard');
      }
      
      router.refresh();
    } catch (error) {
      console.error('Google auth error:', error);
      
      // More detailed error logging
      if (error && typeof error === 'object') {
        console.error('Error details:', JSON.stringify(error));
      }
      
      // Display a meaningful error message
      toast.error(typeof error === 'string' ? error : 'Authentication failed. Please try again.');
      
      // Track the error in analytics
      posthog.capture('google_auth_error', {
        error: typeof error === 'string' ? error : 'Unknown error'
      });
    }
  };

  return (
    <Button
      variant="outline"
      type="button"
      className={`w-full flex items-center justify-center gap-2 ${className}`}
      onClick={handleGoogleSignIn}
    >
      <FcGoogle className="h-5 w-5" />
      <span>{mode === 'sign-in' ? 'Sign in' : 'Sign up'} with Google</span>
    </Button>
  );
}
