'use client';


import { useAppDispatch } from '@/store/hooks';
import { loginWithGoogle, loginWithGoogleRedirect } from '@/store/slices/authSlice';
import { FcGoogle } from 'react-icons/fc';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import posthog from 'posthog-js';
import { sendWelcomeEmail } from '@/utils/email-client';
import { Button } from '../ui/button';
import { trackRegistrationConversion } from '@/utils/googleAdsTracking';

interface GoogleSignInButtonProps {
  mode?: 'sign-in' | 'sign-up';
  className?: string;
  planDetails?: {
    plan: string;
    billingCycle: string;
    price: string;
  };
}

export function GoogleSignInButton({ 
  mode = 'sign-in',
  className = '',
  planDetails
}: GoogleSignInButtonProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const handleGoogleSignIn = async () => {
    try {
      // Single tracking call for attempt
      posthog.capture('google_auth_attempted', {
        mode,
        method: 'google',
        ...(planDetails && { planDetails })
      });

      // Focus the window to improve popup experience
      window.focus();

      const resultAction = await dispatch(loginWithGoogle({ planDetails })).unwrap();

      toast.success(mode === 'sign-in' ? 'Signed in successfully!' : 'Account created successfully!');

      // Handle post-authentication tasks asynchronously without blocking navigation
      const handlePostAuth = async () => {
        try {
          // Track success
          posthog.capture('google_auth_success', {
            mode,
            method: 'google',
            hasCompletedOnboarding: resultAction.hasCompletedOnboarding
          });

          // For sign-ups, handle conversion tracking and email in background
          if (mode === 'sign-up') {
            // Non-blocking operations
            setTimeout(() => {
              trackRegistrationConversion();
            }, 0);

            // Send welcome email in background
            setTimeout(async () => {
              try {
                const auth = (await import('firebase/auth')).getAuth();
                const currentUser = auth.currentUser;

                if (currentUser && currentUser.email) {
                  await sendWelcomeEmail({
                    email: currentUser.email,
                    name: currentUser.displayName || '',
                    dashboardUrl: `${window.location.origin}/dashboard`
                  });
                }
              } catch (emailError) {
                console.error('Failed to send welcome email:', emailError);
              }
            }, 100);
          }
        } catch (error) {
          console.error('Post-auth tasks error:', error);
        }
      };

      // Run post-auth tasks in background
      handlePostAuth();

      // Navigate immediately without waiting for background tasks
      if (resultAction.hasCompletedOnboarding === false) {
        router.push('/onboarding');
      } else {
        router.push('/dashboard');
      }

    } catch (error) {
      console.error('Google auth error:', error);

      // Display a meaningful error message
      toast.error(typeof error === 'string' ? error : 'Authentication failed. Please try again.');

      // Single error tracking call
      posthog.capture('google_auth_error', {
        mode,
        error: typeof error === 'string' ? error : 'Unknown error'
      });
    }
  };

  return (
    <Button
      variant="outline"
      type="button"
      className={`w-full flex items-center justify-center gap-2 ${className}`}
      onClick={handleGoogleSignIn}
    >
      <FcGoogle className="h-5 w-5" />
      <span>{mode === 'sign-in' ? 'Sign in' : 'Sign up'} with Google</span>
    </Button>
  );
}
