'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { gtag_report_conversion } from '@/utils/googleAdsTracking';
import { pricingPlans } from '@/lib/billing';
import { OptimizedImage } from '@/components/ui/optimized-image';

export function Hero() {
  return (
    <section className="relative py-16 md:py-24">
      {/* Hidden SEO content for search engines */}
      <div className="sr-only">
        <h2>Salon and Barbershop Management Software Features</h2>
        <p>Complete salon and barbershop management solution with appointment scheduling, walk-in queue management, payment processing, staff scheduling, and customer communication tools.</p>
        <h3>Key Benefits for Salon and Barbershop Owners</h3>
        <ul>
          <li>Manage appointments and walk-ins in one system</li>
          <li>Send automated appointment reminders</li>
          <li>Track revenue and business performance</li>
          <li>Handle staff scheduling efficiently</li>
          <li>Process payments and manage customers</li>
          <li>Simple setup with free trial available</li>
        </ul>
      </div>
      
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="flex flex-col gap-6">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              Stop Losing Money to 
              <span className="text-red-600"> Walk-in Chaos</span> & 
              <span className="text-red-600"> Empty Chairs</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-lg">
              GroomBook helps salon and barbershop owners manage appointments and walk-ins 
              in one simple system. Get organized and focus on what you do best.
            </p>
            <div className="bg-green-100 text-green-700 font-medium px-4 py-2 rounded-md inline-flex items-center gap-2 max-w-md">
              <span className="text-green-500" aria-hidden="true">✓</span> {pricingPlans.basic.trialDays}-Day Free Trial - No Credit Card Required
            </div>
            <div className="flex flex-col sm:flex-row gap-4 mt-6">
              <Button 
                asChild 
                size="lg" 
                className="bg-mustard text-black hover:bg-mustard-dark text-lg px-8 py-4 shadow-lg"
                onClick={() => gtag_report_conversion('/register')}
              >
                <Link href="/register">Start Free Trial</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="text-lg px-8 py-4 border-2">
                <Link href="/#features">See How It Works</Link>
              </Button>
            </div>
          </div>

          {/* Right Column - App Screenshots */}
          <div className="relative w-full px-6 py-6 md:px-8 md:py-8">
            {/* Stacked Screenshots with Responsive Overlap */}
            <div className="relative w-full max-w-lg md:max-w-2xl lg:max-w-3xl mx-auto">
              {/* Dashboard Screenshot - Behind */}
              <div className="relative rounded-xl overflow-hidden border border-gray-200 shadow-2xl">
                <Image
                  src="/screenshots/dashboard.png"
                  alt="GroomBook Dashboard - Track revenue, monitor chairs, manage stylists and barbers"
                  width={600}
                  height={400}
                  className="w-full h-auto"
                  priority
                />
              </div>
              
              {/* Loyalty Screenshot - Responsive Overlapping */}
              <div className="absolute -bottom-6 -right-6 md:-bottom-8 md:-right-8 w-1/2 md:w-2/3 rounded-xl overflow-hidden border border-gray-200 shadow-2xl bg-white">
                <Image
                  src="/screenshots/loyalty.png"
                  alt="GroomBook Loyalty Program - Turn customers into regulars"
                  width={400}
                  height={300}
                  className="w-full h-auto"
                  priority
                />
              </div>
            </div>
            
            {/* App Features Badge */}
            <div className="absolute top-2 left-2 md:top-0 md:left-0 bg-mustard text-black px-3 py-1 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-semibold shadow-lg z-10">
              Live App Screenshots
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
