'use client';

import Link from 'next/link';
import Image from 'next/image';

interface FooterLinkProps {
  href: string;
  label: string;
}

const FooterLink = ({ href, label }: FooterLinkProps) => (
  <Link 
    href={href} 
    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
  >
    {label}
  </Link>
);

export function Footer() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="py-12 bg-background">
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center mb-8">
          <Image
            src="/logo.png"
            alt="GroomBook logo"
            width={40}
            height={40}
            className="inline-block rounded-sm"
          />
          <span className="sr-only">GroomBook</span>
          <span className="ml-3 text-2xl font-bold">GroomBook</span>
        </div>
        
        <div className="flex justify-center space-x-8 mb-8">
          <FooterLink href="/contact" label="Contact Us" />
          <FooterLink href="/about" label="About Us" />
          <FooterLink href="/support" label="Support Center" />
          <FooterLink href="/register" label="Get Started" />
        </div>
        
        <div className="border-t border-border pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground mb-4 md:mb-0">
            © {currentYear} GroomBook. All rights reserved.
          </p>
          
          <div className="flex space-x-6">
            <FooterLink href="/privacy" label="Privacy Policy" />
            <FooterLink href="/terms" label="Terms of Service" />
            <FooterLink href="/cookies" label="Cookies Settings" />
          </div>
        </div>
      </div>
    </footer>
  );
}
