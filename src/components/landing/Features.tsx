'use client';

import { <PERSON><PERSON>ard, <PERSON>, <PERSON><PERSON>hart, Calendar, Megaphone, Zap, ArrowRight, MessageCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface FeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  badge?: string;
}

const Feature = ({ icon, title, description, badge }: FeatureProps) => {
  return (
    <article className="flex flex-col items-center text-center group p-6 rounded-xl transition-all duration-300 hover:bg-gray-900">
      {badge && (
        <Badge className="mb-4 bg-mustard text-black hover:bg-mustard-dark font-medium">
          {badge}
        </Badge>
      )}
      <div className="mb-6 p-5 rounded-full bg-mustard-light/20 group-hover:bg-mustard-light/30 transition-all duration-300">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-3 max-w-xs leading-tight group-hover:text-mustard transition-colors duration-300">
        {title}
      </h3>
      <p className="text-muted-foreground max-w-xs">
        {description}
      </p>
    </article>
  );
};

export function Features() {
  return (
    <section id="features" className="min-h-[80vh] py-24 bg-gradient-to-b from-black to-gray-900 text-white flex items-center">
      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <Badge className="mb-6 bg-mustard text-black hover:bg-mustard-dark px-4 py-1 text-sm">
            The Complete Salon & Barbershop Solution
          </Badge>
          <h2 className="text-3xl md:text-5xl text-center font-bold mb-8 max-w-4xl mx-auto leading-tight">
            Everything You Need to<br />Run Your Salon or Barbershop<br />Efficiently
          </h2>
          <div className="w-24 h-1 bg-mustard mx-auto mb-8"></div>
          <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
            One simple system to manage appointments, walk-ins, payments, and staff. Focus on cutting hair and styling while we handle the business side.
          </p>
        </div>

        {/* Feature Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          <Feature
            badge="Walk-in + Appointments"
            icon={<Calendar className="h-10 w-10 text-mustard" />}
            title="Manage Walk-ins and Bookings"
            description="Handle both walk-in customers and scheduled appointments in one organized system. No more confusion or lost customers."
          />
          <Feature
            badge="Automated Reminders"
            icon={<MessageCircle className="h-10 w-10 text-mustard" />}
            title="Send Appointment Reminders"
            description="Automated SMS and email reminders help ensure customers remember their appointments and show up on time."
          />
          <Feature
            badge="Business Analytics"
            icon={<BarChart className="h-10 w-10 text-mustard" />}
            title="Track Your Business Performance"
            description="See which services are popular, track revenue by stylist or barber, and understand your business better with simple analytics."
          />
          <Feature
            badge="Payment Processing"
            icon={<CreditCard className="h-10 w-10 text-mustard" />}
            title="Accept Payments Easily"
            description="Process card payments, manage tips, and handle cash transactions all in one place. Get paid faster and more securely."
          />
          <Feature
            badge="Staff Management"
            icon={<Users className="h-10 w-10 text-mustard" />}
            title="Organize Your Team"
            description="Let each stylist or barber manage their own schedule and availability. Keep track of who's working when without the hassle."
          />
          <Feature
            badge="Customer Communication"
            icon={<Megaphone className="h-10 w-10 text-mustard" />}
            title="Stay Connected with Customers"
            description="Send professional appointment confirmations, reminders, and updates that keep customers informed and engaged."
          />
        </div>

        <div className="mt-20 text-center">
          <Button asChild className="bg-mustard text-black hover:bg-mustard-dark px-8 py-6 text-lg shadow-lg">
            <Link href="/register">
              Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
