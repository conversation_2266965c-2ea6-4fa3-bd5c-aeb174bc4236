'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Star, 
  Calendar,
  ClipboardList,
  TrendingUp,
  MessageSquare
} from 'lucide-react';

export function AdvancedFeatures() {
  // Track which tab is active for animation purposes
  const [activeTab, setActiveTab] = useState("staff");
  
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };
  
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Powerful Tools for Your Business</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Streamline operations with our staff portal and build trust through customer reviews.
          </p>
        </motion.div>
        
        <Tabs defaultValue="staff" className="max-w-5xl mx-auto" onValueChange={setActiveTab}>
          <div className="flex justify-center mb-8">
            <TabsList className="w-full overflow-x-auto items-center justify-start max-w-md">
              <TabsTrigger value="staff" className="flex items-center gap-2 whitespace-nowrap">
                <Users className="h-4 w-4" />
                Staff Portal
              </TabsTrigger>
              <TabsTrigger value="reviews" className="flex items-center gap-2 whitespace-nowrap">
                <Star className="h-4 w-4" />
                Customer Reviews
              </TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="staff" className="m-0">
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center"
              initial="hidden"
              animate={activeTab === "staff" ? "visible" : "hidden"}
              variants={fadeIn}
            >
              <div>
                <h3 className="text-2xl font-bold mb-6">Empower Your Team</h3>
                <p className="text-gray-600 mb-8">
                  Give your staff the tools they need to provide exceptional service. Our dedicated staff portal makes it easy to manage appointments, track performance, and communicate with clients.
                </p>
                
                <div className="space-y-6">
                  <div className="flex gap-4">
                    <div className="bg-[#FFF8E0] p-2 rounded-md">
                      <Calendar className="h-6 w-6 text-[#F5B800]" />
                    </div>
                    <div>
                      <h4 className="font-semibold">Personal Schedule Management</h4>
                      <p className="text-gray-500 text-sm">Staff can view and manage their own schedules, add notes, and prepare for upcoming appointments.</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-4">
                    <div className="bg-[#FFF8E0] p-2 rounded-md">
                      <ClipboardList className="h-6 w-6 text-[#F5B800]" />
                    </div>
                    <div>
                      <h4 className="font-semibold">Service Completion Tracking</h4>
                      <p className="text-gray-500 text-sm">Record service details, notes, and products used for better customer insights and inventory management.</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-4">
                    <div className="bg-[#FFF8E0] p-2 rounded-md">
                      <TrendingUp className="h-6 w-6 text-[#F5B800]" />
                    </div>
                    <div>
                      <h4 className="font-semibold">Performance Analytics</h4>
                      <p className="text-gray-500 text-sm">Staff can track their commissions, booking rates, and customer satisfaction scores.</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-100 p-6 rounded-xl relative">
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="bg-[#F5B800] text-black p-4">
                    <h3 className="text-xl font-semibold">Staff Portal</h3>
                    <p>Welcome back, Sarah</p>
                  </div>
                  
                  <div className="p-6">
                    <div className="mb-6">
                      <h4 className="font-medium text-gray-500 mb-2">TODAY'S APPOINTMENTS</h4>
                      
                      <div className="space-y-3">
                        <div className="border rounded-md p-3">
                          <div className="flex justify-between">
                            <p className="font-medium">James Wilson</p>
                            <p className="text-sm bg-blue-100 text-blue-700 px-2 py-0.5 rounded">10:00 AM</p>
                          </div>
                          <p className="text-sm text-gray-500">Haircut & Beard Trim (45m)</p>
                        </div>
                        
                        <div className="border rounded-md p-3">
                          <div className="flex justify-between">
                            <p className="font-medium">Maria Lopez</p>
                            <p className="text-sm bg-blue-100 text-blue-700 px-2 py-0.5 rounded">11:30 AM</p>
                          </div>
                          <p className="text-sm text-gray-500">Hair Coloring (90m)</p>
                        </div>
                        
                        <div className="border rounded-md p-3">
                          <div className="flex justify-between">
                            <p className="font-medium">David Chen</p>
                            <p className="text-sm bg-blue-100 text-blue-700 px-2 py-0.5 rounded">2:15 PM</p>
                          </div>
                          <p className="text-sm text-gray-500">Style & Blowout (30m)</p>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-500 mb-2">YOUR PERFORMANCE</h4>
                      <div className="bg-gray-50 p-4 rounded-lg grid grid-cols-3 gap-4 text-center">
                        <div>
                          <p className="text-2xl font-bold text-[#F5B800]">98%</p>
                          <p className="text-xs text-gray-500">On-time</p>
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-[#F5B800]">4.9</p>
                          <p className="text-xs text-gray-500">Rating</p>
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-[#F5B800]">+12%</p>
                          <p className="text-xs text-gray-500">Bookings</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="absolute -bottom-3 -right-3 w-24 h-24 bg-[#FFF8E0] rounded-full -z-10"></div>
                <div className="absolute -top-3 -left-3 w-16 h-16 bg-[#FFF8E0] rounded-full -z-10"></div>
              </div>
            </motion.div>
          </TabsContent>
          
          <TabsContent value="reviews" className="m-0">
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center"
              initial="hidden"
              animate={activeTab === "reviews" ? "visible" : "hidden"}
              variants={fadeIn}
            >
              <div className="order-2 md:order-1 bg-gray-100 p-6 rounded-xl relative">
                <div className="grid grid-cols-1 gap-4">
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium">Alex Johnson</h4>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 text-[#F5B800] fill-[#F5B800]" />
                          ))}
                        </div>
                      </div>
                      <span className="text-xs text-gray-500">2 days ago</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      "Absolutely loved my experience! Sarah did an amazing job with my hair. The atmosphere was so relaxing and everyone was friendly. Will definitely be back!"
                    </p>
                    <div className="mt-2 flex items-center gap-2">
                      <span className="text-xs bg-[#FFF8E0] text-[#F5B800] px-2 py-0.5 rounded-full">Haircut</span>
                      <span className="text-xs bg-[#FFF8E0] text-[#F5B800] px-2 py-0.5 rounded-full">Color</span>
                    </div>
                  </div>
                  
                  <div className="bg-white p-4 rounded-lg shadow-sm">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium">Michael Smith</h4>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className={`h-4 w-4 ${i < 4 ? 'text-[#F5B800] fill-[#F5B800]' : 'text-gray-300'}`} />
                          ))}
                        </div>
                      </div>
                      <span className="text-xs text-gray-500">1 week ago</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      "Great service and professional staff. The haircut was exactly what I wanted. Very clean space and they followed all safety protocols."
                    </p>
                    <div className="mt-2 flex items-center gap-2">
                      <span className="text-xs bg-[#FFF8E0] text-[#F5B800] px-2 py-0.5 rounded-full">Haircut</span>
                      <span className="text-xs bg-[#FFF8E0] text-[#F5B800] px-2 py-0.5 rounded-full">Beard Trim</span>
                    </div>
                  </div>
                  
                  <div className="bg-[#F5B800] text-black p-4 rounded-lg shadow-sm">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">Your Review Response</h4>
                      <MessageSquare className="h-5 w-5" />
                    </div>
                    <p className="text-sm">
                      "Thank you for your feedback, Michael! We're glad you enjoyed your experience and look forward to seeing you again soon."
                    </p>
                    <div className="mt-2 text-right">
                      <span className="text-xs opacity-75">Sent 3 days ago</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="order-1 md:order-2">
                <h3 className="text-2xl font-bold mb-6">Build Your Reputation</h3>
                <p className="text-gray-600 mb-8">
                  Customer reviews are crucial for growing your business. Our review system helps you collect, showcase, and respond to customer feedback, boosting your online reputation.
                </p>
                
                <div className="space-y-6">
                  <div className="flex gap-4">
                    <div className="bg-[#FFF8E0] p-2 rounded-md">
                      <Star className="h-6 w-6 text-[#F5B800]" />
                    </div>
                    <div>
                      <h4 className="font-semibold">Automated Review Requests</h4>
                      <p className="text-gray-500 text-sm">Automatically request reviews after completed appointments with customizable timing.</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-4">
                    <div className="bg-[#FFF8E0] p-2 rounded-md">
                      <MessageSquare className="h-6 w-6 text-[#F5B800]" />
                    </div>
                    <div>
                      <h4 className="font-semibold">Response Management</h4>
                      <p className="text-gray-500 text-sm">Respond to reviews directly through the dashboard to show customers you value their feedback.</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-4">
                    <div className="bg-[#FFF8E0] p-2 rounded-md">
                      <TrendingUp className="h-6 w-6 text-[#F5B800]" />
                    </div>
                    <div>
                      <h4 className="font-semibold">Satisfaction Analytics</h4>
                      <p className="text-gray-500 text-sm">Track customer satisfaction trends over time and by service type to identify improvement areas.</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
