'use client';

import Image from 'next/image';
import { Quote, <PERSON> } from 'lucide-react';

const testimonials = [
  {
    name: "<PERSON>",
    location: "KK Barbershop - Nairobi",
    avatar: "/avatar.jpg",
    quote: "GroomBook eliminated the chaos in my shop. No more empty chairs, no more no-shows. I went from losing $2,000 monthly to being fully booked every day. My barbers are happier and I'm making 40% more revenue.",
    metric: "40% more revenue"
  },
  {
    name: "<PERSON>",
    location: "Crown Cuts - London",
    avatar: "/avatar2.jpg",
    quote: "Best investment I've made. The walk-in queue system is genius - customers love knowing exactly when they'll be served. We've gone from 60% chair utilization to 95% in just 2 months.",
    metric: "95% chair utilization"
  },
  {
    name: "<PERSON>",
    location: "Elite Barbershop - Dubai",
    avatar: "/avatar3.jpg",
    quote: "The no-show reduction is incredible. Used to lose 3-4 appointments daily, now it's maybe 1 per week. The automated reminders work perfectly and my staff stress is gone.",
    metric: "85% fewer no-shows"
  }
];

export function Testimonial() {
  return (
    <section id="social-proof" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex justify-center items-center gap-2 mb-4">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className="h-6 w-6 text-yellow-400 fill-current" />
            ))}
            <span className="ml-2 text-lg font-semibold">4.9/5 from 3,000+ barbershops</span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Join Barbershop Owners Making More Money
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Real barbershop owners sharing their results after switching to GroomBook
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="flex justify-center mb-4">
                <Quote className="h-8 w-8 text-mustard" />
              </div>
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                &quot;{testimonial.quote}&quot;
              </blockquote>
              
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden relative mr-4">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                </div>
                <div>
                  <p className="font-bold">{testimonial.name}</p>
                  <p className="text-sm text-muted-foreground">{testimonial.location}</p>
                </div>
              </div>
              
              <div className="bg-green-100 text-green-700 font-bold px-3 py-2 rounded-full text-center text-sm">
                {testimonial.metric}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <div className="bg-mustard/10 border border-mustard/20 rounded-lg p-6 max-w-2xl mx-auto">
            <p className="text-lg font-semibold mb-2">
              🔥 This week only: Join now and get 3 months free
            </p>
            <p className="text-muted-foreground mb-4">
              Limited to the first 50 barbershops. 37 spots remaining.
            </p>
            <a 
              href="/register"
              className="bg-mustard hover:bg-mustard-dark text-black font-bold py-3 px-8 rounded-lg inline-block transition-all duration-300 hover:shadow-lg"
            >
              Claim Your 3 Free Months Now
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
