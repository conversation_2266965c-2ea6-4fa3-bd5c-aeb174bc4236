'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON>lipboardCheck,
  CreditCard,
  ArrowRight,
  CheckCircle2,
  Smartphone,
  Receipt,
  MessageSquare,
  CreditCard as CardIcon,
  LineChart
} from 'lucide-react';
import Link from 'next/link';

interface ProcessStepProps {
  number: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  forSalon?: boolean;
}

const ProcessStep = ({ number, title, description, icon, forSalon = false }: ProcessStepProps) => {
  return (
    <div className={`relative flex items-start gap-6 p-6 rounded-xl transition-all duration-300 hover:bg-mustard/10 hover:shadow-md ${forSalon ? 'border-l-4 border-mustard' : ''} group`}>
      <div className={`flex-shrink-0 w-14 h-14 rounded-full flex items-center justify-center shadow-md transition-all duration-300 group-hover:scale-110 ${forSalon ? 'bg-mustard text-black' : 'bg-black text-mustard'}`}>
        {forSalon ? icon : <span className="text-xl font-bold">{number}</span>}
      </div>
      <div className="flex-1">
        <h3 className="text-xl font-bold mb-3 group-hover:text-mustard transition-colors duration-300">{title}</h3>
        <p className="text-muted-foreground leading-relaxed">{description}</p>
      </div>
    </div>
  );
};

export function Process() {
  return (
    <section id='how-it-works' className="min-h-[80vh] py-24 bg-gradient-to-b from-mustard-light to-white relative overflow-hidden">
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-mustard/10 to-transparent"></div>
      <div className="absolute -top-24 -right-24 w-96 h-96 bg-mustard/5 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-24 -left-24 w-96 h-96 bg-mustard/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-20">
          <Badge className="mb-6 bg-mustard text-black hover:bg-mustard-dark px-4 py-1 text-sm font-medium">
            How It Works
          </Badge>
          <h2 className="text-3xl md:text-5xl text-center font-bold mb-8 max-w-4xl mx-auto leading-tight">
            Simple Process for<br />Barbershops and Clients
          </h2>
          <div className="w-24 h-1 bg-mustard mx-auto mb-8"></div>
          <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
            Get organized in minutes. Simple for customers to book, easy for you to manage.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start mt-20 relative">
          <div className="hidden lg:block absolute left-1/2 top-20 bottom-20 w-px bg-gray-200 -translate-x-1/2"></div>
          {/* Left side - Customer Process */}
          <div className="relative">
            <div className="flex items-center mb-10">
              <Smartphone className="h-8 w-8 text-mustard mr-3" />
              <h3 className="text-2xl font-bold">For Your Customers</h3>
            </div>

            <div className="relative">
              <div className="space-y-12 relative z-10">
                <ProcessStep
                  number={1}
                  title="Book Online or Walk In"
                  description="Customers can schedule ahead online or just show up. The system handles both seamlessly."
                  icon={<ClipboardCheck className="h-6 w-6" />}
                />

                <ProcessStep
                  number={2}
                  title="Get Reminders"
                  description="Automatic text and email reminders help customers remember their appointments."
                  icon={<MessageSquare className="h-6 w-6" />}
                />

                <ProcessStep
                  number={3}
                  title="Pay & Leave Happy"
                  description="Quick payments with card or cash, digital receipts, and easy checkout process."
                  icon={<CreditCard className="h-6 w-6" />}
                />
              </div>
            </div>
          </div>

          {/* Right side - Barbershop Owner Process */}
          <div>
            <div className="flex items-center mb-10">
              <Receipt className="h-8 w-8 text-mustard mr-3" />
              <h3 className="text-2xl font-bold">For Barbershop Owners</h3>
            </div>

            <div className="space-y-12">
              <ProcessStep
                number={1}
                title="Organize Your Schedule"
                description="See all appointments and walk-ins in one clear dashboard. Never double-book or miss a customer again."
                icon={<ClipboardCheck className="h-6 w-6" />}
                forSalon={true}
              />

              <ProcessStep
                number={2}
                title="Track Your Business"
                description="Monitor revenue, popular services, and staff performance with simple reports and analytics."
                icon={<LineChart className="h-6 w-6" />}
                forSalon={true}
              />

              <ProcessStep
                number={3}
                title="Get Paid Efficiently"
                description="Process card payments, manage tips, and keep accurate records of all transactions."
                icon={<CardIcon className="h-6 w-6" />}
                forSalon={true}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
