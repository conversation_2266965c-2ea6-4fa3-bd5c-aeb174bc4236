'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

export function LoyaltyShowcase() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  
  const controls = useAnimation();
  
  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);
  
  const variants = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4 }
    }
  };
  
  const phoneVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.8, delay: 0.3 }
    }
  };
  
  const pointItems = [
    {
      title: "Earn Points",
      description: "Customers earn points with every service",
      value: "+10"
    },
    {
      title: "Referral Bonus",
      description: "Reward for each successful referral",
      value: "+100"
    },
    {
      title: "Birthday Reward",
      description: "Special gift on customer's birthday",
      value: "+50"
    },
    {
      title: "Review Bonus",
      description: "Points for leaving verified reviews",
      value: "+25"
    }
  ];
  
  return (
    <section className="py-20 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          <motion.div 
            className="max-w-xl"
            ref={ref}
            initial="hidden"
            animate={controls}
            variants={variants}
          >
            <motion.h2 
              className="text-3xl md:text-4xl font-bold mb-6"
              variants={itemVariants}
            >
              Build Customer Loyalty & <span className="text-[#F5B800]">Drive Referrals</span>
            </motion.h2>
            
            <motion.p 
              className="text-lg text-gray-600 mb-8"
              variants={itemVariants}
            >
              Our integrated loyalty and referral system helps you retain existing customers and attract new ones through word-of-mouth marketing.
            </motion.p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {pointItems.map((item, index) => (
                <motion.div 
                  key={index}
                  className="bg-white rounded-lg p-5 shadow-md border border-gray-100 flex items-start gap-4"
                  variants={itemVariants}
                >
                  <div className="w-16 h-16 flex-shrink-0 bg-[#FFF8E0] rounded-full flex items-center justify-center text-[#F5B800] font-bold text-xl">
                    {item.value}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{item.title}</h3>
                    <p className="text-gray-500 text-sm">{item.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
          
          <motion.div 
            className="relative"
            variants={phoneVariants}
            initial="hidden"
            animate={controls}
          >
            <div className="w-[320px] h-[650px] bg-gray-900 rounded-[3rem] p-3 shadow-2xl relative overflow-hidden">
              <div className="absolute top-0 left-1/2 -translate-x-1/2 w-40 h-7 bg-black rounded-b-2xl"></div>
              <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
                <div className="bg-[#F5B800] h-40 w-full pt-12 px-6">
                  <div className="text-black">
                    <h3 className="text-2xl font-bold">Loyalty Program</h3>
                    <p className="opacity-80">Your rewards</p>
                  </div>
                  <div className="absolute right-6 top-14">
                    <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                      <div className="text-3xl font-bold">540</div>
                      <div className="text-xs font-medium text-center">POINTS</div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="mb-6">
                    <h4 className="font-medium text-gray-500 mb-2">AVAILABLE REWARDS</h4>
                    
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg flex justify-between items-center">
                        <div>
                          <p className="font-medium">Free Haircut</p>
                          <p className="text-sm text-gray-500">Any style of your choice</p>
                        </div>
                        <div className="bg-[#F5B800] text-black px-3 py-1 rounded-md font-medium">
                          500 pts
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg flex justify-between items-center">
                        <div>
                          <p className="font-medium">25% Off Next Visit</p>
                          <p className="text-sm text-gray-500">Valid for 30 days</p>
                        </div>
                        <div className="bg-[#F5B800] text-black px-3 py-1 rounded-md font-medium">
                          300 pts
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-500 mb-2">REFERRAL PROGRAM</h4>
                    
                    <div className="bg-[#FFF8E0] p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-3">
                        <p className="font-medium">Your Referral Code</p>
                        <div className="font-mono font-bold text-lg">GROOM123</div>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">Share with friends and you both get 100 points when they book their first appointment</p>
                      <button className="w-full bg-[#F5B800] text-black py-2 rounded-md font-medium">
                        Share Referral Link
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="absolute -bottom-10 -right-10 w-52 h-52 bg-[#FFF8E0] rounded-full -z-10"></div>
            <div className="absolute -top-10 -left-10 w-32 h-32 bg-[#FFF8E0] rounded-full -z-10"></div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
