'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { WorldMap } from './WorldMap';
import { motion } from 'framer-motion';
import { gtag_report_conversion } from '@/utils/googleAdsTracking';
import { pricingPlans } from '@/lib/billing';

export function CTA() {
  return (
    <section className="py-32 bg-gradient-to-b from-mustard-light to-white relative overflow-hidden">
      <WorldMap />
      
      <div className="container mx-auto px-4 relative">
        <motion.div 
          className="max-w-4xl mx-auto text-center space-y-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600">
            Ready to Get Organized?
          </h2>
          <p className="text-xl text-muted-foreground">
            Start managing your barbershop more efficiently with GroomBook. Simple setup, easy to use.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-3xl mx-auto">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-700 font-bold text-lg">✓ 14-Day Free Trial</p>
              <p className="text-green-600 text-sm">Test everything first</p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-700 font-bold text-lg">✓ Easy Setup</p>
              <p className="text-blue-600 text-sm">Get started quickly</p>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <p className="text-purple-700 font-bold text-lg">✓ No Contracts</p>
              <p className="text-purple-600 text-sm">Cancel anytime</p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              asChild 
              size="lg"
              className="bg-mustard text-black hover:bg-mustard-dark text-xl px-12 py-6 shadow-xl"
              onClick={() => gtag_report_conversion('/register')}
            >
              <Link href="/register">Start Free Trial</Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
