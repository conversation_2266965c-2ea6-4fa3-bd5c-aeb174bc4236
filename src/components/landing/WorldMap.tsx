'use client';

import { useEffect, useState } from 'react';
import DottedMap from 'dotted-map';

export function WorldMap() {
  const [mapSvg, setMapSvg] = useState<string>('');

  useEffect(() => {
    const map = new DottedMap({ height: 60, grid: 'diagonal' });

    // Add pins for major business hubs
    const pins = [
      { lat: 40.7128, lng: -74.0060 }, // New York
      { lat: 51.5074, lng: -0.1278 },  // London
      { lat: 35.6762, lng: 139.6503 }, // Tokyo
      { lat: 1.3521, lng: 103.8198 },  // Singapore
      { lat: -33.8688, lng: 151.2093 }, // Sydney
      { lat: -1.2921, lng: 36.8219 },   // Nairobi
      { lat: 6.5244, lng: 3.3792 }      // Lagos
    ];

    pins.forEach(pin => {
      map.addPin({
        lat: pin.lat,
        lng: pin.lng,
        svgOptions: { color: '#231F20', radius: 0.4 }
      });
    });

    const svgMap = map.getSVG({
      radius: 0.22,
      color: '#423B38',
      shape: 'circle',
      backgroundColor: 'transparent',
    });

    setMapSvg(svgMap);
  }, []);

  return (
    <div 
      className="absolute inset-0 w-full h-full opacity-20 pointer-events-none"
      dangerouslySetInnerHTML={{ __html: mapSvg }}
    />
  );
}
