'use client';

import { 
  Award, 
  Share2, 
  Users, 
  Star, 
  MessageSquare,
  Gift,
  BadgePercent
} from 'lucide-react';
import { motion } from 'framer-motion';

export function ExclusiveFeatures() {
  const features = [
    {
      title: "Loyalty Program",
      description: "Reward your loyal customers with points for each visit. They can redeem points for discounts, free services, or exclusive offers.",
      icon: <Award className="h-10 w-10 text-[#F5B800]" />,
      delay: 0,
    },
    {
      title: "Customer Referrals",
      description: "Grow your business through word-of-mouth. Customers earn bonus points when they refer friends, creating a win-win situation.",
      icon: <Share2 className="h-10 w-10 text-[#F5B800]" />,
      delay: 0.1,
    },
    {
      title: "Staff Portal",
      description: "Empower your staff with their own dedicated portal to manage appointments, track commissions, and provide better service.",
      icon: <Users className="h-10 w-10 text-[#F5B800]" />,
      delay: 0.2,
    },
    {
      title: "Customer Reviews",
      description: "Build your reputation with verified customer reviews. Showcase positive experiences to attract new clients.",
      icon: <Star className="h-10 w-10 text-[#F5B800]" />,
      delay: 0.3,
    },
    {
      title: "Automated Rewards",
      description: "Special rewards for birthdays and milestones are automatically sent, making your customers feel valued.",
      icon: <Gift className="h-10 w-10 text-[#F5B800]" />,
      delay: 0.4,
    },
    {
      title: "Promotional Campaigns",
      description: "Create and track targeted marketing campaigns with special offers to boost bookings during slow periods.",
      icon: <BadgePercent className="h-10 w-10 text-[#F5B800]" />,
      delay: 0.5,
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2 
            className="text-3xl md:text-4xl font-bold mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Exclusive Features That Set You Apart
          </motion.h2>
          <motion.p 
            className="text-xl text-gray-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            Our unique tools help you build customer loyalty, encourage referrals, and manage your team effectively.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div 
              key={index}
              className="bg-white p-8 rounded-lg shadow-md hover:shadow-lg transition-shadow"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: feature.delay }}
            >
              <div className="mb-5">{feature.icon}</div>
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        <motion.div 
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="inline-block bg-[#FFF8E0] px-6 py-4 rounded-lg">
            <p className="text-lg font-semibold text-gray-800">
              On average, businesses using our loyalty system see a <span className="text-[#F5B800] font-bold">30% increase</span> in repeat customer visits.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
