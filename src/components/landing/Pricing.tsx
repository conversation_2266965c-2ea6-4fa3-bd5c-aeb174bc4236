'use client';

import { Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import posthog from 'posthog-js';
import { gtag_report_conversion } from '@/utils/googleAdsTracking';
import { pricingPlans } from '@/lib/billing';
import { PricingSchema } from '@/components/seo/PricingSchema';

interface PricingFeature {
  text: string;
}

interface PricingTierProps {
  name: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
  billingCycle: 'monthly' | 'yearly';
}

type PlanName = 'Free' | 'Basic' | 'Pro' | 'Enterprise';

const getPlanPrice = (planName: string, cycle: 'monthly' | 'yearly') => {
  const prices = {
    'Free': { monthly: 0, yearly: 0 },
    'Basic': { monthly: 5, yearly: 50 },
    'Pro': { monthly: 19.90, yearly: 190 },
    'Enterprise': { monthly: 45.00, yearly: 450 }
  };
  const price = prices[planName as PlanName]?.[cycle];
  return price === 0 ? '0' : price?.toFixed(2);
};

const PricingTier = ({
  name,
  description,
  features,
  isPopular,
  billingCycle,
}: PricingTierProps) => {

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-visible relative ${isPopular ? 'border-2 border-primary shadow-xl scale-105' : ''}`}>
      {isPopular && (
        <div className="absolute -top-4 left-1/2 -translate-x-1/2 z-10">
          <div className="bg-mustard text-black font-bold px-4 py-1 rounded-full text-sm">
            Most Popular
          </div>
        </div>
      )}
      <div className="p-6">
        <h3 className="text-2xl font-bold">{name}</h3>
        <p className="text-sm text-muted-foreground mt-1">{description}</p>
        <div className="mt-4 flex items-baseline">
          <span className="text-4xl font-bold">
            {name === 'Free' ? 'Free' : `$${getPlanPrice(name, billingCycle)}`}
          </span>
          {name !== 'Free' && (
            <span className="ml-1 text-muted-foreground">
              /{billingCycle === 'monthly' ? 'mo' : 'yr'}
            </span>
          )}
        </div>
        {billingCycle === 'yearly' && (
          <div className="mt-2">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              2 Months Free
            </Badge>
          </div>
        )}
      </div>
      <div className="p-6 border-t border-gray-100">
        <h4 className="font-medium mb-4">Includes:</h4>
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">{feature.text}</span>
            </li>
          ))}
        </ul>
      </div>
      <div className="p-6 border-t border-gray-100">
        <Button
          asChild
          className={`w-full ${isPopular ? 'bg-primary text-primary-foreground hover:bg-primary/90' : 'bg-mustard text-black hover:bg-mustard-dark'}`}
          onClick={() => {
            posthog.capture('plan_selected', {
              plan: name.toLowerCase(),
              billingCycle
            });
            gtag_report_conversion(`/register?plan=${name.toLowerCase()}&billing=${billingCycle}`);
          }}
        >
          <Link href={`/register?plan=${name.toLowerCase()}&billing=${billingCycle}`}>
            {name === 'Free' ? 'Get Started Free' : 'Start Free Trial'}
          </Link>
        </Button>
        {name !== 'Free' && (
          <p className="text-xs text-center mt-3 text-gray-500">{pricingPlans.basic.trialDays}-day trial</p>
        )}
      </div>
    </div>
  );
};

export function Pricing() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  // Track billing cycle changes
  useEffect(() => {
    posthog.capture('pricing_billing_cycle_changed', {
      billingCycle: billingCycle
    });
  }, [billingCycle]);

  const plans = [
    {
      name: "Free",
      description: "Perfect for getting started",
      features: [
        { text: "1 salon space" },
        { text: "Up to 400 customers" },
        { text: "Up to 30 appointments/month" },
        { text: "Basic appointment scheduling" },
        { text: "Email notifications" },
        { text: "Community support" },
      ],
    },
    {
      name: "Basic",
      description: "For 1-2 chair shops",
      features: [
        { text: "2 salon spaces" },
        { text: "Up to 1200 customers" },
        { text: "Unlimited appointments" },
        { text: "Basic appointment scheduling" },
        { text: "Email notifications" },
        { text: "Staff Portal" },
      ],
    },
    {
      name: "Pro", 
      description: "Most barbershops choose this",
      isPopular: true,
      features: [
        { text: "Everything in Basic" },
        { text: "Unlimited customers" },
        { text: "Up to 5 barber chairs" },
        { text: "SMS reminders (95% show rate)" },
        { text: "Revenue tracking by barber" },
        { text: "Customer loyalty program" },
        { text: "Marketing tools" },
        { text: "Analytics dashboard" },
        { text: "Priority support" },
        { text: "Referral system" },
      ],
    },
  ];


  return (
    <section id="pricing" className="py-20">
      {/* Add structured data for pricing plans - good for SEO */}
      <PricingSchema
        planName="Free"
        description="Perfect for getting started with salon management"
        currency="USD"
        price={0}
        billingPeriod={billingCycle}
        trialDays={0}
      />
      <PricingSchema
        planName="Basic"
        description="Get started with essential salon management tools"
        currency="USD"
        price={billingCycle === 'monthly' ? 5 : 50}
        billingPeriod={billingCycle}
        trialDays={pricingPlans.basic.trialDays}
      />
      <PricingSchema
        planName="Pro"
        description="Advanced features for growing businesses"
        currency="USD"
        price={billingCycle === 'monthly' ? 19.9 : 190}
        billingPeriod={billingCycle}
        trialDays={pricingPlans.pro.trialDays}
      />
      
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Stop Losing Money - ROI Guaranteed</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Most barbershops save $2,400+ monthly by eliminating no-shows and filling empty chairs. Choose your plan and start profiting immediately.
          </p>
          <div className="mt-4">
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-sm px-3 py-1">
              {`${pricingPlans.basic.trialDays}-Day Free Trial - See Results or Cancel`}
            </Badge>
          </div>

          <div className="mt-8 flex items-center justify-center gap-3">
            <span className={`text-sm ${billingCycle === 'monthly' ? 'font-semibold' : ''}`}>Monthly</span>
            <button
              type="button"
              role="switch"
              aria-checked={billingCycle === 'yearly'}
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors 
                ${billingCycle === 'yearly' ? 'bg-green-600' : 'bg-gray-200'}`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                ${billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'}`}
              />
            </button>
            <span className={`text-sm ${billingCycle === 'yearly' ? 'font-semibold' : ''}`}>
              Yearly
              <Badge className="ml-2 bg-green-50 text-green-700 border-green-200">Save 16.7%</Badge>
            </span>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <PricingTier
              key={plan.name}
              name={plan.name}
              description={plan.description}
              features={plan.features}
              isPopular={plan.isPopular}
              billingCycle={billingCycle}
            />
          ))}
        </div>

        <div className="text-center mt-10 space-y-2">
          <p className="text-sm text-muted-foreground">All prices are in USD. Yearly plans are billed annually and include 2 months free.</p>
          <p className="text-sm font-medium text-green-600">Average barbershop saves $2,400+ monthly - try {pricingPlans.basic.trialDays} days free to see your results.</p>
        </div>
      </div>
    </section>
  );
}
