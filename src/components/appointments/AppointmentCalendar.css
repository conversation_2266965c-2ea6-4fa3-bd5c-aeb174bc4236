/* FullCalendar custom styles */
:root {
  /* Use the project's mustard color scheme */
  --fc-border-color: hsl(var(--border));
  --fc-button-text-color: hsl(var(--primary-foreground));
  --fc-button-bg-color: hsl(var(--primary));
  --fc-button-border-color: hsl(var(--primary));
  --fc-button-hover-bg-color: hsl(var(--primary) / 0.9);
  --fc-button-hover-border-color: hsl(var(--primary) / 0.9);
  --fc-button-active-bg-color: hsl(var(--primary) / 0.8);
  --fc-button-active-border-color: hsl(var(--primary) / 0.8);
  --fc-today-bg-color: hsl(var(--accent) / 0.5);
  --fc-event-bg-color: hsl(var(--primary));
  --fc-event-border-color: hsl(var(--primary));
}

.fc {
  /* Base styles */
  --fc-small-font-size: 0.8125rem;
  --fc-page-bg-color: hsl(var(--background));
  --fc-neutral-bg-color: hsl(var(--background));
  --fc-list-event-hover-bg-color: hsl(var(--accent));
  --fc-highlight-color: hsl(var(--accent));

  /* Override with mustard color scheme */
  --fc-event-bg-color: hsl(var(--primary));
  --fc-event-border-color: hsl(var(--primary));
  --fc-today-bg-color: hsl(var(--accent) / 0.5);

  /* Button styling to match project */
  --fc-button-bg-color: hsl(var(--primary));
  --fc-button-text-color: hsl(var(--primary-foreground));
  --fc-button-border-color: hsl(var(--primary));
  --fc-button-hover-bg-color: hsl(var(--primary) / 0.9);
  --fc-button-hover-border-color: hsl(var(--primary) / 0.9);
  --fc-button-active-bg-color: hsl(var(--primary) / 0.8);
  --fc-button-active-border-color: hsl(var(--primary) / 0.8);

  /* Non-primary buttons (like "today") */
  --fc-button-secondary-bg-color: hsl(var(--secondary));
  --fc-button-secondary-text-color: hsl(var(--secondary-foreground));
  --fc-button-secondary-border-color: hsl(var(--secondary));

  font-family: var(--font-jakarta), sans-serif;
}

/* Toolbar styling */
.fc .fc-toolbar {
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem !important;
  padding: 0.5rem 0;
  align-items: center;
}

.fc .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  line-height: 1.2;
}

/* Button styling to match project's button component */
.fc .fc-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  font-size: 0.875rem;
  font-weight: 500;
  height: 2.25rem;
  padding: 0 1rem;
  border-radius: 0.375rem;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.fc .fc-button-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: 1px solid transparent;
}

.fc .fc-button-primary:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.fc .fc-button-primary:not(:disabled):active,
.fc .fc-button-primary:not(:disabled).fc-button-active {
  background-color: hsl(var(--primary) / 0.8);
  color: hsl(var(--primary-foreground));
  border-color: transparent;
}

/* Today button styling */
.fc .fc-today-button {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.fc .fc-today-button:hover {
  background-color: hsl(var(--secondary) / 0.9);
}

.fc .fc-today-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Fix button text capitalization */
.fc button {
  text-transform: capitalize;
}

/* Today highlight */
.fc .fc-daygrid-day.fc-day-today,
.fc .fc-timegrid-col.fc-day-today {
  background-color: hsl(var(--accent) / 0.3);
}

/* Row labels and time slots */
.fc .fc-timegrid-axis-cushion,
.fc .fc-timegrid-slot-label-cushion {
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  font-size: 0.75rem;
  padding: 0.25rem;
}

/* Improve time slot visibility */
.fc .fc-timegrid-slot {
  height: 3rem;
  border-bottom: 1px solid hsl(var(--border) / 0.5);
}

.fc .fc-timegrid-slot-label {
  vertical-align: middle;
}

/* Event styling */
.fc .fc-event {
  border-radius: 0.25rem;
  padding: 0.25rem;
  font-size: 0.75rem;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.fc .fc-event-main {
  padding: 0.25rem 0.25rem;
}

/* Empty state */
.fc .fc-list-empty {
  background-color: transparent;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Header toolbar */
.fc-header-toolbar {
  margin-bottom: 1.5rem !important;
}

/* Event title and time */
.fc-event-title {
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.2;
}

.fc-event-time {
  font-size: 0.7rem;
  opacity: 0.9;
  font-weight: 400;
}

/* Status-based colors (these will override the default colors for specific statuses) */
.fc-event.status-completed {
  background-color: #10B981 !important;
  border-color: #059669 !important;
}

.fc-event.status-cancelled {
  background-color: #EF4444 !important;
  border-color: #DC2626 !important;
}

.fc-event.status-scheduled {
  background-color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary) / 0.8) !important;
}

.fc-event.status-confirmed {
  background-color: #3B82F6 !important;
  border-color: #2563EB !important;
}

.fc-event.status-in_progress {
  background-color: #8B5CF6 !important;
  border-color: #7C3AED !important;
}

.fc-event.status-no-show {
  background-color: #9CA3AF !important;
  border-color: #6B7280 !important;
}

/* Enhanced event styling */
.fc-event {
  border-radius: 4px !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  transition: transform 0.1s ease-in-out !important;
  border-width: 0 !important;
  border-left-width: 3px !important;
}

.fc-event:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.fc-event-main-content {
  padding: 3px 6px !important;
}

/* Day and week view specific styling */
.fc-timegrid-event {
  margin: 1px 0 !important;
}

.fc-timegrid-event-harness {
  margin: 0 2px !important;
}

/* Month view specific styling */
.fc-daygrid-event {
  margin: 1px 2px !important;
  padding: 2px 4px !important;
}

/* Improve day headers */
.fc-col-header-cell {
  padding: 8px 0 !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  font-size: 0.75rem !important;
  color: hsl(var(--muted-foreground)) !important;
  background-color: hsl(var(--muted) / 0.2) !important;
}

/* Improve day cells */
.fc-daygrid-day {
  transition: background-color 0.2s ease !important;
}

.fc-daygrid-day:hover {
  background-color: hsl(var(--accent) / 0.1) !important;
}

/* Tooltip styling */
.appointment-tooltip {
  animation: fadeIn 0.2s ease-in-out;
  background-color: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: var(--radius) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fix time slot labels */
.fc-timegrid-axis {
  padding: 0 8px !important;
  width: 60px !important;
}

.fc-timegrid-slot-label {
  width: 60px !important;
}

.fc-timegrid-slot-label-cushion {
  padding-right: 8px !important;
  text-align: right !important;
}

/* Improve selection highlight */
.fc-highlight {
  background-color: hsl(var(--primary) / 0.15) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fc-header-toolbar {
    flex-direction: column;
    gap: 1rem;
  }

  .fc-toolbar-title {
    font-size: 1rem !important;
    text-align: center;
    width: 100%;
  }

  .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .fc-button {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .fc-timegrid-axis,
  .fc-timegrid-slot-label {
    width: 40px !important;
  }

  .fc-timegrid-slot-label-cushion {
    font-size: 0.65rem !important;
  }

  /* Smaller events on mobile */
  .fc-event-main-content {
    padding: 2px 4px !important;
  }

  .fc-event-title {
    font-size: 0.7rem !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
