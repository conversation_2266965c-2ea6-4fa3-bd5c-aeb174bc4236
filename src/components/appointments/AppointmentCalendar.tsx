import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';
import { AppointmentWithDetails } from '@/services/appointmentService';
import { parseISO, addMinutes, format } from 'date-fns';
import './AppointmentCalendar.css';
import './AppointmentHoverStyles.css';
import { useState, useRef, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useAppSelector } from "@/store/hooks";
import { formatPrice } from "@/utils/price-formatter";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface AppointmentCalendarProps {
    appointments: AppointmentWithDetails[];
    onEventClick: (appointment: AppointmentWithDetails) => void;
    onDateSelect?: (start: Date, end: Date) => void;
    loading?: boolean;
    onEventDrop?: (appointmentId: string, newStart: Date, newEnd: Date) => void;
}

export default function AppointmentCalendar({
    appointments,
    onEventClick,
    onDateSelect,
    loading = false,
    onEventDrop
}: AppointmentCalendarProps) {
    const calendarRef = useRef<any>(null);
    const [currentView, setCurrentView] = useState<string>('timeGridWeek');
    const { currentSpace } = useAppSelector((state) => state.space);
    const [tooltipContent, setTooltipContent] = useState<AppointmentWithDetails | null>(null);
    const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
    const [showTooltip, setShowTooltip] = useState(false);
    const [hoveredEventId, setHoveredEventId] = useState<string | null>(null);

    // Determine initial view based on screen size
    const getInitialView = () => {
        if (typeof window !== 'undefined') {
            return window.innerWidth < 768 ? 'timeGridDay' : 'timeGridWeek';
        }
        return 'timeGridWeek'; // Default for SSR
    };

    const [initialView, setInitialView] = useState(getInitialView());

    // Handle window resize to adjust calendar view on mobile
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 768 && currentView === 'timeGridWeek') {
                if (calendarRef.current) {
                    calendarRef.current.getApi().changeView('timeGridDay');
                    setCurrentView('timeGridDay');
                }
            } else if (window.innerWidth >= 768 && currentView === 'timeGridDay') {
                if (calendarRef.current) {
                    calendarRef.current.getApi().changeView('timeGridWeek');
                    setCurrentView('timeGridWeek');
                }
            }
        };

        window.addEventListener('resize', handleResize);
        // Initial check
        handleResize();

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [currentView]);

    // Map appointments to calendar events
    const events = appointments.map(appointment => {
        // Parse the time string into hours and minutes
        let hours = 10; // Default to 10 AM if parsing fails
        let minutes = 0;

        try {
            if (appointment.time) {
                const timeParts = appointment.time.split(':');
                if (timeParts.length >= 2) {
                    hours = parseInt(timeParts[0], 10);
                    minutes = parseInt(timeParts[1], 10);

                    // Handle AM/PM format
                    if (appointment.time.toLowerCase().includes('pm') && hours < 12) {
                        hours += 12;
                    }
                    if (appointment.time.toLowerCase().includes('am') && hours === 12) {
                        hours = 0;
                    }
                }
            }
        } catch (error) {
            console.error("Error parsing time:", error);
        }

        // Create the start date
        let startDate: Date;

        try {
            if (typeof appointment.date === 'string') {
                try {
                    startDate = parseISO(appointment.date);
                } catch (e) {
                    console.error("Error parsing ISO date:", e);
                    startDate = new Date();
                }
                startDate.setHours(hours, minutes, 0, 0);
            } else if (appointment.date instanceof Date) {
                startDate = new Date(appointment.date);
                startDate.setHours(hours, minutes, 0, 0);
            } else if (appointment.startTime) {
                // Try to use startTime if date is not available
                try {
                    startDate = typeof appointment.startTime === 'string'
                        ? parseISO(appointment.startTime)
                        : new Date(appointment.startTime);
                } catch (e) {
                    console.error("Error parsing startTime:", e);
                    startDate = new Date();
                    startDate.setHours(hours, minutes, 0, 0);
                }
            } else {
                // Handle the case where date might be a Timestamp or other object
                // Try to convert to ISO string first
                const dateStr = String(appointment.date || '');
                try {
                    startDate = parseISO(dateStr);
                    startDate.setHours(hours, minutes, 0, 0);
                } catch (e) {
                    console.error("Error parsing date:", e);
                    // Fallback to current date if parsing fails
                    startDate = new Date();
                    startDate.setHours(hours, minutes, 0, 0);
                }
            }
        } catch (error) {
            console.error("Error creating start date:", error);
            // Fallback to current date
            startDate = new Date();
            startDate.setHours(hours, minutes, 0, 0);
        }

        // Ensure the date is valid
        if (isNaN(startDate.getTime())) {
            console.error("Invalid date detected, using current date");
            startDate = new Date();
            startDate.setHours(hours, minutes, 0, 0);
        }

        // Calculate end time based on service duration
        let duration = 30; // Default duration

        if (appointment.services && appointment.services.length > 0) {
            const firstService = appointment.services[0];
            if (typeof firstService.duration === 'number') {
                duration = firstService.duration;
            } else if (typeof firstService.duration === 'string') {
                // Try to parse duration from string like "30 min" or "1 hr 30 min"
                const durationStr = firstService.duration;
                if (durationStr.includes('hr')) {
                    const hourMatch = durationStr.match(/(\d+)\s*hr/);
                    const minuteMatch = durationStr.match(/(\d+)\s*min/);

                    let hours = 0;
                    let minutes = 0;

                    if (hourMatch && hourMatch[1]) {
                        hours = parseInt(hourMatch[1], 10);
                    }

                    if (minuteMatch && minuteMatch[1]) {
                        minutes = parseInt(minuteMatch[1], 10);
                    }

                    duration = (hours * 60) + minutes;
                } else {
                    // Just try to parse the number
                    const numMatch = durationStr.match(/(\d+)/);
                    if (numMatch && numMatch[1]) {
                        duration = parseInt(numMatch[1], 10);
                    }
                }
            }
        } else if (typeof appointment.duration === 'string') {
            // Try to parse duration from the appointment directly
            const durationStr = appointment.duration;
            if (durationStr.includes('hr')) {
                const hourMatch = durationStr.match(/(\d+)\s*hr/);
                const minuteMatch = durationStr.match(/(\d+)\s*min/);

                let hours = 0;
                let minutes = 0;

                if (hourMatch && hourMatch[1]) {
                    hours = parseInt(hourMatch[1], 10);
                }

                if (minuteMatch && minuteMatch[1]) {
                    minutes = parseInt(minuteMatch[1], 10);
                }

                duration = (hours * 60) + minutes;
            } else {
                // Just try to parse the number
                const numMatch = durationStr.match(/(\d+)/);
                if (numMatch && numMatch[1]) {
                    duration = parseInt(numMatch[1], 10);
                }
            }
        }

        const endDate = addMinutes(startDate, duration);

        // Create a more detailed title for the event
        const title = `${appointment.customerName}`;

        // Create the event object
        return {
            id: appointment.id,
            title: title,
            start: startDate,
            end: endDate,
            backgroundColor: getEventColor(appointment.status || 'scheduled'),
            borderColor: getEventColor(appointment.status || 'scheduled'),
            classNames: [`status-${(appointment.status || 'scheduled').toLowerCase()}`],
            extendedProps: {
                appointment,
                serviceName: appointment.service || 'Service',
                price: appointment.price || 0,
                status: appointment.status || 'scheduled',
                customerPhone: appointment.customerPhone || 'No phone'
            }
        };
    });

    // Get color based on appointment status
    function getEventColor(status: string) {
        switch (status.toLowerCase()) {
            case 'completed':
                return '#10B981'; // Green
            case 'cancelled':
                return '#EF4444'; // Red
            case 'scheduled':
                return '#F5B800'; // Yellow
            case 'confirmed':
                return '#3B82F6'; // Blue
            case 'in_progress':
                return '#8B5CF6'; // Purple
            case 'no-show':
                return '#9CA3AF'; // Gray
            default:
                return '#6B7280'; // Default Gray
        }
    }

    // Custom event rendering
    const renderEventContent = (eventInfo: any) => {
        const { appointment } = eventInfo.event.extendedProps;
        const isTimeGridView = eventInfo.view.type.includes('timeGrid');
        const isDayGridView = eventInfo.view.type.includes('dayGrid');

        return (
            <Popover open={hoveredEventId === appointment.id}>
                <PopoverTrigger asChild>
                    <div 
                        className={cn(
                            "w-full h-full cursor-pointer",
                            appointment.status === "completed" && "hover:brightness-90",
                            appointment.status === "cancelled" && "hover:brightness-90",
                            appointment.status === "scheduled" && "hover:brightness-90"
                        )}
                        onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            onEventClick(appointment);
                        }}
                        onMouseEnter={() => setHoveredEventId(appointment.id)}
                        onMouseLeave={() => {
                            // Add small delay before closing to allow hover transition to popover
                            setTimeout(() => {
                                if (hoveredEventId === appointment.id) {
                                    setHoveredEventId(null);
                                }
                            }, 100);
                        }}
                    >
                        <div className="fc-event-main-content">
                            {isTimeGridView ? (
                                <>
                                    <div className="font-medium truncate">{eventInfo.event.title}</div>
                                    <div className="text-xs opacity-90 truncate">{appointment.service}</div>
                                    <div className="text-xs mt-1 flex items-center gap-1">
                                        <span>{format(eventInfo.event.start, 'h:mm a')}</span>
                                        <span>-</span>
                                        <span>{format(eventInfo.event.end, 'h:mm a')}</span>
                                    </div>
                                </>
                            ) : isDayGridView ? (
                                <>
                                    <div className="font-medium truncate text-xs">{eventInfo.event.title}</div>
                                    <div className="text-xs opacity-90 truncate">{appointment.service}</div>
                                </>
                            ) : (
                                <>
                                    <div className="font-medium">{eventInfo.event.title}</div>
                                    <div className="text-xs opacity-90">{appointment.service}</div>
                                    <div className="text-xs mt-1">
                                        {format(eventInfo.event.start, 'h:mm a')} - {format(eventInfo.event.end, 'h:mm a')}
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </PopoverTrigger>
                <PopoverContent 
                    className="w-80 p-0" 
                    sideOffset={5}
                    onMouseEnter={() => setHoveredEventId(appointment.id)}
                    onMouseLeave={() => setHoveredEventId(null)}
                >
                    <div className="flex flex-col gap-2 p-3">
                        <div>
                            <div className="font-medium text-sm">{appointment.customerName}</div>
                            <div className="text-xs text-muted-foreground">{appointment.customerPhone}</div>
                        </div>

                        <div className="border-t border-border pt-2">
                            <div className="text-sm font-medium">{appointment.service}</div>
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                                <span>{format(new Date(appointment.date), 'MMM d, yyyy')}</span>
                                <span>•</span>
                                <span>{appointment.time}</span>
                                <span>•</span>
                                <span>{appointment.duration}</span>
                            </div>
                        </div>

                        <div className="flex items-center justify-between pt-1">
                            <Badge
                                variant={
                                    appointment.status === "completed" ? "secondary" :
                                    appointment.status === "cancelled" ? "destructive" :
                                    "default"
                                }
                                className="text-xs"
                            >
                                {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                            </Badge>
                            <span className="text-xs font-medium">
                                {formatPrice(appointment.price, currentSpace)}
                            </span>
                        </div>
                    </div>
                </PopoverContent>
            </Popover>
        );
    };

    // Handle view change
    const handleViewChange = (viewInfo: any) => {
        setCurrentView(viewInfo.view.type);
    };

    if (loading) {
        return <Skeleton className="h-[600px] w-full" />;
    }

    return (
        <FullCalendar
            ref={calendarRef}
            plugins={[dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin]}
            initialView={initialView}
            headerToolbar={{
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
            }}
            selectable={!!onDateSelect}
            selectMirror={true}
            dayMaxEvents={true}
            weekends={true}
            events={events}
            slotMinTime="06:00:00"
            slotMaxTime="22:00:00"
            eventClick={(info) => {
                onEventClick(info.event.extendedProps.appointment);
            }}
            select={onDateSelect ? (info) => {
                onDateSelect(info.start, info.end);
            } : undefined}
            height="auto"
            expandRows={true}
            stickyHeaderDates={true}
            nowIndicator={true}
            eventTimeFormat={{
                hour: 'numeric',
                minute: '2-digit',
                meridiem: 'short'
            }}
            eventContent={renderEventContent}
            viewDidMount={handleViewChange}
            editable={!!onEventDrop}
            eventDrop={onEventDrop ? (info) => {
                onEventDrop(
                    info.event.id,
                    info.event.start || new Date(),
                    info.event.end || addMinutes(info.event.start || new Date(), 30)
                );
            } : undefined}
            eventResizableFromStart={false}
            eventDurationEditable={false}
            longPressDelay={0}
            eventLongPressDelay={0}
            selectLongPressDelay={0}
        />
    );
}
