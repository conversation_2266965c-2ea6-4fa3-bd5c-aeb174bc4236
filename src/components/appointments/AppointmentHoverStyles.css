/* Hover effect for calendar events */
.fc-event {
  position: relative;
}

/* Add hover trigger for popover */
.fc-event:hover [data-radix-popper-content-wrapper] {
  visibility: visible !important;
  opacity: 1 !important;
  transition: visibility 0s, opacity 0.2s ease-in-out !important;
}

[data-radix-popper-content-wrapper] {
  z-index: 999 !important;
}

/* Ensure popover remains visible */
[data-radix-popper-content-wrapper]:hover {
  visibility: visible !important;
  opacity: 1 !important;
}
