'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { CreditCard, DollarSign, Smartphone } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Space } from '@/services/types/models';

interface PaymentMethodSelectorProps {
  space: Space;
  selectedMethod: 'cash' | 'card' | 'mpesa' | null;
  onMethodSelect: (method: 'cash' | 'card' | 'mpesa') => void;
  className?: string;
  showDescriptions?: boolean;
}

export function PaymentMethodSelector({
  space,
  selectedMethod,
  onMethodSelect,
  className,
  showDescriptions = true
}: PaymentMethodSelectorProps) {
  const paymentMethods = [
    {
      id: 'cash' as const,
      name: 'Cash',
      description: 'Pay with cash upon arrival',
      icon: DollarSign,
      enabled: space.paymentModes?.cash ?? true,
    },
    {
      id: 'card' as const,
      name: 'Card',
      description: 'Credit or debit card payment',
      icon: CreditCard,
      enabled: space.paymentModes?.card ?? false,
    },
    {
      id: 'mpesa' as const,
      name: 'M-PESA',
      description: 'Mobile money payment',
      icon: Smartphone,
      enabled: space.paymentModes?.mpesa ?? false,
    },
  ];

  const enabledMethods = paymentMethods.filter(method => method.enabled);

  if (enabledMethods.length === 0) {
    return (
      <div className={cn("space-y-3", className)}>
        <Label className="text-base font-medium">Payment Method</Label>
        <Card>
          <CardContent className="p-4">
            <p className="text-sm text-muted-foreground text-center">
              No payment methods are currently available for this business.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      <Label className="text-base font-medium">Payment Method</Label>
      <div className="grid gap-3">
        {enabledMethods.map((method) => {
          const Icon = method.icon;
          const isSelected = selectedMethod === method.id;
          
          return (
            <Card
              key={method.id}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md",
                isSelected 
                  ? "border-primary bg-primary/5 shadow-sm" 
                  : "border-border hover:border-primary/50"
              )}
              onClick={() => onMethodSelect(method.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "p-2 rounded-lg",
                    isSelected 
                      ? "bg-primary text-primary-foreground" 
                      : "bg-muted"
                  )}>
                    <Icon className="h-5 w-5" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{method.name}</span>
                      {isSelected && (
                        <Badge variant="default" className="text-xs">
                          Selected
                        </Badge>
                      )}
                    </div>
                    {showDescriptions && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {method.description}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      
      {enabledMethods.length === 1 && (
        <p className="text-xs text-muted-foreground">
          Only one payment method is available for this business.
        </p>
      )}
    </div>
  );
}
