'use client'

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { 
  Switch
} from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { customerService } from "@/services/firestore"
import { Customer } from "@/services/types/models"
import { formatToE164 } from "@/utils/phoneUtils"
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { CalendarIcon } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { useFeatureAccess, RestrictedFeature } from '@/hooks/use-feature-access';

const customerFormSchema = z.object({
  displayName: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phoneNumber: z.string()
    .refine(
      (value) => {
        // Try to format to E.164 and check if valid
        const formatted = formatToE164(value);
        return formatted !== null;
      },
      {
        message: "Please enter a valid phone number (e.g., +254712345678 or 0712345678).",
      }
    ),
  notes: z.string().optional(),
  birthdate: z.string().optional(),
  enrollInLoyalty: z.boolean(),
})

type CustomerFormValues = z.infer<typeof customerFormSchema>

const defaultValues: Partial<CustomerFormValues> = {
  notes: "",
  birthdate: "",
  enrollInLoyalty: true,
}

interface CustomerFormProps {
  onSuccess?: (customer: Customer) => void
}

export function CustomerForm({ onSuccess }: CustomerFormProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [date, setDate] = useState<Date>()

  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerFormSchema),
    defaultValues,
  })

  async function onSubmit(data: CustomerFormValues) {
    try {
      setIsLoading(true)

      // Format phone number to E.164
      const formattedPhoneNumber = formatToE164(data.phoneNumber);
      if (!formattedPhoneNumber) {
        toast.error("Invalid phone number format");
        setIsLoading(false);
        return;
      }

      // Create base customer data
      const newCustomer = {
        ...data,
        phoneNumber: formattedPhoneNumber, // Use the E.164 formatted number
        role: 'customer' as const,
        visits: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Add loyalty points if enrolled AND user has access to loyalty features
      if (data.enrollInLoyalty && canManageLoyalty) {
        const loyaltyData = {
          balance: 50, // Signup bonus
          tier: 'bronze' as const,
          updatedAt: new Date().toISOString(),
          birthdate: data.birthdate || undefined,
          referralCode: Math.random().toString(36).substring(2, 10).toUpperCase(),
        }
        
        Object.assign(newCustomer, { loyaltyPoints: loyaltyData })
      }

      const result = await customerService.create(newCustomer)
      const customer = result as unknown as Customer

      // If customer was enrolled in loyalty program and created successfully, AND user has loyalty access
      if (data.enrollInLoyalty && canManageLoyalty && customer.id) {
        try {
          const { loyaltyService } = await import('@/services/loyaltyService')
          await loyaltyService.awardPointsForSignup(customer.id)
        } catch (error) {
          console.error('Error awarding signup points:', error)
          // Don't fail the whole process if loyalty points can't be awarded
        }
      }

      toast.success("Customer added successfully")
      form.reset()
      setOpen(false)

      if (onSuccess) {
        onSuccess(customer)
      }
    } catch (error) {
      console.error('Error adding customer:', error)
      toast.error("Failed to add customer")
    } finally {
      setIsLoading(false)
    }
  }

  // Feature gating: Check if the user has access to the loyalty program feature
  const { hasAccess: canManageLoyalty } = useFeatureAccess(RestrictedFeature.LOYALTY_PROGRAM)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Add New Customer</Button>
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] w-full sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Customer</DialogTitle>
          <DialogDescription>
            Enter customer details to create a new customer profile.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="displayName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter customer name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter email address" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter phone number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="birthdate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Birthdate (for birthday rewards)</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(new Date(field.value), "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => {
                          field.onChange(date ? date.toISOString() : "")
                        }}
                        disabled={(date) => date > new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {canManageLoyalty && (
              <FormField
                control={form.control}
                name="enrollInLoyalty"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Loyalty Program</FormLabel>
                      <FormMessage />
                      <p className="text-sm text-muted-foreground">
                        Enroll this customer in the loyalty program
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}
            
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes about the customer"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Adding..." : "Add Customer"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}