'use client';


import { useAppSelector } from '@/store/hooks';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, Megaphone } from 'lucide-react';

export function MarketingCard() {
  const { currentSpace } = useAppSelector((state) => state.space);

  if (!currentSpace) {
    return null;
  }

  // These functions will be used when implementing QR code functionality
  // const serviceUrl = `${window.location.origin}/services/${currentSpace.id}`;

  // const downloadQRCode = () => {
  //   const canvas = document.getElementById('qr-canvas') as HTMLCanvasElement;
  //   if (!canvas) return;

  //   const image = canvas.toDataURL('image/png');
  //   const link = document.createElement('a');
  //   link.href = image;
  //   link.download = `${currentSalon.name.replace(/\s+/g, '-').toLowerCase()}-qrcode.png`;
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);

  //   toast.success('QR Code downloaded successfully');
  // };

  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Marketing</CardTitle>
        <Megaphone className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">89%</div>
        <p className="text-xs text-muted-foreground">
          of customers found you through Google
        </p>
        <div className="mt-4">
          <Button
            variant="outline"
            size="sm"
            className="w-full text-xs"
          >
            View marketing insights
            <ArrowRight className="ml-2 h-3 w-3" />
          </Button>
        </div>
      </CardContent>
      {/* Decorative gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#F5B800] to-[#FFD700]" />
    </Card>
  );
}
