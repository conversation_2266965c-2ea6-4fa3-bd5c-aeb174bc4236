import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowRight, Lock } from 'lucide-react';
import Link from 'next/link';

interface UpgradePromptProps {
  /**
   * The feature name that requires an upgrade
   */
  featureName: string;
  
  /**
   * The plan name required (e.g., "Premium", "Basic")
   */
  requiredPlan: string;
  
  /**
   * Description of benefits when upgrading
   */
  description?: string;
  
  /**
   * Additional benefits to display as bullet points
   */
  benefits?: string[];
  
  /**
   * Custom CTAs to show instead of the default upgrade button
   */
  actions?: React.ReactNode;
  
  /**
   * Whether to show a compact version of the prompt
   */
  compact?: boolean;
}

/**
 * A component for showing upgrade prompts inline within features
 */
export function UpgradePrompt({
  featureName,
  requiredPlan,
  description,
  benefits = [],
  actions,
  compact = false,
}: UpgradePromptProps) {
  if (compact) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded-md bg-amber-50 border-amber-200">
        <Lock className="h-4 w-4 text-amber-500 flex-shrink-0" />
        <p className="text-sm flex-grow">
          <span className="font-medium">{featureName}</span> requires {requiredPlan} plan
        </p>
        <Button asChild size="sm" variant="outline">
          <Link href="/dashboard/billing">
            Upgrade
          </Link>
        </Button>
      </div>
    );
  }
  
  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Lock className="h-5 w-5 text-amber-500" />
          <CardTitle>{featureName} - {requiredPlan} Feature</CardTitle>
        </div>
        <CardDescription>
          {description || `Upgrade to the ${requiredPlan} plan to access ${featureName} and other premium features.`}
        </CardDescription>
      </CardHeader>
      
      {benefits.length > 0 && (
        <CardContent>
          <ul className="space-y-2">
            {benefits.map((benefit, index) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <div className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5 flex-shrink-0">
                  <span className="text-green-600 text-xs">✓</span>
                </div>
                <span>{benefit}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      )}
      
      <CardFooter>
        {actions || (
          <Button asChild className="w-full">
            <Link href="/dashboard/billing">
              Upgrade to {requiredPlan} <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
