'use client';

import React from 'react';

interface GroomBookBrandingProps {
  className?: string;
}

/**
 * GroomBook branding footer component
 * Shows "Powered by GroomBook" with a text-based watermark for free tier users
 */
export function GroomBookBranding({ className = '' }: GroomBookBrandingProps) {
  return (
    <div className={`flex items-center justify-center py-3 px-6 border-t bg-gray-50/50 ${className}`}>
      <div className="flex items-center space-x-2 text-sm text-gray-400">
        <span>Powered by</span>
        <span className="font-bold text-gray-500">GroomBook</span>
      </div>
    </div>
  );
}
