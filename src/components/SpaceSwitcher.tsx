'use client';

import React, { useEffect, useState } from 'react';
import { Check, ChevronsUpDown, PlusCircle, Store } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setCurrentSpace, fetchSpaces } from '@/store/slices/spaceSlice';
import { CreateSpaceDialog } from '@/components/CreateSpaceDialog';

export function SpaceSwitcher() {
  const [open, setOpen] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const dispatch = useAppDispatch();
  const { currentSpace, spaces } = useAppSelector((state) => state.space);
  const { user } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (user) {
      dispatch(fetchSpaces(user.uid));
    }
  }, [dispatch, user]);

  if (!currentSpace) {
    return (
      <div className="flex items-center justify-between px-4 py-2">
        <div className="flex items-center space-x-2">
          <Store className="h-5 w-5" />
          <span className="text-sm font-medium">Loading spaces...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-[95vw] w-full sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create space</DialogTitle>
            <DialogDescription>
              Add a new space to manage your business.
            </DialogDescription>
          </DialogHeader>
          <CreateSpaceDialog onClose={() => setDialogOpen(false)} />
        </DialogContent>
      </Dialog>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            aria-label="Select a space"
            className="w-full justify-between"
          >
            <div className="flex items-center space-x-2">
              <Store className="h-5 w-5" />
              <span className="truncate">{currentSpace.name}</span>
            </div>
            <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-0">
          <Command>
            <CommandList>
              <CommandInput placeholder="Search space..." />
              <CommandEmpty>No space found.</CommandEmpty>
              <CommandGroup heading="Spaces">
                {spaces.map((space) => (
                  <CommandItem
                    key={space.id}
                    onSelect={() => {
                      dispatch(setCurrentSpace(space));
                      setOpen(false);
                    }}
                    className="text-sm"
                  >
                    <Store className="mr-2 h-4 w-4" />
                    <span className="truncate">{space.name}</span>
                    {currentSpace.id === space.id ? (
                      <Check className="ml-auto h-4 w-4" />
                    ) : null}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
            <CommandSeparator />
            <CommandList>
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    setOpen(false);
                    setDialogOpen(true);
                  }}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Create Space
                </CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
