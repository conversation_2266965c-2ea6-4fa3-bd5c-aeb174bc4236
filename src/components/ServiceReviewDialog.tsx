'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Star, StarHalf } from 'lucide-react';
import { reviewService } from '@/services/firestore';
import { type Review } from '@/services/types/models';

interface ServiceReviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  activeServiceId: string;
  staffId: string;
  staffName: string;
  customerId: string;
  customerName: string;
  spaceId: string;
  serviceIds: string[];
  serviceDate: string;
  onSuccess?: () => void;
}

export function ServiceReviewDialog({
  open,
  onOpenChange,
  activeServiceId,
  staffId,
  staffName,
  customerId,
  customerName,
  spaceId,
  serviceIds,
  serviceDate,
  onSuccess
}: ServiceReviewDialogProps) {
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Validate input
      if (rating === 0) {
        toast.error('Please select a rating');
        return;
      }

      // Submit review
      await reviewService.create({
        activeServiceId,
        staffId,
        customerId,
        spaceId,
        rating,
        comment,
        serviceDate,
        customerName,
        serviceIds,
        status: 'published',
        createdAt: new Date().toISOString()
      });

      toast.success('Review submitted successfully');
      onSuccess?.();
      onOpenChange(false);

      // Reset form
      setRating(0);
      setComment('');
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95vw] w-full sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl sm:text-2xl">Review Your Experience with {staffName}</DialogTitle>
          <DialogDescription className="text-sm sm:text-base">
            Share your feedback about the service you received
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Rating Stars */}
          <div className="flex flex-col items-center gap-2">
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  className="text-2xl focus:outline-none"
                  onClick={() => setRating(value)}
                  onMouseEnter={() => setHoverRating(value)}
                  onMouseLeave={() => setHoverRating(0)}
                >
                  <Star
                    className={`h-6 w-6 sm:h-8 sm:w-8 ${
                      value <= (hoverRating || rating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'fill-none text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
            <span className="text-sm text-muted-foreground">
              {rating === 0 ? 'Select a rating' : `${rating} out of 5 stars`}
            </span>
          </div>

          {/* Comment */}
          <div className="space-y-2">
            <Textarea
              placeholder="Share details about your experience..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0 justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)} className="w-full sm:w-auto">
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting} className="w-full sm:w-auto">
            {isSubmitting ? 'Submitting...' : 'Submit Review'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
