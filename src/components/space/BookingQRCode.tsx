'use client';

import { useState, useRef } from 'react';
import QRCode from 'react-qr-code';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Share2, 
  Download, 
  Copy, 
  QrCode, 
  Printer, 
  Facebook, 
  Twitter,  
  Smartphone 
} from 'lucide-react';
import { toast } from 'sonner';

interface BookingQRCodeProps {
  spaceId: string;
  spaceName: string;
  baseUrl?: string;
  salonId?: string;  // For backward compatibility
  salonName?: string; // For backward compatibility
}

export function BookingQRCode({ 
  spaceId, 
  spaceName, 
  baseUrl = typeof window !== 'undefined' ? window.location.origin : '',
  salonId, // For backward compatibility
  salonName // For backward compatibility
}: BookingQRCodeProps) {
  // Use the provided spaceId and spaceName, falling back to salonId and salonName for compatibility
  const actualSpaceId = spaceId || salonId || '';
  const actualSpaceName = spaceName || salonName || '';
  const [activeTab, setActiveTab] = useState('qrcode');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const qrCodeRef = useRef<HTMLDivElement>(null);
  
  // Generate the booking URL
  const bookingUrl = `${baseUrl}/book/${actualSpaceId}`;
  
  // Function to copy the booking URL to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(bookingUrl);
    toast.success('Booking link copied to clipboard!');
  };
  
  // Function to download QR code as PNG
  const downloadQRCode = () => {
    if (!qrCodeRef.current) return;
    
    const svg = qrCodeRef.current.querySelector('svg');
    if (!svg) return;
    
    // Create a canvas element
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions (with some padding)
    canvas.width = 1024;
    canvas.height = 1024;
    
    // Fill background
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Convert SVG to data URL
    const svgData = new XMLSerializer().serializeToString(svg);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const DOMURL = window.URL || window.webkitURL || window;
    const img = new Image();
    const svgUrl = DOMURL.createObjectURL(svgBlob);
    
    img.onload = () => {
      // Draw the image centered with padding
      const padding = 100;
      ctx.drawImage(img, padding, padding, canvas.width - (padding * 2), canvas.height - (padding * 2));
      
      // Add salon name at the bottom
      ctx.font = 'bold 48px Arial';
      ctx.textAlign = 'center';
      ctx.fillStyle = '#000000';
      ctx.fillText(actualSpaceName, canvas.width / 2, canvas.height - 40);
      
      // Add "Scan to book" text at the top
      ctx.font = 'bold 36px Arial';
      ctx.fillText('Scan to book your appointment', canvas.width / 2, 60);
      
      // Create download link
      const dataUrl = canvas.toDataURL('image/png');
      const downloadLink = document.createElement('a');
      downloadLink.href = dataUrl;
      downloadLink.download = `${actualSpaceName.replace(/\s+/g, '-').toLowerCase()}-booking-qr.png`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      
      // Clean up
      DOMURL.revokeObjectURL(svgUrl);
    };
    
    img.src = svgUrl;
  };
  
  // Function to print QR code
  const printQRCode = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      toast.error('Pop-up blocked. Please allow pop-ups and try again.');
      return;
    }
    
    printWindow.document.write(`
      <html>
        <head>
          <title>${actualSpaceName} - Booking QR Code</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 20px;
            }
            .qr-container {
              margin: 30px auto;
              max-width: 500px;
            }
            h1 {
              font-size: 24px;
              margin-bottom: 10px;
            }
            p {
              font-size: 16px;
              color: #666;
              margin-bottom: 30px;
            }
            .url {
              margin-top: 20px;
              font-size: 14px;
              color: #666;
              word-break: break-all;
            }
            @media print {
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <h1>${actualSpaceName}</h1>
            <p>Scan this QR code to book your appointment</p>
            <div>
              ${qrCodeRef.current?.innerHTML || ''}
            </div>
            <div class="url">
              ${bookingUrl}
            </div>
          </div>
          <div class="no-print" style="margin-top: 40px;">
            <button onclick="window.print();" style="padding: 10px 20px; background: #000; color: #fff; border: none; cursor: pointer; font-size: 16px; border-radius: 4px;">
              Print QR Code
            </button>
          </div>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
    }, 500);
  };
  
  // Function to share via native share API (mobile)
  const shareNative = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Book an appointment at ${actualSpaceName}`,
          text: `Book your appointment at ${actualSpaceName} using this link:`,
          url: bookingUrl,
        });
        toast.success('Shared successfully!');
      } catch (error) {
        console.error('Error sharing:', error);
        toast.error('Failed to share. Please try another method.');
      }
    } else {
      toast.error('Native sharing not supported on this device. Please use another method.');
    }
  };
  
  return (
    <>
      <Button 
        variant="outline" 
        className="flex items-center gap-2"
        onClick={() => setIsDialogOpen(true)}
      >
        <QrCode className="h-4 w-4" />
        <span>Booking Link & QR Code</span>
      </Button>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-[95vw] w-full sm:max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Share Booking Link</DialogTitle>
            <DialogDescription>
              Let customers book appointments directly using this link or QR code
            </DialogDescription>
          </DialogHeader>
          
          <Tabs defaultValue="qrcode" className="w-full" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full flex overflow-x-auto items-center justify-start">
              <TabsTrigger value="qrcode" className="whitespace-nowrap">QR Code</TabsTrigger>
              <TabsTrigger value="link" className="whitespace-nowrap">Direct Link</TabsTrigger>
            </TabsList>
            
            <TabsContent value="qrcode" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-center">Scan to Book</CardTitle>
                  <CardDescription className="text-center">
                    Display this QR code in your space for easy booking
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center">
                  <div 
                    ref={qrCodeRef} 
                    className="bg-white p-4 rounded-lg"
                    style={{ maxWidth: '100%' }}
                  >
                    <QRCode
                      value={bookingUrl}
                      size={200}
                      style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                      viewBox={`0 0 256 256`}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex flex-wrap justify-center gap-2">
                  <Button variant="outline" size="sm" onClick={downloadQRCode}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  <Button variant="outline" size="sm" onClick={printQRCode}>
                    <Printer className="h-4 w-4 mr-2" />
                    Print
                  </Button>
                  {typeof navigator !== 'undefined' && 'share' in navigator && (
                    <Button variant="outline" size="sm" onClick={shareNative}>
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="link" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Direct Booking Link</CardTitle>
                  <CardDescription>
                    Share this link with your customers via email, SMS, or social media
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex w-full max-w-sm items-center space-x-2">
                    <Input
                      value={bookingUrl}
                      readOnly
                      className="flex-1"
                    />
                    <Button type="button" size="sm" onClick={copyToClipboard}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-wrap justify-center gap-2">
                  <Button variant="outline" size="sm" onClick={copyToClipboard}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(bookingUrl)}`, '_blank')}>
                    <Facebook className="h-4 w-4 mr-2" />
                    Facebook
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(bookingUrl)}&text=${encodeURIComponent(`Book your appointment at ${actualSpaceName}`)}`, '_blank')}>
                    <Twitter className="h-4 w-4 mr-2" />
                    Twitter
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.open(`https://wa.me/?text=${encodeURIComponent(`Book your appointment at ${actualSpaceName}: ${bookingUrl}`)}`, '_blank')}>
                    <Smartphone className="h-4 w-4 mr-2" />
                    WhatsApp
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
}
