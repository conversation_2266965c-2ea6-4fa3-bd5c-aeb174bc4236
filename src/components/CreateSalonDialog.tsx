'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { doc, setDoc } from 'firebase/firestore';
import { getDownloadURL, ref, uploadBytes } from 'firebase/storage';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ImagePlus } from 'lucide-react';
import { toast } from 'sonner';
import { db } from '@/utils/firebase';
import { storage } from '@/utils/firebase';
import { fetchSpaces } from '@/store/slices/spaceSlice';
import { ScrollArea } from './ui/scroll-area';

const salonSchema = z.object({
  name: z.string().min(2, 'Shop name must be at least 2 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
});

type FormData = z.infer<typeof salonSchema>;

interface CreateSpaceDialogProps {
  onClose: () => void;
}

export function CreateSpaceDialog({ onClose }: CreateSpaceDialogProps) {
  const dispatch = useAppDispatch();
  const user = useAppSelector((state) => state.auth.user);
  const [isLoading, setIsLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(salonSchema),
    defaultValues: {
      name: '',
      description: '',
      address: '',
      phone: '',
    },
  });

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: FormData) => {
    if (!user) {
      toast.error("You must be logged in to create a shop");
      return;
    }

    try {
      setIsLoading(true);

      // Upload image if selected
      let imageUrl = '';
      if (imageFile) {
        const imageRef = ref(storage, `salons/${user.uid}/${imageFile.name}`);
        await uploadBytes(imageRef, imageFile);
        imageUrl = await getDownloadURL(imageRef);
      }

      // Create salon document
      const salonId = crypto.randomUUID();
      const salonData = {
        id: salonId,
        ...data,
        imageUrl,
        ownerId: user.uid,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await setDoc(doc(db, 'salons', salonId), salonData);

      // Refresh salons list
      dispatch(fetchSpaces(user.uid));

      toast.success("Your shop has been created");

      // Reset form
      form.reset();
      setImageFile(null);
      setImagePreview(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create salon";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <ScrollArea className="max-h-[calc(100vh-8rem)] sm:max-h-[70vh]">
        <div className="p-4 sm:p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Logo Upload */}
              <div className="space-y-2">
              <FormLabel>Shop Logo</FormLabel>
              <div
                className="border border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50"
                onClick={() => document.getElementById('logo-upload')?.click()}
              >
                {imagePreview ? (
                  <div className="relative w-32 h-32 mx-auto">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-full object-cover rounded-lg"
                    />
                  </div>
                ) : (
                  <div className="py-4">
                    <ImagePlus className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                    <p className="text-sm text-gray-500">Click to upload logo</p>
                  </div>
                )}
                <input
                  id="logo-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Shop Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter shop name" className="border-gray-300" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe your shop..."
                      className="resize-none border-gray-300"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter shop address" className="border-gray-300" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter phone number" className="border-gray-300" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-2 pb-2 px-6 border-t mt-4">
              <Button
                type="button"
                variant="outline"
                disabled={isLoading}
                className="bg-white"
                onClick={() => {
                  form.reset();
                  setImageFile(null);
                  setImagePreview(null);
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#F5B800] hover:bg-[#E5AB00] text-black"
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Create Shop'}
              </Button>
            </div>
          </form>
        </Form>
        </div>
        </ScrollArea>
        </>
  );
}
