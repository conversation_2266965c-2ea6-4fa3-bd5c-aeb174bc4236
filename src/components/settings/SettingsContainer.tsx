'use client';

import { useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { Store, CreditCard, Clock, Bell, PaintBucket, UserCog, Globe } from 'lucide-react';
import { useAppSelector } from '@/lib/store/hooks';
import { RestrictedFeature, useFeatureAccess } from '@/hooks/use-feature-access';
import { OnboardingContainer } from '@/components/onboarding/OnboardingContainer';
import { ROUTES } from '@/lib/routes';

interface SettingsTabConfig {
  value: string;
  label: string;
  icon: React.ElementType;
  premium?: boolean;
  requiresSpace?: boolean;
}

interface SettingsContainerProps {
  defaultTab?: string;
  children?: ReactNode;
}

export function SettingsContainer({
  defaultTab = 'general',
  children
}: SettingsContainerProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(defaultTab);
  const currentSpace = useAppSelector((state) => state.space.currentSpace);
  const { hasAccess: hasBrandingAccess } = useFeatureAccess(RestrictedFeature.CUSTOM_BRANDING);
  const needsOnboarding = useAppSelector((state) => !state.user.hasCompletedOnboarding);

  if (needsOnboarding) {
    return <OnboardingContainer />;
  }

  const tabs: SettingsTabConfig[] = [
    { value: 'general', label: 'General', icon: Store, requiresSpace: false },
    { value: 'payments', label: 'Payments', icon: CreditCard, requiresSpace: true },
    { value: 'business', label: 'Business Hours', icon: Clock, requiresSpace: true },
    { value: 'notifications', label: 'Notifications', icon: Bell, requiresSpace: true },
    { value: 'branding', label: 'Branding', icon: PaintBucket, premium: true, requiresSpace: true },
    { value: 'staff', label: 'Staff', icon: UserCog, requiresSpace: true },
    { value: 'website', label: 'Website', icon: Globe, requiresSpace: true },
  ];

  const availableTabs = tabs.filter(tab => {
    if (tab.requiresSpace && !currentSpace) return false;
    if (tab.premium && tab.value === 'branding') {
      return hasBrandingAccess;
    }
    return true;
  });

  useEffect(() => {
    const currentTab = tabs.find(tab => tab.value === activeTab);
    if (currentTab?.requiresSpace && !currentSpace) {
      setActiveTab('general');
      router.push(ROUTES.SETTINGS.ROOT);
    }
  }, [activeTab, currentSpace, router, tabs]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    router.push(ROUTES.SETTINGS.withTab(value));
  };

  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col space-y-6">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="flex w-full overflow-x-auto items-center justify-start gap-4">
            {availableTabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className="flex items-center gap-2 whitespace-nowrap"
                  disabled={tab.requiresSpace && !currentSpace}
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {availableTabs.map((tab) => (
            <TabsContent key={tab.value} value={tab.value}>
              <Card className="p-6">
                {children}
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
