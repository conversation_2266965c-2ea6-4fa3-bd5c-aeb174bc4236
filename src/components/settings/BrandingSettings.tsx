'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Upload, Palette } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';

const CUSTOM_BRANDING_BENEFITS = [
  "Remove GroomBook branding from customer communications",
  "Add your logo to all customer emails and notifications",
  "Customize colors to match your brand identity",
  "Create a branded customer booking experience",
  "Custom domain for your booking portal"
];

export function BrandingSettings() {
  const { toast } = useToast();
  const [brandSettings, setBrandSettings] = useState({
    primaryColor: '#6366f1',
    accentColor: '#4f46e5',
    textColor: '#1f2937',
    logoUrl: '',
    removeGroomBookBranding: true,
    customDomain: '',
    customEmailHeader: ''
  });

  return (
    <FeatureGate feature={RestrictedFeature.CUSTOM_BRANDING}>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Custom Branding</CardTitle>
            <CardDescription>
              Customize your brand appearance across your booking portal and customer communications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label>Logo</Label>
                <div className="mt-2 flex items-center space-x-4">
                  <div className="h-24 w-24 rounded-lg border flex items-center justify-center">
                    {brandSettings.logoUrl ? (
                      <img 
                        src={brandSettings.logoUrl} 
                        alt="Brand logo" 
                        className="max-h-full max-w-full object-contain"
                      />
                    ) : (
                      <Upload className="h-8 w-8 text-muted-foreground" />
                    )}
                  </div>
                  <Button variant="outline">Upload Logo</Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Primary Color</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <div 
                      className="h-8 w-8 rounded-md border"
                      style={{ backgroundColor: brandSettings.primaryColor }}
                    />
                    <Input 
                      type="text" 
                      value={brandSettings.primaryColor}
                      onChange={(e) => setBrandSettings(prev => ({
                        ...prev,
                        primaryColor: e.target.value
                      }))}
                    />
                  </div>
                </div>

                <div>
                  <Label>Accent Color</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <div 
                      className="h-8 w-8 rounded-md border"
                      style={{ backgroundColor: brandSettings.accentColor }}
                    />
                    <Input 
                      type="text"
                      value={brandSettings.accentColor}
                      onChange={(e) => setBrandSettings(prev => ({
                        ...prev,
                        accentColor: e.target.value
                      }))}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Remove GroomBook Branding</Label>
                  <Switch 
                    checked={brandSettings.removeGroomBookBranding}
                    onCheckedChange={(checked) => setBrandSettings(prev => ({
                      ...prev,
                      removeGroomBookBranding: checked
                    }))}
                  />
                </div>

                <div>
                  <Label>Custom Domain</Label>
                  <Input 
                    className="mt-2"
                    placeholder="bookings.yourbusiness.com"
                    value={brandSettings.customDomain}
                    onChange={(e) => setBrandSettings(prev => ({
                      ...prev,
                      customDomain: e.target.value
                    }))}
                  />
                </div>
              </div>
            </div>

            <Button 
              onClick={() => toast({
                title: 'Branding settings saved',
                description: 'Your changes have been applied successfully.'
              })}
            >
              Save Changes
            </Button>
          </CardContent>
        </Card>
      </div>
    </FeatureGate>
  );
}
