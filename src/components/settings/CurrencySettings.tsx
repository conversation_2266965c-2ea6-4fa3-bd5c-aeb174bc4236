'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { currencyService, ConversionRecord } from '@/services/currencyService';
import { updateSpace } from '@/store/slices/spaceSlice';
import { toast } from 'sonner';
import { ArrowRight, Clock, DollarSign, AlertTriangle, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';

const SUPPORTED_CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R' },
];

export function CurrencySettings() {
  const dispatch = useAppDispatch();
  const { currentSpace } = useAppSelector((state) => state.space);
  const [selectedCurrency, setSelectedCurrency] = useState(currentSpace?.settings?.currency || 'USD');
  const [isConverting, setIsConverting] = useState(false);
  const [conversionHistory, setConversionHistory] = useState<ConversionRecord[]>([]);
  const [canConvert, setCanConvert] = useState(true);
  const [lastConversion, setLastConversion] = useState<ConversionRecord | null>(null);
  const [previewConversion, setPreviewConversion] = useState<{
    sampleService: number;
    sampleRevenue: number;
  } | null>(null);

  useEffect(() => {
    if (currentSpace) {
      loadConversionData();
    }
  }, [currentSpace]);

  useEffect(() => {
    // Update preview when currency selection changes
    if (currentSpace && selectedCurrency !== currentSpace.settings?.currency) {
      const currentCurrency = currentSpace.settings?.currency || 'USD';
      const sampleServicePrice = 50; // Sample $50 service
      const sampleRevenueAmount = 1000; // Sample $1000 revenue

      setPreviewConversion({
        sampleService: currencyService.convertAmount(sampleServicePrice, currentCurrency, selectedCurrency),
        sampleRevenue: currencyService.convertAmount(sampleRevenueAmount, currentCurrency, selectedCurrency)
      });
    } else {
      setPreviewConversion(null);
    }
  }, [selectedCurrency, currentSpace]);

  const loadConversionData = async () => {
    if (!currentSpace) return;

    try {
      const [history, conversionCheck] = await Promise.all([
        currencyService.getConversionHistory(currentSpace.id),
        currencyService.canConvertCurrency(currentSpace.id)
      ]);

      setConversionHistory(history);
      setCanConvert(conversionCheck.canConvert);
      setLastConversion(conversionCheck.lastConversion || null);
    } catch (error) {
      console.error('Error loading conversion data:', error);
    }
  };

  const handleCurrencyConversion = async () => {
    if (!currentSpace || !canConvert) return;

    const currentCurrency = currentSpace.settings?.currency || 'USD';
    if (currentCurrency === selectedCurrency) {
      toast.error('Selected currency is the same as current currency');
      return;
    }

    setIsConverting(true);

    try {
      // Show confirmation dialog
      const confirmed = window.confirm(
        `This will convert all prices, revenue targets, and recent invoices from ${currentCurrency} to ${selectedCurrency}. This action cannot be undone. Continue?`
      );

      if (!confirmed) {
        setIsConverting(false);
        return;
      }

      // Perform currency conversion
      const conversionRecord = await currencyService.convertSpaceCurrency(
        currentSpace.id,
        currentCurrency,
        selectedCurrency
      );

      // Update space settings
      const updatedSpace = {
        ...currentSpace,
        settings: {
          ...currentSpace.settings,
          currency: selectedCurrency
        }
      };

      await dispatch(updateSpace({ id: currentSpace.id, data: updatedSpace })).unwrap();

      toast.success(
        `Currency converted successfully! ${conversionRecord.itemsConverted.services} services, ${conversionRecord.itemsConverted.revenueTargets} revenue targets, and ${conversionRecord.itemsConverted.invoices} invoices updated.`
      );

      // Reload conversion data
      await loadConversionData();

    } catch (error) {
      console.error('Error converting currency:', error);
      toast.error('Failed to convert currency. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const getCurrentCurrency = () => {
    return SUPPORTED_CURRENCIES.find(c => c.code === (currentSpace?.settings?.currency || 'USD'));
  };

  const getSelectedCurrency = () => {
    return SUPPORTED_CURRENCIES.find(c => c.code === selectedCurrency);
  };

  const exchangeRate = currentSpace ? 
    currencyService.getExchangeRate(currentSpace.settings?.currency || 'USD', selectedCurrency) : 1;

  return (
    <div className="space-y-6">
      {/* Current Currency Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Currency Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Current Currency</Label>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-sm">
                  {getCurrentCurrency()?.code} - {getCurrentCurrency()?.name}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  ({getCurrentCurrency()?.symbol})
                </span>
              </div>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <div>
              <Label htmlFor="currency-select">Change Currency</Label>
              <Select value={selectedCurrency} onValueChange={setSelectedCurrency}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {SUPPORTED_CURRENCIES.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code}>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{currency.code}</span>
                        <span className="text-muted-foreground">-</span>
                        <span>{currency.name}</span>
                        <span className="text-muted-foreground">({currency.symbol})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Conversion Preview */}
            {previewConversion && (
              <Alert>
                <ArrowRight className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <div className="font-medium">Conversion Preview:</div>
                    <div className="text-sm space-y-1">
                      <div>
                        Service ($50) → {currencyService.formatCurrency(previewConversion.sampleService, selectedCurrency)}
                      </div>
                      <div>
                        Revenue ($1,000) → {currencyService.formatCurrency(previewConversion.sampleRevenue, selectedCurrency)}
                      </div>
                      <div className="text-muted-foreground">
                        Exchange Rate: 1 {getCurrentCurrency()?.code} = {exchangeRate} {selectedCurrency}
                      </div>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Conversion Restrictions */}
            {!canConvert && lastConversion && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Currency was last converted on {format(new Date(lastConversion.convertedAt), 'MMM d, yyyy')}. 
                  You can only convert currency once every 24 hours to prevent data inconsistencies.
                </AlertDescription>
              </Alert>
            )}

            <Button
              onClick={handleCurrencyConversion}
              disabled={
                isConverting || 
                !canConvert || 
                selectedCurrency === (currentSpace?.settings?.currency || 'USD')
              }
              className="w-full"
            >
              {isConverting ? 'Converting...' : `Convert to ${getSelectedCurrency()?.name}`}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Conversion History */}
      {conversionHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Conversion History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {conversionHistory
                .sort((a, b) => new Date(b.convertedAt).getTime() - new Date(a.convertedAt).getTime())
                .slice(0, 5)
                .map((record) => (
                  <div key={record.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{record.fromCurrency}</span>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{record.toCurrency}</span>
                        <Badge variant="outline" className="text-xs">
                          Rate: {record.exchangeRate}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {format(new Date(record.convertedAt), 'MMM d, yyyy h:mm a')}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>
                          {record.itemsConverted.services + record.itemsConverted.revenueTargets + record.itemsConverted.invoices} items
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
