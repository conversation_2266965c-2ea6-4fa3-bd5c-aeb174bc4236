import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { revenueTargetService } from "@/services/revenueTargetService"

// Form schema with correct types
const revenueTargetSchema = z.object({
  type: z.enum(['daily', 'monthly', 'annual'], {
    required_error: "Please select a target type",
  }),
  amount: z.number({
    required_error: "Amount is required",
  }).positive("Amount must be greater than 0"),
  startDate: z.string({
    required_error: "Start date is required",
  }).refine(val => {
    const date = new Date(val);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);
    return date >= today;
  }, "Start date cannot be in the past"),
  isRecurring: z.boolean(),
});

type RevenueTargetFormValues = z.infer<typeof revenueTargetSchema>

interface RevenueTargetSettingsProps {
  spaceId: string;
}

export function RevenueTargetSettings({ spaceId }: RevenueTargetSettingsProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<RevenueTargetFormValues>({
    resolver: zodResolver(revenueTargetSchema),
    defaultValues: {
      type: 'daily',
      amount: 0,
      startDate: new Date().toISOString().split('T')[0],
      isRecurring: true,
    },
  });

  const onSubmit = async (data: RevenueTargetFormValues) => {
    try {
      setIsLoading(true);

      await revenueTargetService.create({
        spaceId,
        type: data.type,
        amount: data.amount,
        startDate: data.startDate,
        isRecurring: data.isRecurring,
      });

      toast("Revenue target set");

      form.reset({
        type: 'daily',
        amount: 0,
        startDate: new Date().toISOString().split('T')[0],
        isRecurring: true,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : "Could not set revenue target. Please try again.";
      toast("Error");
    } finally {
      setIsLoading(false);
    }
  };

  const getTargetTypeLabel = (type: string) => {
    switch (type) {
      case 'daily':
        return 'Set a target for your daily revenue';
      case 'monthly':
        return 'Set a target for your monthly revenue';
      case 'annual':
        return 'Set a target for your annual revenue';
      default:
        return 'Select target type';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Set New Target</CardTitle>
        <CardDescription>
          Set your revenue targets to track your business performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target Type</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value}
                      onValueChange={(value: 'daily' | 'monthly' | 'annual') => {
                        field.onChange(value);
                        form.clearErrors('type');
                      }}
                      disabled={isLoading}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select target type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily Target</SelectItem>
                        <SelectItem value="monthly">Monthly Target</SelectItem>
                        <SelectItem value="annual">Annual Target</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    {getTargetTypeLabel(field.value)}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target Amount (KES)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      placeholder="Enter target amount"
                      {...field}
                      onChange={(e) => {
                        // Convert string to number for the form
                        field.onChange(Number(e.target.value));
                        form.clearErrors('amount');
                      }}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Set your revenue target in Kenyan Shillings
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      min={new Date().toISOString().split('T')[0]}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        form.clearErrors('startDate');
                      }}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    When should this target start?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isRecurring"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between space-y-0 rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Recurring Target
                    </FormLabel>
                    <FormDescription>
                      Automatically apply this target to future periods
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? "Setting target..." : "Set Revenue Target"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
