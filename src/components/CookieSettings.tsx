'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { <PERSON><PERSON> } from 'lucide-react';

interface CookieSettingsProps {
  trigger?: React.ReactNode;
}

export function CookieSettings({ trigger }: CookieSettingsProps) {
  const [open, setOpen] = useState(false);
  const [necessary, setNecessary] = useState(true);
  const [functional, setFunctional] = useState(true);
  const [analytics, setAnalytics] = useState(true);
  const [marketing, setMarketing] = useState(false);

  const handleSave = () => {
    // In a real implementation, you would save these preferences to cookies/localStorage
    // and update your tracking scripts accordingly
    
    const preferences = {
      necessary,
      functional,
      analytics,
      marketing
    };
    
    console.log('Cookie preferences saved:', preferences);
    
    // Show success message
    toast.success('Cookie preferences saved successfully');
    
    // Close the dialog
    setOpen(false);
  };

  const handleAcceptAll = () => {
    setNecessary(true);
    setFunctional(true);
    setAnalytics(true);
    setMarketing(true);
    
    // Save immediately when "Accept All" is clicked
    setTimeout(handleSave, 100);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || <Button variant="link">Cookie Settings</Button>}
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] w-full sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Cookie className="h-5 w-5 mr-2 text-mustard" />
            Cookie Settings
          </DialogTitle>
          <DialogDescription>
            Manage your cookie preferences. Necessary cookies help make a website usable by enabling basic functions.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Necessary Cookies</h4>
              <p className="text-sm text-muted-foreground">
                These cookies are essential for the website to function properly.
              </p>
            </div>
            <Switch 
              checked={necessary} 
              onCheckedChange={setNecessary}
              disabled={true} // Necessary cookies cannot be disabled
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Functional Cookies</h4>
              <p className="text-sm text-muted-foreground">
                These cookies enable personalized features and functionality.
              </p>
            </div>
            <Switch 
              checked={functional} 
              onCheckedChange={setFunctional}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Analytics Cookies</h4>
              <p className="text-sm text-muted-foreground">
                These cookies help us understand how visitors interact with our website.
              </p>
            </div>
            <Switch 
              checked={analytics} 
              onCheckedChange={setAnalytics}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Marketing Cookies</h4>
              <p className="text-sm text-muted-foreground">
                These cookies are used to track visitors across websites to display relevant advertisements.
              </p>
            </div>
            <Switch 
              checked={marketing} 
              onCheckedChange={setMarketing}
            />
          </div>
        </div>
        
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button 
            variant="outline" 
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={handleAcceptAll}
            >
              Accept All
            </Button>
            <Button 
              onClick={handleSave}
              className="bg-mustard text-black hover:bg-mustard-dark"
            >
              Save Preferences
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
