'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/utils/auth';
import { getUsageForDisplay } from '@/services/usageService';
import { Users, Calendar, Building, Crown } from 'lucide-react';
import Link from 'next/link';

interface UsageData {
  appointments: {
    current: number;
    limit: number;
    unlimited: boolean;
    percentage: number;
  };
  customers: {
    current: number;
    limit: number;
    unlimited: boolean;
    percentage: number;
  };
  spaces: {
    current: number;
    limit: number;
    unlimited: boolean;
    percentage: number;
  };
  planName: string;
  isFreePlan: boolean;
}

export function UsageLimitsCard() {
  const { user } = useAuth();
  const [usage, setUsage] = useState<UsageData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUsage = async () => {
      if (!user) return;
      
      try {
        const usageData = await getUsageForDisplay(user.uid);
        setUsage(usageData);
      } catch (error) {
        console.error('Error fetching usage data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUsage();
  }, [user]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Plan Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="animate-pulse h-4 bg-gray-200 rounded"></div>
            <div className="animate-pulse h-4 bg-gray-200 rounded"></div>
            <div className="animate-pulse h-4 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!usage) {
    return null;
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatLimit = (limit: number) => {
    return limit === -1 ? 'Unlimited' : limit.toLocaleString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Plan Usage
          </div>
          <Badge variant={usage.isFreePlan ? 'secondary' : 'default'}>
            {usage.planName.charAt(0).toUpperCase() + usage.planName.slice(1)} Plan
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Appointments */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Appointments (This Month)</span>
            </div>
            <span className="text-sm text-muted-foreground">
              {usage.appointments.current.toLocaleString()} / {formatLimit(usage.appointments.limit)}
            </span>
          </div>
          {!usage.appointments.unlimited && (
            <Progress 
              value={usage.appointments.percentage} 
              className="h-2"
            />
          )}
          {usage.appointments.percentage >= 80 && !usage.appointments.unlimited && (
            <p className="text-xs text-orange-600">
              You're approaching your monthly appointment limit
            </p>
          )}
        </div>

        {/* Customers */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Customers</span>
            </div>
            <span className="text-sm text-muted-foreground">
              {usage.customers.current.toLocaleString()} / {formatLimit(usage.customers.limit)}
            </span>
          </div>
          {!usage.customers.unlimited && (
            <Progress 
              value={usage.customers.percentage} 
              className="h-2"
            />
          )}
          {usage.customers.percentage >= 80 && !usage.customers.unlimited && (
            <p className="text-xs text-orange-600">
              You're approaching your customer limit
            </p>
          )}
        </div>

        {/* Spaces */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Salon Spaces</span>
            </div>
            <span className="text-sm text-muted-foreground">
              {usage.spaces.current.toLocaleString()} / {formatLimit(usage.spaces.limit)}
            </span>
          </div>
          {!usage.spaces.unlimited && (
            <Progress 
              value={usage.spaces.percentage} 
              className="h-2"
            />
          )}
        </div>

        {/* Upgrade CTA for free plan */}
        {usage.isFreePlan && (
          <div className="pt-4 border-t">
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Need more capacity? Upgrade to unlock unlimited features.
              </p>
              <Button asChild size="sm" className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                <Link href="/dashboard/billing">
                  Upgrade Now
                </Link>
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}