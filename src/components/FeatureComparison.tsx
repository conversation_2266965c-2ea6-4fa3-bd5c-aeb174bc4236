'use client';

import { RestrictedFeature, featureAccessMap } from '@/hooks/use-feature-access';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Check, X } from 'lucide-react';

export interface FeatureComparisonProps {
  showPricing?: boolean;
}

export function FeatureComparison({ showPricing = true }: FeatureComparisonProps) {
  // Group features by category
  const featuresByCategory = {
    basic: Object.entries(featureAccessMap)
      .filter(([, plans]) => plans.includes('free'))
      .map(([feature]) => feature as RestrictedFeature),
    premium: Object.entries(featureAccessMap)
      .filter(([, plans]) => plans.includes('pro') && !plans.includes('free'))
      .map(([feature]) => feature as RestrictedFeature),
    enterprise: Object.entries(featureAccessMap)
      .filter(([, plans]) => plans.includes('enterprise') && !plans.includes('pro'))
      .map(([feature]) => feature as RestrictedFeature),
  };

  // Feature display names and descriptions
  const featureDisplayInfo: Record<RestrictedFeature, { name: string; description: string }> = {
    [RestrictedFeature.APPOINTMENTS]: { 
      name: 'Appointment Booking', 
      description: 'Schedule and manage client appointments' 
    },
    [RestrictedFeature.CUSTOMER_MANAGEMENT]: { 
      name: 'Customer Management', 
      description: 'Maintain customer profiles and history' 
    },
    [RestrictedFeature.EMAIL_NOTIFICATIONS]: { 
      name: 'Email Notifications', 
      description: 'Send automated email reminders' 
    },
    [RestrictedFeature.LOYALTY_PROGRAM]: { 
      name: 'Loyalty Program', 
      description: 'Reward repeat customers with points and perks' 
    },
    [RestrictedFeature.REFERRAL_SYSTEM]: { 
      name: 'Referral System', 
      description: 'Generate referral codes for customers' 
    },
    [RestrictedFeature.SMS_NOTIFICATIONS]: { 
      name: 'SMS Notifications', 
      description: 'Send text message reminders to clients' 
    },
    [RestrictedFeature.STAFF_PORTAL]: { 
      name: 'Staff Portal', 
      description: 'Dedicated access for staff members' 
    },
    [RestrictedFeature.MARKETING]: { 
      name: 'Marketing Tools', 
      description: 'Create and track marketing campaigns' 
    },
    [RestrictedFeature.ANALYTICS]: { 
      name: 'Advanced Analytics', 
      description: 'Detailed business performance metrics' 
    },
    [RestrictedFeature.CUSTOM_BRANDING]: { 
      name: 'Custom Branding', 
      description: 'Remove GroomBook branding and use your own' 
    },
    [RestrictedFeature.API_ACCESS]: { 
      name: 'API Access', 
      description: 'Integrate with other business systems' 
    },
    [RestrictedFeature.DEDICATED_SUPPORT]: { 
      name: 'Dedicated Support', 
      description: 'Priority support with dedicated account manager' 
    }
  };

  // Plans data
  const plans = [
    { 
      name: 'Free', 
      price: '$0', 
      description: 'Basic functionality to get started' 
    },
    { 
      name: 'Basic', 
      price: '$9.99', 
      description: 'Essential tools for small businesses' 
    },
    { 
      name: 'Premium', 
      price: '$19.99', 
      description: 'Advanced features for growing businesses' 
    },
    { 
      name: 'Enterprise', 
      price: '$49.99', 
      description: 'Complete solution for established businesses' 
    }
  ];

  return (
    <div className="py-8">
      <h2 className="text-2xl font-bold mb-6">Compare Plan Features</h2>
      
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">Feature</TableHead>
              {plans.map((plan) => (
                <TableHead key={plan.name} className="text-center">
                  <div className="font-bold">{plan.name}</div>
                  {showPricing && (
                    <div className="text-sm font-medium">{plan.price}/mo</div>
                  )}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* Basic Features */}
            <TableRow>
              <TableCell colSpan={5} className="bg-muted/50 font-medium">
                Basic Features
              </TableCell>
            </TableRow>
            {featuresByCategory.basic.map((feature) => (
              <TableRow key={feature}>
                <TableCell>
                  <div className="font-medium">{featureDisplayInfo[feature].name}</div>
                  <div className="text-sm text-muted-foreground">{featureDisplayInfo[feature].description}</div>
                </TableCell>
                <TableCell className="text-center">
                  <X className="h-5 w-5 text-destructive mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <Check className="h-5 w-5 text-green-500 mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <Check className="h-5 w-5 text-green-500 mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <Check className="h-5 w-5 text-green-500 mx-auto" />
                </TableCell>
              </TableRow>
            ))}

            {/* Premium Features */}
            <TableRow>
              <TableCell colSpan={5} className="bg-muted/50 font-medium">
                Premium Features
              </TableCell>
            </TableRow>
            {featuresByCategory.premium.map((feature) => (
              <TableRow key={feature}>
                <TableCell>
                  <div className="font-medium">{featureDisplayInfo[feature].name}</div>
                  <div className="text-sm text-muted-foreground">{featureDisplayInfo[feature].description}</div>
                </TableCell>
                <TableCell className="text-center">
                  <X className="h-5 w-5 text-destructive mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <X className="h-5 w-5 text-destructive mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <Check className="h-5 w-5 text-green-500 mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <Check className="h-5 w-5 text-green-500 mx-auto" />
                </TableCell>
              </TableRow>
            ))}

            {/* Enterprise Features */}
            <TableRow>
              <TableCell colSpan={5} className="bg-muted/50 font-medium">
                Enterprise Features
              </TableCell>
            </TableRow>
            {featuresByCategory.enterprise.map((feature) => (
              <TableRow key={feature}>
                <TableCell>
                  <div className="font-medium">{featureDisplayInfo[feature].name}</div>
                  <div className="text-sm text-muted-foreground">{featureDisplayInfo[feature].description}</div>
                </TableCell>
                <TableCell className="text-center">
                  <X className="h-5 w-5 text-destructive mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <X className="h-5 w-5 text-destructive mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <X className="h-5 w-5 text-destructive mx-auto" />
                </TableCell>
                <TableCell className="text-center">
                  <Check className="h-5 w-5 text-green-500 mx-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
