/**
 * Test script for createPublicAppointment function
 * 
 * This script tests the enhanced createPublicAppointment function
 * in the appointmentSlice.ts file, focusing on:
 * 
 * 1. Customer lookup by phone number (in international format)
 * 2. Customer lookup by email
 * 3. Customer creation when no match is found
 * 4. Customer visit count incrementation
 */

import { customerService } from "@/services/firestore";
import { formatToInternational } from "@/utils/phoneUtils";

// Import required Redux thunk action from appointmentSlice


/**
 * Test customer lookup by phone number
 */
async function testCustomerLookupByPhone() {
  console.log('=== TESTING CUSTOMER LOOKUP BY PHONE ===');
  
  // Sample customer phone in different formats
  const phoneVariants = [
    '0712345678',       // Local format with leading zero
    '+254712345678',    // International format with plus
    '254712345678',     // International format without plus
    '712345678'         // Local format without leading zero
  ];
  
  console.log('Testing phone number formatting:');
  phoneVariants.forEach(phone => {
    const formatted = formatToInternational(phone);
    console.log(`Original: ${phone} → Formatted: ${formatted}`);
  });
  
  // Test customer lookup
  try {
    const phone = '+254712345678';
    console.log(`Looking up customer with phone: ${phone}`);
    const customer = await customerService.getByPhone(formatToInternational(phone));
    console.log('Customer found?', customer ? 'YES' : 'NO');
    if (customer) {
      console.log('Customer details:', {
        id: customer.id,
        name: customer.displayName,
        email: customer.email,
        phone: customer.phoneNumber,
        visits: customer.visits
      });
    }
  } catch (error) {
    console.error('Error during phone lookup test:', error);
  }
}

/**
 * Test customer lookup by email
 */
async function testCustomerLookupByEmail() {
  console.log('\n=== TESTING CUSTOMER LOOKUP BY EMAIL ===');
  
  // Sample customer email variants
  const emailVariants = [
    '<EMAIL>',
    '<EMAIL>',    // Different case
    ' <EMAIL> ',  // With whitespace
  ];
  
  console.log('Testing email normalization:');
  emailVariants.forEach(email => {
    const normalized = email.toLowerCase().trim();
    console.log(`Original: ${email} → Normalized: ${normalized}`);
  });
  
  // Test customer lookup
  try {
    const email = '<EMAIL>';
    console.log(`Looking up customer with email: ${email}`);
    const customers = await customerService.queryCustomers([
      { field: 'email', operator: '==', value: email.toLowerCase().trim() }
    ]);
    console.log('Customer found?', customers.length > 0 ? 'YES' : 'NO');
    if (customers.length > 0) {
      const customer = customers[0];
      console.log('Customer details:', {
        id: customer.id,
        name: customer.displayName,
        email: customer.email,
        phone: customer.phoneNumber,
        visits: customer.visits
      });
    }
  } catch (error) {
    console.error('Error during email lookup test:', error);
  }
}

/**
 * Test public appointment creation with customer handling
 */
async function testAppointmentCreation() {
  console.log('\n=== TESTING PUBLIC APPOINTMENT CREATION ===');
  
  // Sample appointment data
  const appointmentData = {
    customerName: 'Jane Smith',
    customerEmail: '<EMAIL>',
    customerPhone: '+254723456789',  // Use international format for testing
    serviceId: 'sample-service-id',  // This should be a valid service ID from your database
    staffId: 'sample-staff-id',      // This should be a valid staff ID from your database
    spaceId: 'sample-space-id',      // This should be a valid space ID from your database
    startTime: new Date('2025-06-01T10:00:00'),
    notes: 'Test appointment from script'
  };
  
  console.log('Creating appointment with data:', appointmentData);
  
  // Note: In a real test, you would dispatch this action to Redux
  // For this test script, we'll just note what would happen
  console.log('\nExpected behavior:');
  console.log('1. Check if customer exists with phone +254723456789');
  console.log('2. If not found, check if customer exists <NAME_EMAIL>');
  console.log('3. If customer exists, update visit count and use existing customer ID');
  console.log('4. If customer does not exist, create new customer record');
  console.log('5. Create appointment with the correct customer ID');
}

// Run tests
async function runTests() {
  try {
    await testCustomerLookupByPhone();
    await testCustomerLookupByEmail();
    await testAppointmentCreation();
    console.log('\nAll tests completed!');
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Note: This script is meant for testing concepts only.
// To run it, you would need to set up the proper Redux and Firebase environment.
console.log('Customer creation and lookup test script');
console.log('This script demonstrates the logic implemented in createPublicAppointment');
console.log('To run actual tests, you will need to integrate this with your test framework');
