import { useEffect, useState } from 'react';
import { useAuth } from '@/utils/auth';
import { hasFeatureAccess, getSubscriptionStatus } from '@/services/subscriptionService';

// Features that require subscription
export enum RestrictedFeature {
  // Basic features (available on free plan)
  APPOINTMENTS = 'basic_appointments',
  CUSTOMER_MANAGEMENT = 'basic_customer_management',
  EMAIL_NOTIFICATIONS = 'basic_email_notifications',
  
  // Pro features
  LOYALTY_PROGRAM = 'advanced_loyalty_program',
  REFERRAL_SYSTEM = 'advanced_referral_system',
  SMS_NOTIFICATIONS = 'advanced_sms_notifications',
  STAFF_PORTAL = 'advanced_staff_portal',
  MARKETING = 'advanced_marketing',
  ANALYTICS = 'advanced_analytics',
  
  // Enterprise features
  CUSTOM_BRANDING = 'enterprise_custom_branding',
  API_ACCESS = 'enterprise_api_access',
  DEDICATED_SUPPORT = 'enterprise_dedicated_support'
}

// Feature access by plan level
export const featureAccessMap: Record<RestrictedFeature, Array<'free' | 'basic' | 'pro' | 'enterprise'>> = {
  // Basic features
  [RestrictedFeature.APPOINTMENTS]: ['free', 'basic', 'pro', 'enterprise'],
  [RestrictedFeature.CUSTOMER_MANAGEMENT]: ['free', 'basic', 'pro', 'enterprise'],
  [RestrictedFeature.EMAIL_NOTIFICATIONS]: ['free', 'basic', 'pro', 'enterprise'],
  
  // Pro features
  [RestrictedFeature.LOYALTY_PROGRAM]: ['pro', 'enterprise'],
  [RestrictedFeature.REFERRAL_SYSTEM]: ['pro', 'enterprise'],
  [RestrictedFeature.SMS_NOTIFICATIONS]: ['pro', 'enterprise'],
  [RestrictedFeature.STAFF_PORTAL]: ['basic', 'pro', 'enterprise'],
  [RestrictedFeature.MARKETING]: ['pro', 'enterprise'],
  [RestrictedFeature.ANALYTICS]: ['pro', 'enterprise'],
  
  // Enterprise features
  [RestrictedFeature.CUSTOM_BRANDING]: ['enterprise'],
  [RestrictedFeature.API_ACCESS]: ['enterprise'],
  [RestrictedFeature.DEDICATED_SUPPORT]: ['enterprise']
};

interface UseFeatureAccessReturn {
  hasAccess: boolean;
  isLoading: boolean;
  currentPlan: 'free' | 'basic' | 'pro' | 'enterprise' | null;
}

/**
 * Hook to check if the current user has access to a specific feature.
 * During the beta period, all features are available to all users.
 */
export function useFeatureAccess(feature: RestrictedFeature): UseFeatureAccessReturn {
  const { user } = useAuth();
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentPlan, setCurrentPlan] = useState<'free' | 'basic' | 'pro' | 'enterprise' | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    const checkAccess = async () => {
      setIsLoading(true);
      
      try {
        if (!user) {
          if (isMounted) {
            setHasAccess(false);
            setCurrentPlan(null);
          }
          return;
        }
        
        // Get actual subscription status
        const status = await getSubscriptionStatus(user.uid);
        const accessGranted = await hasFeatureAccess(user.uid, feature);
        
        const plan = status.tier as 'free' | 'basic' | 'pro' | 'enterprise';
        
        if (isMounted) {
          setHasAccess(accessGranted);
          setCurrentPlan(plan);
        }
      } catch (error) {
        console.error('Error checking feature access:', error);
        if (isMounted) {
          setHasAccess(false);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    
    checkAccess();
    
    return () => {
      isMounted = false;
    };
  }, [user, feature]);
  
  return { hasAccess, isLoading, currentPlan };
}

/**
 * Checks if a given plan level has access to a feature
 */
export function planHasFeatureAccess(
  feature: RestrictedFeature, 
  plan: 'free' | 'basic' | 'pro' | 'enterprise' | null
): boolean {
  if (!plan) return false;
  return featureAccessMap[feature].includes(plan);
}
