import { configureStore } from '@reduxjs/toolkit';
import userReducer from './slices/userSlice';
import spaceReducer from './slices/spaceSlice';

export interface Space {
  id: string;
  name: string;
  // Add other space properties here
}

export const store = configureStore({
  reducer: {
    user: userReducer,
    space: spaceReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
