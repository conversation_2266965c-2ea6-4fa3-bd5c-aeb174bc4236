import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { Space } from '../store';

interface SpaceState {
  currentSpace: Space | null;
  spaces: Space[];
}

const initialState: SpaceState = {
  currentSpace: null,
  spaces: [],
};

export const spaceSlice = createSlice({
  name: 'space',
  initialState,
  reducers: {
    setCurrentSpace: (state, action: PayloadAction<Space>) => {
      state.currentSpace = action.payload;
    },
    addSpace: (state, action: PayloadAction<Space>) => {
      state.spaces.push(action.payload);
      if (!state.currentSpace) {
        state.currentSpace = action.payload;
      }
    },
  },
});

export const { setCurrentSpace, addSpace } = spaceSlice.actions;

export default spaceSlice.reducer;
