import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '@/utils/firebase';

interface OnboardingData {
  businessType: string;
  referralSource: string;
}

interface UserState {
  hasCompletedOnboarding: boolean;
  businessType: string | null;
  referralSource: string | null;
  userId: string | null;
}

const initialState: UserState = {
  hasCompletedOnboarding: false,
  businessType: null,
  referralSource: null,
  userId: null,
};

export const saveOnboardingData = createAsyncThunk(
  'user/saveOnboardingData',
  async (data: OnboardingData & { userId: string }) => {
    const { userId, ...onboardingData } = data;
    const userDoc = doc(db, 'users', userId);
    
    await setDoc(userDoc, {
      hasCompletedOnboarding: true,
      businessType: onboardingData.businessType,
      referralSource: onboardingData.referralSource,
      updatedAt: new Date().toISOString(),
    }, { merge: true });

    return onboardingData;
  }
);

export const loadUserData = createAsyncThunk(
  'user/loadUserData',
  async (userId: string) => {
    const userDoc = doc(db, 'users', userId);
    const userSnapshot = await getDoc(userDoc);
    
    if (userSnapshot.exists()) {
      return userSnapshot.data();
    }
    return null;
  }
);

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserId: (state, action: PayloadAction<string>) => {
      state.userId = action.payload;
    },
    completeOnboarding: (state, action: PayloadAction<OnboardingData>) => {
      state.hasCompletedOnboarding = true;
      state.businessType = action.payload.businessType;
      state.referralSource = action.payload.referralSource;
      
      // Automatically save to Firebase when onboarding is completed
      if (state.userId) {
        saveOnboardingData({
          userId: state.userId,
          businessType: action.payload.businessType,
          referralSource: action.payload.referralSource
        });
      }
    },
    resetUser: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(saveOnboardingData.fulfilled, (state, action) => {
        state.hasCompletedOnboarding = true;
        state.businessType = action.payload.businessType;
        state.referralSource = action.payload.referralSource;
      })
      .addCase(loadUserData.fulfilled, (state, action) => {
        if (action.payload) {
          state.hasCompletedOnboarding = action.payload.hasCompletedOnboarding ?? false;
          state.businessType = action.payload.businessType ?? null;
          state.referralSource = action.payload.referralSource ?? null;
        }
      });
  },
});

export const { setUserId, completeOnboarding, resetUser } = userSlice.actions;

export default userSlice.reducer;
