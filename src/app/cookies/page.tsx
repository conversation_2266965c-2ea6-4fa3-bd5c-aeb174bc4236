'use client';

import { useEffect } from 'react';
import { Navbar } from '@/components/landing/Navbar';
import { Footer } from '@/components/landing/Footer';
import { CookieSettings } from '@/components/CookieSettings';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON> } from 'lucide-react';

export default function CookiesPage() {
  // Automatically open the cookie settings dialog when the page loads
  useEffect(() => {
    // Find and click the cookie settings button after the component mounts
    const timer = setTimeout(() => {
      const cookieButton = document.getElementById('cookie-settings-button');
      if (cookieButton) {
        cookieButton.click();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto text-center">
          <div className="flex justify-center mb-6">
            <Cookie className="h-16 w-16 text-mustard" />
          </div>

          <h1 className="text-3xl md:text-4xl font-bold mb-6">Cookie Settings</h1>

          <p className="text-muted-foreground mb-8">
            Manage how we use cookies on our website. You can customize your preferences at any time.
          </p>

          <div className="flex justify-center mb-12">
            <CookieSettings
              trigger={
                <Button
                  id="cookie-settings-button"
                  className="bg-mustard text-black hover:bg-mustard-dark"
                >
                  Manage Cookie Preferences
                </Button>
              }
            />
          </div>

          <div className="bg-card border rounded-lg p-6 md:p-8 text-left">
            <h2 className="text-xl font-semibold mb-4">About Our Cookies</h2>

            <p className="mb-4">
              Cookies are small text files that are stored on your browser or device by websites, apps, online media, and advertisements. We use cookies for the following purposes:
            </p>

            <ul className="list-disc pl-6 space-y-2 mb-6">
              <li>
                <strong>Necessary Cookies:</strong> These cookies are essential for our website to function properly. They enable basic functions like page navigation and access to secure areas of the website.
              </li>
              <li>
                <strong>Functional Cookies:</strong> These cookies enable us to provide enhanced functionality and personalization. They may be set by us or by third-party providers whose services we have added to our pages.
              </li>
              <li>
                <strong>Analytics Cookies:</strong> These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.
              </li>
              <li>
                <strong>Marketing Cookies:</strong> These cookies are used to track visitors across websites. The intention is to display ads that are relevant and engaging for the individual user.
              </li>
            </ul>

            <p>
              You can change your cookie preferences at any time by clicking the &quot;Manage Cookie Preferences&quot; button above. For more information about how we use cookies and your personal data, please visit our <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>.
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
