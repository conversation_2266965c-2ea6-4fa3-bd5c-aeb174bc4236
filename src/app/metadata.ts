import { Metadata } from 'next';

export const metadata: Metadata = {
  metadataBase: new URL(process.env.SITE_URL || 'https://groombook.me'),
  title: {
    default: 'GroomBook - Smart Booking & Management for Salons, Barbershops & More',
    template: '%s | GroomBook'
  },
  description: 'Streamline your booking, client management, and business operations with GroomBook - the all-in-one solution for salons, barbershops, fitness centers, and wellness spaces.',
  keywords: [
    'salon booking',
    'barbershop management',
    'spa appointment system',
    'fitness center scheduling',
    'salon software',
    'business management',
    'client management',
    'appointment scheduling',
    'salon marketing'
  ],
  authors: [
    { name: 'GroomBook Team' }
  ],
  creator: '<PERSON>room<PERSON><PERSON>',
  publisher: '<PERSON>roomB<PERSON>',
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.SITE_URL || 'https://groombook.me',
    title: '<PERSON>roomBook - Smart Booking & Management for Salons, Barbershops & More',
    description: 'Streamline your booking, client management, and business operations with GroomBook - the all-in-one solution for salons, barbershops, fitness centers, and wellness spaces.',
    siteName: 'GroomBook',
    images: [
      {
        url: '/logo.png', 
        width: 1200,
        height: 630,
        alt: 'GroomBook',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GroomBook - Smart Booking & Management for Salons, Barbershops & More',
    description: 'Streamline your booking, client management, and business operations with GroomBook.',
    creator: '@groombook',
    images: ['/logo.png'],
  },
  robots: {
    index: true,
    follow: true,
    nocache: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/logo.png',
  },
  verification: {
    google: 'google-site-verification-code', // Replace with your actual verification code when you have it
  },
  alternates: {
    canonical: process.env.SITE_URL || 'https://groombook.me',
    languages: {
      'en-US': 'https://groombook.me',
    },
  },
  category: 'technology',
};
