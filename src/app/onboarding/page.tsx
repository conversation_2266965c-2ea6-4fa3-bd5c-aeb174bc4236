'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/store/hooks';
import { OnboardingContainer } from '@/components/onboarding/OnboardingContainer';
import { Loader2 } from 'lucide-react';

export default function OnboardingPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const { user, authLoading } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // Track auth state for debugging
    console.log("Auth state in onboarding page:", {
      authLoading,
      user: user ? { uid: user.uid, hasCompletedOnboarding: user.hasCompletedOnboarding } : null
    });

    // Only take action once auth state is determined (not loading)
    if (!authLoading) {
      if (user) {
        // User is authenticated
        if (user.hasCompletedOnboarding) {
          // User has already completed onboarding, redirect to dashboard
          console.log("User already completed onboarding, redirecting to dashboard");
          router.replace('/dashboard');
        } else {
          // User needs to complete onboarding, proceed with this page
          console.log("User needs to complete onboarding");
          setIsLoading(false);
        }
      } else {
        // No authenticated user, redirect to login
        console.log("No user found, redirecting to login");
        router.replace('/login');
      }
    }
  }, [user, authLoading, router]);

  // Show loading indicator while checking auth state
  if (isLoading || authLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return <OnboardingContainer />;
}
