'use client';

import { Navbar } from '@/components/landing/Navbar';
import { Footer } from '@/components/landing/Footer';
import { Button } from '@/components/ui/button';
import { FileText, Clock } from 'lucide-react';
import Link from 'next/link';

export default function TermsPage() {
  const lastUpdated = "May 12, 2025";

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center mb-8">
            <FileText className="h-8 w-8 text-mustard mr-3" />
            <h1 className="text-3xl md:text-4xl font-bold">Terms of Service</h1>
          </div>

          <div className="flex items-center text-sm text-muted-foreground mb-8">
            <Clock className="h-4 w-4 mr-2" />
            <span>Last updated: {lastUpdated}</span>
          </div>

          <div className="space-y-8 max-w-none">
            <div className="bg-card rounded-lg p-6 border">
              <p className="text-lg">
                Please read these Terms of Service (&quot;Terms&quot;) carefully before using the GroomBook platform. By accessing or using our services, you agree to be bound by these Terms.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">1. Acceptance of Terms</h2>
              <p>
                By accessing or using GroomBook, you agree to these Terms and our Privacy Policy. If you do not agree to these Terms, please do not use our services.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">2. Description of Service</h2>
              <p>
                GroomBook provides salon and barbershop management tools, including appointment scheduling, customer management, inventory tracking, and marketing features. We reserve the right to modify, suspend, or discontinue any aspect of our services at any time.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">3. User Accounts</h2>
              <p className="mb-4">
                To use certain features of our services, you must create an account. You are responsible for:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Providing accurate and complete information</li>
                <li>Maintaining the confidentiality of your account credentials</li>
                <li>All activities that occur under your account</li>
                <li>Notifying us immediately of any unauthorized use</li>
              </ul>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">4. User Responsibilities</h2>
              <p className="mb-4">
                When using our services, you agree not to:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Violate any applicable laws or regulations</li>
                <li>Infringe on the rights of others</li>
                <li>Submit false or misleading information</li>
                <li>Upload or transmit viruses or malicious code</li>
                <li>Attempt to gain unauthorized access to our systems</li>
                <li>Use our services for any illegal or unauthorized purpose</li>
              </ul>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">5. Payment Terms</h2>
              <p className="mb-4">
                Subscription fees are billed in advance on a monthly or annual basis. By subscribing to our services, you authorize us to charge your payment method for the subscription fee at the then-current rate.
              </p>
              <p>
                Refunds are governed by our <Link href="/refund" className="text-primary hover:underline">Refund Policy</Link>. Please review it carefully before subscribing to our services.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">6. Intellectual Property</h2>
              <p>
                All content, features, and functionality of our services, including but not limited to text, graphics, logos, and software, are owned by GroomBook and are protected by copyright, trademark, and other intellectual property laws.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">7. User Content</h2>
              <p>
                You retain ownership of any content you submit to our services. By submitting content, you grant us a worldwide, non-exclusive, royalty-free license to use, reproduce, modify, and display such content in connection with providing our services.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">8. Limitation of Liability</h2>
              <p>
                To the maximum extent permitted by law, GroomBook shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including but not limited to loss of profits, data, or business opportunities, arising out of or in connection with your use of our services.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">9. Indemnification</h2>
              <p>
                You agree to indemnify and hold harmless GroomBook and its officers, directors, employees, and agents from any claims, liabilities, damages, losses, and expenses arising out of or in any way connected with your use of our services or violation of these Terms.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">10. Termination</h2>
              <p>
                We may terminate or suspend your account and access to our services at any time, without prior notice or liability, for any reason, including but not limited to a breach of these Terms.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">11. Governing Law</h2>
              <p>
                These Terms shall be governed by and construed in accordance with the laws of Kenya, without regard to its conflict of law provisions.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">12. Changes to Terms</h2>
              <p>
                We reserve the right to modify these Terms at any time. We will provide notice of significant changes by posting the updated Terms on our website and updating the &quot;Last updated&quot; date. Your continued use of our services after such changes constitutes your acceptance of the new Terms.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">13. Contact Us</h2>
              <p className="mb-4">
                If you have any questions about these Terms, please contact us at:
              </p>
              <p>
                <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium">
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>

          <div className="mt-12 flex justify-between items-center">
            <Button asChild variant="outline">
              <Link href="/privacy">Privacy Policy</Link>
            </Button>

            <Button asChild className="bg-mustard text-black hover:bg-mustard-dark">
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
