'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { staffPortalService } from '@/services/staffPortalService';
import { toast } from 'sonner';
import ResetPinLoading from './loading';
import { Suspense } from 'react';

// PIN reset schema
const pinResetSchema = z.object({
  newPin: z.string()
    .length(4, 'PIN must be exactly 4 digits')
    .regex(/^\d+$/, 'PIN must contain only digits'),
  confirmPin: z.string()
    .length(4, 'PIN must be exactly 4 digits')
    .regex(/^\d+$/, 'PIN must contain only digits'),
}).refine((data) => data.newPin === data.confirmPin, {
  message: "PINs don't match",
  path: ["confirmPin"],
});

const currentPinSchema = z.object({
  pin: z.string()
    .length(4, 'PIN must be exactly 4 digits')
    .regex(/^\d+$/, 'PIN must contain only digits'),
});

type PinResetFormData = z.infer<typeof pinResetSchema>;
type CurrentPinFormData = z.infer<typeof currentPinSchema>;

export default function ResetPinPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const staffId = searchParams.get('id');
  const spaceId = searchParams.get('spaceId');
  
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPin, setCurrentPin] = useState<string>('');
  const [step, setStep] = useState<'enterCurrentPin' | 'setNewPin'>('enterCurrentPin');

  // Redirect if no staff ID or space ID is provided
  useEffect(() => {
    if (!staffId || !spaceId) {
      router.push('/staff');
    }
  }, [staffId, spaceId, router]);

  const resetForm = useForm<PinResetFormData>({
    resolver: zodResolver(pinResetSchema),
    defaultValues: {
      newPin: '',
      confirmPin: '',
    },
  });

  const currentPinForm = useForm<CurrentPinFormData>({
    resolver: zodResolver(currentPinSchema),
    defaultValues: {
      pin: '',
    },
  });

  async function onVerifyCurrentPin(data: CurrentPinFormData) {
    if (!staffId) return;
    
    try {
      setError(null);
      setIsLoading(true);

      // Verify current PIN
      const result = await staffPortalService.verifyPin(data.pin);
      
      if (!result.isValid || result.staffId !== staffId) {
        setError('Invalid PIN. Please try again.');
        return;
      }

      // Store current PIN for use in the next step
      setCurrentPin(data.pin);
      setStep('setNewPin');
    } catch (error) {
      console.error('PIN verification error:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }

  async function onSetNewPin(data: PinResetFormData) {
    if (!staffId || !currentPin || !spaceId) return;
    
    try {
      setError(null);
      setIsLoading(true);

      // Update PIN
      const success = await staffPortalService.updatePin(staffId, currentPin, data.newPin);
      
      if (!success) {
        setError('Failed to update PIN. Please try again.');
        return;
      }

      toast.success('PIN updated successfully!');
      
      // Redirect to staff dashboard with secure URL structure
      router.push(`/staff/dashboard?spaceId=${spaceId}&staffId=${staffId}`);
    } catch (error) {
      console.error('PIN reset error:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Suspense fallback={<ResetPinLoading />}>
      <div className="flex items-center justify-center min-h-screen p-4 bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>{step === 'enterCurrentPin' ? 'Verify Your PIN' : 'Set Your New PIN'}</CardTitle>
            <CardDescription>
              {step === 'enterCurrentPin' 
                ? 'Please enter your current PIN to continue.'
                : 'You need to set a new PIN for your staff portal access.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            {step === 'enterCurrentPin' ? (
              <Form {...currentPinForm}>
                <form onSubmit={currentPinForm.handleSubmit(onVerifyCurrentPin)} className="space-y-6">
                  <FormField
                    control={currentPinForm.control}
                    name="pin"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current PIN</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Enter your current PIN"
                            inputMode="numeric"
                            maxLength={4}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={isLoading}
                  >
                    {isLoading ? "Verifying..." : "Verify PIN"}
                  </Button>
                </form>
              </Form>
            ) : (
              <Form {...resetForm}>
                <form onSubmit={resetForm.handleSubmit(onSetNewPin)} className="space-y-6">
                  <FormField
                    control={resetForm.control}
                    name="newPin"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New PIN</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Enter a 4-digit PIN"
                            inputMode="numeric"
                            maxLength={4}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={resetForm.control}
                    name="confirmPin"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm PIN</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Confirm your PIN"
                            inputMode="numeric"
                            maxLength={4}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={isLoading}
                  >
                    {isLoading ? "Updating..." : "Update PIN"}
                  </Button>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
      </div>
    </Suspense>
  );
}
