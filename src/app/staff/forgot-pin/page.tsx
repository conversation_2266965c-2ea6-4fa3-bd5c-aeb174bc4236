'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { staffPortalService } from '@/services/staffPortalService';
import { toast } from 'sonner';
import Link from 'next/link';

// Staff PIN reset request schema
const forgotPinSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ForgotPinFormData = z.infer<typeof forgotPinSchema>;

export default function ForgotPinPage() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ForgotPinFormData>({
    resolver: zodResolver(forgotPinSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(data: ForgotPinFormData) {
    try {
      setError(null);
      setIsLoading(true);

      // Request PIN reset
      await staffPortalService.requestPinReset(data.email);
      
      // Show success state, no need to check result as we want to show the same message either way
      // for security reasons
      setSuccess(true);
    } catch (error) {
      console.error('PIN reset request error:', error);
      // Still show success message even on error, for security reasons
      setSuccess(true);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Forgot Your PIN?</CardTitle>
          <CardDescription>
            Enter your email to request a PIN reset from your space management.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {success ? (
            <div className="space-y-4">
              <Alert className="mb-6 bg-green-50 border-green-200">
                <AlertTitle className="text-green-700">Request submitted</AlertTitle>
                <AlertDescription className="text-green-600">
                  If your email is associated with a staff account, your space management will be notified to reset your PIN.
                </AlertDescription>
              </Alert>
              <Button 
                className="w-full" 
                onClick={() => router.push('/staff')}
              >
                Return to Login
              </Button>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          type="email" 
                          placeholder="Enter your email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isLoading}
                >
                  {isLoading ? "Submitting..." : "Request PIN Reset"}
                </Button>
              </form>
            </Form>
          )}
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-6">
          <div className="text-sm text-gray-500">
            Remember your PIN? <Link href="/staff" className="text-blue-600 hover:underline">Back to login</Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
