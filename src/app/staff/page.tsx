'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';
import { KeyRound } from 'lucide-react';
import Link from 'next/link';

const STAFF_PORTAL_BENEFITS = [
  "Create staff accounts with personalized access",
  "Staff can manage their own schedules",
  "Track staff performance and metrics",
  "Secure PIN-based login system for staff",
  "Allow staff to view only their relevant appointments"
];

function StaffPortalContent() {
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const router = useRouter();

  const handlePinSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (pin.length !== 4 || !/^\d+$/.test(pin)) {
      setError('Please enter a valid 4-digit PIN');
      return;
    }
    
    setError('Please use the secure portal link provided by your space manager.');
  };

  return (
    <div className="container mx-auto py-10 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Staff Portal</h1>
        <p className="text-muted-foreground">Access your appointments and schedule</p>
        <p className="text-sm text-amber-600">
          ⚠️ Please use the secure portal link provided by your space manager for access.
        </p>
      </div>
      
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-center gap-2">
              <KeyRound className="h-5 w-5" />
              <span>Staff Login</span>
            </CardTitle>
            <CardDescription className="text-center">
              Enter your PIN to access your dashboard
            </CardDescription>
          </CardHeader>
          <form onSubmit={handlePinSubmit}>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Input
                    type="password"
                    maxLength={4}
                    placeholder="Enter your 4-digit PIN"
                    value={pin}
                    onChange={(e) => {
                      setPin(e.target.value);
                      setError('');
                    }}
                    className="text-center text-xl tracking-widest"
                  />
                  {error && <p className="text-destructive text-sm">{error}</p>}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button type="submit" className="w-full">
                Continue
              </Button>
              <Button asChild variant="ghost" className="w-full">
                <Link href="/staff/forgot-pin">
                  Forgot PIN?
                </Link>
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}

export default function StaffPortalPage() {
  return (
    <FeatureGate
      feature={RestrictedFeature.STAFF_PORTAL}
      title="Staff Portal"
      description="Allow your staff to access their schedules and appointments."
      benefits={STAFF_PORTAL_BENEFITS}
    >
      <StaffPortalContent />
    </FeatureGate>
  );
}