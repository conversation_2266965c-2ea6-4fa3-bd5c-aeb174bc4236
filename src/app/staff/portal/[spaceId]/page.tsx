'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { KeyRound, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { staffPortalService } from '@/services/staffPortalService';
import { publicAppointmentService } from '@/services/publicAppointmentService';
import { toast } from 'sonner';
import { Space } from '@/services/types/models';

export default function StaffPortalPage() {
  const params = useParams();
  const router = useRouter();
  const spaceId = params.spaceId as string;
  
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [space, setSpace] = useState<Space | null>(null);
  const [isLoadingSpace, setIsLoadingSpace] = useState(true);

  useEffect(() => {
    const fetchSpace = async () => {
      try {
        setIsLoadingSpace(true);

        // Run PIN migration first (this is safe to run multiple times)
        try {
          await staffPortalService.migrateStaffPins();
        } catch (migrationError) {
          console.warn('PIN migration failed, but continuing:', migrationError);
        }

        const spaceData = await publicAppointmentService.getSpaceDetails(spaceId);
        if (!spaceData) {
          router.push('/staff');
          return;
        }
        setSpace(spaceData);
      } catch (error) {
        console.error('Error fetching space:', error);
        router.push('/staff');
      } finally {
        setIsLoadingSpace(false);
      }
    };

    if (spaceId) {
      fetchSpace();
    }
  }, [spaceId, router]);

  const handlePinSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (pin.length !== 4 || !/^\d+$/.test(pin)) {
      setError('Please enter a valid 4-digit PIN');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      console.log('Attempting to verify PIN:', pin);
      const result = await staffPortalService.verifyPin(pin);
      console.log('PIN verification result:', result);

      if (!result.isValid) {
        setError('Invalid PIN. Please check your PIN and try again. If this is a new staff member, the admin may need to run a PIN migration.');
        return;
      }

      // Check if the staff member belongs to this space
      console.log('Getting staff data for ID:', result.staffId);
      const staffData = await staffPortalService.getStaffData(result.staffId!);
      console.log('Staff data:', staffData);

      if (!staffData) {
        setError('Staff member not found. Please contact your administrator.');
        return;
      }

      if (staffData.spaceId !== spaceId) {
        setError(`This PIN is not valid for this location. Your PIN is for a different location.`);
        return;
      }

      // Check if it's first login and PIN needs to be changed
      if (result.isFirstLogin) {
        console.log('First login detected, redirecting to PIN reset');
        router.push(`/staff/reset-pin?id=${result.staffId}&spaceId=${spaceId}`);
        return;
      }

      // Successful login - redirect to staff dashboard
      console.log('Login successful, redirecting to dashboard');
      router.push(`/staff/dashboard?spaceId=${spaceId}&staffId=${result.staffId}`);

    } catch (error) {
      console.error('Login error:', error);
      setError('An error occurred during login. Please try again or contact your administrator.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingSpace) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F5B800]"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!space) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Location Not Found</CardTitle>
            <CardDescription>
              The staff portal for this location could not be found.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/staff">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Staff Portal
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="w-full max-w-md space-y-6">
        {/* Space Info */}
        <div className="text-center">
          <h1 className="text-3xl font-bold">{space.name}</h1>
          <p className="text-muted-foreground">Staff Portal</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-center gap-2">
              <KeyRound className="h-5 w-5" />
              <span>Staff Login</span>
            </CardTitle>
            <CardDescription className="text-center">
              Enter your PIN to access your dashboard
            </CardDescription>
          </CardHeader>
          <form onSubmit={handlePinSubmit}>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Input
                    type="password"
                    maxLength={4}
                    placeholder="Enter your 4-digit PIN"
                    value={pin}
                    onChange={(e) => {
                      setPin(e.target.value);
                      setError('');
                    }}
                    className="text-center text-xl tracking-widest"
                    disabled={isLoading}
                  />
                  {error && <p className="text-destructive text-sm text-center">{error}</p>}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Signing in...' : 'Continue'}
              </Button>
              <Button asChild variant="ghost" className="w-full">
                <Link href="/staff/forgot-pin">
                  Forgot PIN?
                </Link>
              </Button>
            </CardFooter>
          </form>
        </Card>

        <div className="text-center">
          <Button asChild variant="ghost" size="sm">
            <Link href="/staff">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Staff Portal
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
