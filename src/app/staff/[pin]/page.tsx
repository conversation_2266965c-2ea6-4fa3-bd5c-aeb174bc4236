'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function StaffPortalLegacyPage() {
  const router = useRouter();

  // Redirect to the main staff portal page
  useEffect(() => {
    router.replace('/staff');
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Redirecting...</h1>
        <p className="text-gray-600">Please wait while we redirect you to the staff portal.</p>
        <div className="mt-4">
          <div className="h-8 w-8 mx-auto rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
        </div>
      </div>
    </div>
  );
}
