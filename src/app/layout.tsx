import type { Metadata } from "next";
import "./globals.css";
import { AuthProvider } from "@/utils/auth";
import { ReduxProvider } from "@/providers/ReduxProvider";
import { AuthGuard } from "@/components/auth/AuthGuard";
import { Plus_Jakarta_Sans, Lato } from 'next/font/google';
import { CookieConsentBanner } from "@/components/CookieConsentBanner";
import { PostHogProvider } from "@/providers/PostHogProvider";


const jakarta = Plus_Jakarta_Sans({
  subsets: ['latin'],
  variable: '--font-jakarta',
  display: 'swap',
});

const lato = Lato({
  subsets: ['latin'],
  variable: '--font-lato',
  weight: ['100', '300', '400', '700', '900'],
  display: 'swap',
});

export const metadata: Metadata = {
  metadataBase: new URL('https://groombook.com'), // Replace with your domain
  title: {
    default: '<PERSON>roomBook | Complete Business Management Platform for Salons, Spas, Fitness & Wellness Centers',
    template: '%s | GroomBook'
  },
  description: 'All-in-one management solution for salons, barbershops, spas, fitness centers and wellness spaces with scheduling, customer management, and marketing capabilities.',
  keywords: ['salon software', 'barbershop management', 'spa booking', 'fitness center management', 'appointment booking', 'business management', 'wellness center'],
  authors: [{ name: 'GroomBook' }],
  creator: 'GroomBook',
  publisher: 'GroomBook',
  formatDetection: {
    email: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://groombook.com',
    title: 'GroomBook | Complete Business Management Platform',
    description: 'All-in-one management solution for salons, barbershops, spas, fitness centers and wellness spaces with scheduling, customer management, and marketing capabilities.',
    siteName: 'GroomBook',
    images: [
      {
        url: '/images/groombooks.png',
        width: 1200,
        height: 630,
        alt: 'GroomBook - Business Management Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GroomBook | Complete Business Management Platform',
    description: 'All-in-one management solution for salons, barbershops, spas, fitness centers and wellness spaces.',
    creator: '@groombook',
    images: ['/images/groombooks.png'],
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://groombook.com',
    languages: {
      'en-US': 'https://groombook.com',
      'es-ES': 'https://groombook.com/es',
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <html lang="en" className={`${jakarta.variable} ${lato.variable} font-jakarta`}>
        <head>
          <script
            src="https://cdn.paddle.com/paddle/v2/paddle.js"
            async
          />
        </head>
        <body className="font-jakarta">
          <PostHogProvider>
            <ReduxProvider>
              <AuthProvider>
                <AuthGuard>{children}</AuthGuard>
              </AuthProvider>
            </ReduxProvider>
            <CookieConsentBanner />
          </PostHogProvider>
        </body>
      </html>
    </>

  );
}