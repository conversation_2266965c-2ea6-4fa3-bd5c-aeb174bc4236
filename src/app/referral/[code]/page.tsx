'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { customerService } from '@/services/firestore';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from 'sonner';
import { 
  Gift, 
  ArrowRight,
  Disc3,
  AlertCircle
} from 'lucide-react';

interface ReferralPageProps {
  params: {
    code: string;
  };
}

export default function ReferralPage({ params }: ReferralPageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [referrerName, setReferrerName] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  
  const { code } = params;
  
  useEffect(() => {
    async function validateReferralCode() {
      if (!code) {
        setError('Invalid referral code');
        setIsLoading(false);
        return;
      }
      
      try {
        // Find the customer with this referral code
        const customers = await customerService.queryCustomers([
          { field: 'loyaltyPoints.referralCode', operator: '==', value: code }
        ]);
        
        if (customers.length === 0) {
          setError('Invalid referral code');
          setIsLoading(false);
          return;
        }
        
        const referrer = customers[0];
        setReferrerName(referrer.displayName);
        
        // Store the referral code in session storage for later use during signup
        sessionStorage.setItem('referralCode', code);
        sessionStorage.setItem('referrerId', referrer.id);
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error validating referral code:', error);
        setError('An error occurred while validating the referral code');
        setIsLoading(false);
      }
    }
    
    validateReferralCode();
  }, [code]);
  
  const handleContinue = () => {
    router.push('/register?referral=true');
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Disc3 className="h-8 w-8 animate-spin" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen p-4">
        <Card className="max-w-md w-full p-6 text-center">
          <div className="mb-6">
            <div className="bg-red-100 text-red-700 rounded-full p-3 inline-flex">
              <AlertCircle className="h-6 w-6" />
            </div>
          </div>
          <h1 className="text-2xl font-bold mb-2">Invalid Referral</h1>
          <p className="text-muted-foreground mb-6">
            {error}. Please check the link and try again, or continue without a referral.
          </p>
          <Button onClick={() => router.push('/register')}>
            Continue to Registration
          </Button>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="flex justify-center items-center min-h-screen p-4">
      <Card className="max-w-md w-full p-6 text-center">
        <div className="mb-6">
          <div className="bg-green-100 text-green-700 rounded-full p-3 inline-flex">
            <Gift className="h-6 w-6" />
          </div>
        </div>
        <h1 className="text-2xl font-bold mb-2">You've Been Invited!</h1>
        <p className="text-muted-foreground mb-6">
          {referrerName} has invited you to join our space. 
          Register now to claim your welcome bonus and help {referrerName} earn referral points!
        </p>
        <Button onClick={handleContinue} className="w-full">
          Continue to Registration <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        <p className="text-xs text-muted-foreground mt-4">
          Referral code: {code}
        </p>
      </Card>
    </div>
  );
}
