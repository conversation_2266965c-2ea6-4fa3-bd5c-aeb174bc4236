import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileQuestion } from "lucide-react";

export default function NotFound() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="text-center">
        <div className="flex justify-center">
          <FileQuestion className="h-12 w-12 text-gray-400" />
        </div>
        <h1 className="mt-4 text-2xl font-bold tracking-tight">
          Page not found
        </h1>
        <p className="mt-2 text-gray-600">
          Sorry, we couldn&apos;t find the page you&apos;re looking for.
        </p>
        <div className="mt-6">
          <Button asChild>
            <Link href="/">
              Back to home
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}