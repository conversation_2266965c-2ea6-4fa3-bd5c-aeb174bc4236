'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAppDispatch } from '@/store/hooks';
import { resetPassword } from '@/store/slices/authSlice';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type FormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(data: FormData) {
    try {
      setError(null);
      setIsLoading(true);
      const resultAction = await dispatch(resetPassword(data.email));

      if (resetPassword.fulfilled.match(resultAction)) {
        setIsSuccess(true);
      } else if (resetPassword.rejected.match(resultAction)) {
        setError(resultAction.payload as string || 'Failed to send password reset email. Please try again.');
      }
    } catch (error: any) {
      console.error('Password reset error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex">
      {/* Left side - Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          <div>
            <div className="mb-12">
              <h1 className="text-2xl font-bold">GroomBook</h1>
            </div>
            <h2 className="text-4xl font-bold mb-4">Forgot Password</h2>
            <p className="text-gray-500">
              {isSuccess
                ? "We've sent you an email with instructions to reset your password."
                : "Enter your email address and we'll send you a link to reset your password."}
            </p>
          </div>

          {isSuccess ? (
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 text-green-700 px-6 py-8 rounded-md text-sm flex flex-col items-center">
                <CheckCircle className="h-12 w-12 mb-4 text-green-500" />
                <h3 className="text-lg font-medium mb-2">Email Sent!</h3>
                <p className="text-center">
                  We&apos;ve sent a password reset link to <strong>{form.getValues().email}</strong>.
                  Please check your inbox and follow the instructions to reset your password.
                </p>
              </div>
              <Button
                onClick={() => router.push('/login')}
                className="w-full bg-[#F5B800] hover:bg-[#E5AB00] text-black font-medium"
              >
                Return to Login
              </Button>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm flex items-center">
                    <span className="mr-2">⚠️</span>
                    {error}
                  </div>
                )}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                          <Input
                            placeholder="Enter your email address"
                            type="email"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-[#F5B800] hover:bg-[#E5AB00] text-black font-medium"
                >
                  {isLoading ? 'Sending...' : 'Send Reset Link'}
                </Button>

                <div className="flex items-center justify-center">
                  <Link
                    href="/login"
                    className="inline-flex items-center text-sm text-[#F5B800] hover:text-[#E5AB00] font-medium"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Login
                  </Link>
                </div>
              </form>
            </Form>
          )}
        </div>
      </div>

      {/* Right side - Image */}
      <div className="hidden lg:block flex-1 relative p-5">
        <Image
          src="/space-4.jpg"
          alt="Space background we are open"
          fill
          className="object-cover rounded-md"
          priority
        />
      </div>
    </div>
  );
}
