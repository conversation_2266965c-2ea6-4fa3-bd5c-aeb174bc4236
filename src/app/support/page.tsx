'use client';

import { useState } from 'react';
import { Navbar } from '@/components/landing/Navbar';
import { Footer } from '@/components/landing/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Search, Mail, MessageSquare, FileText, HelpCircle } from 'lucide-react';
import Link from 'next/link';

export default function SupportPage() {
  const [searchQuery, setSearchQuery] = useState('');

  const faqs = [
    {
      question: "How do I create a new appointment?",
      answer: "To create a new appointment, navigate to the Appointments tab in your dashboard, click on 'New Appointment', select a customer (or create a new one), choose the service, staff member, date, and time, then click 'Save'."
    },
    {
      question: "How do I manage my services?",
      answer: "You can manage your services by going to the Services tab in your dashboard. Here you can add new services, edit existing ones, set prices, and organize them into categories."
    },
    {
      question: "Can I send reminders to customers?",
      answer: "Yes, GroomBook automatically sends appointment reminders to customers. You can configure reminder settings in the Settings tab under 'Notifications'."
    },
    {
      question: "How do I track inventory?",
      answer: "To track inventory, go to the Inventory tab in your dashboard. You can add products, set stock levels, and track usage. The system will alert you when items are running low."
    },
    {
      question: "How do I export my data?",
      answer: "You can export your data by going to Settings > Data Management > Export. You can choose to export customers, appointments, services, or financial data in CSV format."
    },
    {
      question: "How do I reset my password?",
      answer: "If you forgot your password, click on 'Forgot Password' on the login page. Enter your email address, and we'll send you a link to reset your password."
    },
  ];

  const filteredFaqs = searchQuery
    ? faqs.filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs;

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-6">Support Center</h1>
          <p className="text-muted-foreground mb-8">
            Find answers to common questions or get in touch with our support team.
          </p>

          <div className="relative mb-12">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
            <Input
              type="search"
              placeholder="Search for help..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <Link href="/contact" className="flex flex-col items-center text-center p-6 border rounded-lg hover:border-primary transition-colors">
              <Mail className="h-8 w-8 text-mustard mb-4" />
              <h3 className="text-lg font-semibold mb-2">Email Support</h3>
              <p className="text-muted-foreground">
                Send us an email and we&apos;ll get back to you within 24 hours.
              </p>
            </Link>

            <div className="flex flex-col items-center text-center p-6 border rounded-lg">
              <MessageSquare className="h-8 w-8 text-mustard mb-4" />
              <h3 className="text-lg font-semibold mb-2">Live Chat</h3>
              <p className="text-muted-foreground mb-2">
                Chat with our support team in real-time.
              </p>
              <Button disabled variant="outline" size="sm">
                Start Chat (Coming Soon)
              </Button>
            </div>

            <Link href="" className="flex flex-col items-center text-center p-6 border rounded-lg hover:border-primary transition-colors">
              <FileText className="h-8 w-8 text-mustard mb-4" />
              <h3 className="text-lg font-semibold mb-2">Documentation (Coming Soon)</h3>
              <p className="text-muted-foreground">
                Browse our detailed documentation and tutorials.
              </p>
            </Link>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-6 flex items-center">
              <HelpCircle className="h-6 w-6 mr-2 text-mustard" />
              Frequently Asked Questions
            </h2>

            {filteredFaqs.length > 0 ? (
              <Accordion type="single" collapsible className="w-full">
                {filteredFaqs.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger className="text-left">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            ) : (
              <div className="text-center p-8 border rounded-lg">
                <p className="text-muted-foreground">
                  No results found for &quot;{searchQuery}&quot;. Try a different search term or contact our support team.
                </p>
              </div>
            )}
          </div>

          <div className="bg-card border rounded-lg p-6 md:p-8 text-center">
            <h2 className="text-xl font-semibold mb-4">Still Need Help?</h2>
            <p className="text-muted-foreground mb-6">
              Our support team is available Monday through Friday, 9am to 5pm.
            </p>
            <Button asChild className="bg-mustard text-black hover:bg-mustard-dark">
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
