'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAppDispatch } from '@/store/hooks';
import { loginUser } from '@/store/slices/authSlice';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { AnimatedGreeting } from '@/components/AnimatedGreeting';
import posthog from 'posthog-js';
import { GoogleSignInButton } from '@/components/auth/GoogleSignInButton';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

type FormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  async function onSubmit(data: FormData) {
    try {
      setError(null);
      setIsLoading(true);

      const resultAction = await dispatch(loginUser({
        email: data.email,
        password: data.password
      }));

      if (loginUser.fulfilled.match(resultAction)) {
        // Track successful login in background
        setTimeout(() => {
          posthog.capture('login_success', {
            hasCompletedOnboarding: resultAction.payload.hasCompletedOnboarding
          });
        }, 0);

        // Check onboarding status and redirect accordingly
        const userData = resultAction.payload;
        if (!userData.hasCompletedOnboarding) {
          router.push('/onboarding');
        } else {
          router.push('/dashboard');
        }
      } else if (loginUser.rejected.match(resultAction)) {
        const errorMessage = resultAction.payload as string || 'Login failed. Please check your credentials.';
        setError(errorMessage);

        // Track failed login in background
        setTimeout(() => {
          posthog.capture('login_failed', {
            error: errorMessage,
          });
        }, 0);
      }
    } catch (error: any) {
      console.error('Login error:', error);
      const errorMessage = 'An unexpected error occurred. Please try again.';
      setError(errorMessage);

      // Track error in background
      setTimeout(() => {
        posthog.capture('login_error', {
          error: errorMessage,
        });
      }, 0);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex">
      {/* Left side - Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          <div>
            <div className="mb-12">
              <h1 className="text-2xl font-bold">GroomBook</h1>
            </div>
            <AnimatedGreeting />
            <h2 className="text-4xl font-bold mb-4">Welcome Back!</h2>
            <p className="text-gray-500">To your special place!</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm flex items-center">
                  <span className="mr-2">⚠️</span>
                  {error}
                </div>
              )}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          className="pl-10"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••••••"
                          className="pl-10 pr-12"
                          {...field}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <label className="text-sm text-gray-600">
                        Remember me
                      </label>
                    </div>
                    <Link href="/forgot-password" className="text-sm text-[#F5B800] hover:text-[#E5AB00] font-medium">
                      Forgot Password?
                    </Link>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-[#F5B800] hover:bg-[#E5AB00] text-black font-medium"
              >
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>
          </Form>

          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">or continue with</span>
            </div>
          </div>

          <GoogleSignInButton mode="sign-in" />

          <p className="text-center text-sm text-gray-600 mt-6">
            Don&apos;t have an account?{' '}
            <Link href="/register" className="text-[#F5B800] hover:text-[#E5AB00] font-medium">
              Sign Up
            </Link>
          </p>
        </div>
      </div>

      {/* Right side - Image */}
      <div className="hidden lg:block flex-1 relative p-5">
        <Image
          src="/space-4.jpg"
          alt="Space We are open"
          fill
          className="object-cover rounded-md"
          priority
        />
      </div>
    </div>
  );
}