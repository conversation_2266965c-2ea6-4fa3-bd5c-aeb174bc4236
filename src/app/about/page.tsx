'use client';

import { Navbar } from '@/components/landing/Navbar';
import { Footer } from '@/components/landing/Footer';
import Image from 'next/image';
import { Users, Award, Clock, Target } from 'lucide-react';

export default function AboutPage() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-6">About GroomBook</h1>

          <div className="mb-12">
            <p className="text-lg mb-4">
              GroomBook is a comprehensive service business management platform designed to streamline operations, enhance customer experiences, and boost business growth.
            </p>
            <p className="text-muted-foreground mb-8">
              Our mission is to empower service business owners with intuitive tools that simplify day-to-day operations, allowing them to focus on what they do best – providing exceptional service to their clients.
            </p>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">Our Story</h2>
            <p className="mb-4">
              Founded in 2025, GroomBook was born from a simple observation: service business owners were spending too much time on administrative tasks and not enough time with their clients. They ended up losing money!.
            </p>
            <p className="mb-4">
              Our partner, having experienced these challenges firsthand while managing a family-owned business, set out to create a solution that would address the unique needs of the beauty, wellness, and grooming industry.
            </p>
            <p className="mb-4">
              What started as a simple staff management tool has evolved into a comprehensive platform that handles everything from scheduling and customer management to inventory tracking and marketing campaigns.
            </p>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">Our Values</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-6 border rounded-lg">
                <Users className="h-8 w-8 text-mustard mb-4" />
                <h3 className="text-lg font-semibold mb-2">Customer-Centric</h3>
                <p className="text-muted-foreground">
                  We put our users first, designing every feature with their needs in mind.
                </p>
              </div>

              <div className="p-6 border rounded-lg">
                <Award className="h-8 w-8 text-mustard mb-4" />
                <h3 className="text-lg font-semibold mb-2">Excellence</h3>
                <p className="text-muted-foreground">
                  We strive for excellence in everything we do, from code quality to customer support.
                </p>
              </div>

              <div className="p-6 border rounded-lg">
                <Clock className="h-8 w-8 text-mustard mb-4" />
                <h3 className="text-lg font-semibold mb-2">Efficiency</h3>
                <p className="text-muted-foreground">
                  We believe in creating tools that save time and reduce complexity.
                </p>
              </div>

              <div className="p-6 border rounded-lg">
                <Target className="h-8 w-8 text-mustard mb-4" />
                <h3 className="text-lg font-semibold mb-2">Innovation</h3>
                <p className="text-muted-foreground">
                  We continuously innovate to stay ahead of industry needs and trends.
                </p>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-2xl font-semibold mb-6">Our Team</h2>
            <p className="mb-8">
              GroomBook is powered by a dedicated team of developers, designers, and industry experts who are passionate about creating the best salon management solution on the market.
            </p>

            <div className="bg-card border rounded-lg p-6 md:p-8 text-center">
              <h3 className="text-xl font-semibold mb-4">Join Our Team</h3>
              <p className="text-muted-foreground mb-4">
                We&apos;re always looking for talented individuals to join our growing team. Check out our careers page for current openings.
              </p>
              <p className="text-primary">
                <EMAIL>
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
