'use client';

import { Navbar } from '@/components/landing/Navbar';
import { Footer } from '@/components/landing/Footer';
import { Button } from '@/components/ui/button';
import { FileText, Clock } from 'lucide-react';
import Link from 'next/link';

export default function RefundPolicyPage() {
  const lastUpdated = "May 20, 2025";

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center mb-8">
            <FileText className="h-8 w-8 text-mustard mr-3" />
            <h1 className="text-3xl md:text-4xl font-bold">Refund Policy</h1>
          </div>

          <div className="flex items-center text-sm text-muted-foreground mb-8">
            <Clock className="h-4 w-4 mr-2" />
            <span>Last updated: {lastUpdated}</span>
          </div>

          <div className="space-y-8 max-w-none">
            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">Overview</h2>
              <p>
                We want you to be satisfied with our services. This Refund Policy explains how we handle refunds for our subscription plans and services.
              </p>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">Subscription Refunds</h2>
              <p className="mb-4">
                For monthly and annual subscriptions:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Within the first 14 days of your initial subscription, you may request a full refund if you are not satisfied with our services.</li>
                <li>After the 14-day period, subscriptions are non-refundable for the current billing period.</li>
                <li>Cancelling your subscription will stop future billing but will not generate a refund for the current period.</li>
              </ul>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">How to Request a Refund</h2>
              <p className="mb-4">
                To request a refund:
              </p>
              <ol className="list-decimal pl-6 space-y-2">
                <li>Contact our support <NAME_EMAIL></li>
                <li>Include your account email and reason for the refund</li>
                <li>For eligible refunds, we will process the request within 5-7 business days</li>
              </ol>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">Exceptions</h2>
              <p className="mb-4">
                Refunds may not be available in the following cases:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Violations of our Terms of Service</li>
                <li>Abuse of our services or refund policy</li>
                <li>Account termination due to misconduct</li>
                <li>Special promotional or discounted subscriptions</li>
              </ul>
            </div>

            <div className="bg-card rounded-lg p-6 border">
              <h2 className="text-2xl font-bold mb-4 text-mustard">Contact Us</h2>
              <p className="mb-4">
                If you have any questions about our Refund Policy, please contact us at:
              </p>
              <p>
                <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium">
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>

          <div className="mt-12 flex justify-between items-center">
            <Button asChild variant="outline">
              <Link href="/terms">Terms of Service</Link>
            </Button>

            <Button asChild className="bg-mustard text-black hover:bg-mustard-dark">
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
