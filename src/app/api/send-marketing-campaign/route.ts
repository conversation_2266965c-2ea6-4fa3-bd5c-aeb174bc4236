import { NextResponse } from 'next/server'
import { z } from 'zod'
import { functions } from '@/utils/firebase'
import { httpsCallable } from 'firebase/functions'
import { auth } from '@/utils/firebase'
import { requireFeatureAccess, ApiResponse } from '@/utils/server-auth'

// Define validation schema for the request body
const campaignSchema = z.object({
  campaignName: z.string().min(1, 'Campaign name is required'),
  message: z.string().min(1, 'Message is required'),
  recipients: z.array(z.string()).min(1, 'At least one recipient is required'),
  spaceId: z.string().optional(),
  userId: z.string().min(1, 'User ID is required'), // Add userId for feature access check
})

export async function POST(request: Request) {
  try {
    // Check if user has access to marketing features
    await requireFeatureAccess(request, 'advanced_marketing');
    
    // Parse and validate the request body
    const body = await request.json()
    const validatedData = campaignSchema.parse(body)

    // Get the current space ID from the user's claims or use the one provided
    const spaceId = validatedData.spaceId || 'default-space-id'

    // Call the Firebase function
    const sendBulkSMSCampaign = httpsCallable(functions, 'sendBulkSMSCampaign')
    const result = await sendBulkSMSCampaign({
      recipients: validatedData.recipients,
      message: validatedData.message,
      campaignName: validatedData.campaignName,
      spaceId: spaceId,
    })

    // Return the result
    return ApiResponse.success(result.data)
  } catch (error) {
    console.error('Error sending marketing campaign:', error)
    
    if (error instanceof Error && error.message.includes('Access denied')) {
      return ApiResponse.forbidden(error.message);
    }
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return ApiResponse.badRequest('Validation error')
    }

    return ApiResponse.error('Failed to send marketing campaign')
  }
}
