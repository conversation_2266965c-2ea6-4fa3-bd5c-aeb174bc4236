import { NextResponse } from 'next/server';
import { spaceService, serviceService, staffService, appointmentService, activeServiceService } from '@/services/firestore';
import { auth } from '@/utils/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/utils/firebase';

export async function DELETE(
  _request: Request,
  { params }: { params: { spaceId: string } }
) {
  try {
    // Get the current user
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the space ID from the URL params
    const { spaceId } = params;
    if (!spaceId) {
      return NextResponse.json(
        { error: 'Space ID is required' },
        { status: 400 }
      );
    }

    // Get the space to verify it exists and the user has access
    const space = await spaceService.getById(spaceId);
    if (!space) {
      return NextResponse.json(
        { error: 'Space not found' },
        { status: 404 }
      );
    }

    // Verify the user has permission to delete this space
    if (space.ownerId !== currentUser.uid) {
      return NextResponse.json(
        { error: 'You do not have permission to delete this space' },
        { status: 403 }
      );
    }

    // Delete all related data in the following order:
    // 1. Active services
    // 2. Appointments
    // 3. Services
    // 4. Staff
    // 5. Space

    // 1. Delete active services for this space
    const activeServicesQuery = query(
      collection(db, 'active-services'),
      where('spaceId', '==', spaceId)
    );
    const activeServicesSnapshot = await getDocs(activeServicesQuery);
    const activeServiceDeletePromises = activeServicesSnapshot.docs.map(doc => 
      activeServiceService.delete(doc.id)
    );
    await Promise.all(activeServiceDeletePromises);

    // 2. Delete appointments for this space
    const appointmentsQuery = query(
      collection(db, 'appointments'),
      where('spaceId', '==', spaceId)
    );
    const appointmentsSnapshot = await getDocs(appointmentsQuery);
    const appointmentDeletePromises = appointmentsSnapshot.docs.map(doc => 
      appointmentService.delete(doc.id)
    );
    await Promise.all(appointmentDeletePromises);

    // 3. Delete services for this space
    const servicesQuery = query(
      collection(db, 'services'),
      where('spaceId', '==', spaceId)
    );
    const servicesSnapshot = await getDocs(servicesQuery);
    const serviceDeletePromises = servicesSnapshot.docs.map(doc => 
      serviceService.delete(doc.id)
    );
    await Promise.all(serviceDeletePromises);

    // 4. Delete staff for this space
    const staffQuery = query(
      collection(db, 'staff'),
      where('spaceId', '==', spaceId)
    );
    const staffSnapshot = await getDocs(staffQuery);
    const staffDeletePromises = staffSnapshot.docs.map(doc => 
      staffService.delete(doc.id)
    );
    await Promise.all(staffDeletePromises);

    // 5. Finally, delete the space itself
    await spaceService.delete(spaceId);

    return NextResponse.json(
      {
        success: true,
        message: 'Space and all associated data deleted successfully',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting space:', error);
    return NextResponse.json(
      { error: 'Failed to delete space' },
      { status: 500 }
    );
  }
}

export async function GET(
  _request: Request,
  { params }: { params: { spaceId: string } }
) {
  try {
    // Get the space ID from the URL params
    const { spaceId } = params;
    if (!spaceId) {
      return NextResponse.json(
        { error: 'Space ID is required' },
        { status: 400 }
      );
    }

    // Get the space
    const space = await spaceService.getById(spaceId);
    if (!space) {
      return NextResponse.json(
        { error: 'Space not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        space,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error retrieving space:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve space' },
      { status: 500 }
    );
  }
}
