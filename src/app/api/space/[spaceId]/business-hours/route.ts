import { NextResponse } from 'next/server';
import { z } from 'zod';
import { spaceService } from '@/services/firestore';
import { auth } from '@/utils/firebase';

// Define validation schema for business hours
const businessHourSchema = z.object({
  day: z.string(),
  open: z.string(),
  close: z.string(),
  enabled: z.boolean(),
});

const businessHoursSchema = z.object({
  businessHours: z.array(businessHourSchema),
});

export async function POST(
  request: Request,
  { params }: { params: { salonId: string } }
) {
  try {
    // Get the current user
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the salon ID from the URL params
    const { salonId } = params;
    if (!salonId) {
      return NextResponse.json(
        { error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Get the salon to verify it exists and the user has access
    const space = await spaceService.getById(salonId);
    if (!space) {
      return NextResponse.json(
        { error: 'Space not found' },
        { status: 404 }
      );
    }

    // Verify the user has permission to update this salon
    if (space.ownerId !== currentUser.uid) {
      return NextResponse.json(
        { error: 'You do not have permission to update this salon' },
        { status: 403 }
      );
    }

    // Parse and validate the request body
    const body = await request.json();
    const validatedData = businessHoursSchema.parse(body);

    // Update the salon with the new business hours
    await spaceService.update(salonId, {
      businessHours: validatedData.businessHours,
    });

    return NextResponse.json(
      {
        success: true,
        message: 'Business hours updated successfully',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating business hours:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update business hours' },
      { status: 500 }
    );
  }
}

export async function GET(
  _request: Request,
  { params }: { params: { spaceId: string } }
) {
  try {
    // Get the salon ID from the URL params
    const { spaceId } = params;
    if (!spaceId) {
      return NextResponse.json(
        { error: 'Space ID is required' },
        { status: 400 }
      );
    }

    // Get the salon to retrieve its business hours
    const salon = await spaceService.getById(spaceId);
    if (!salon) {
      return NextResponse.json(
        { error: 'Salon not found' },
        { status: 404 }
      );
    }

    // Return the business hours or default ones if not set
    const businessHours = salon.businessHours || [
      { day: 'Monday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Tuesday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Wednesday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Thursday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Friday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Saturday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Sunday', open: '09:00', close: '18:00', enabled: false },
    ];

    return NextResponse.json(
      {
        success: true,
        businessHours,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error retrieving business hours:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve business hours' },
      { status: 500 }
    );
  }
}
