import { NextResponse } from 'next/server'
import { z } from 'zod'
import { appointmentService, serviceService, staffService, spaceService } from '@/services/firestore'
import { appointmentValidationService } from '@/services/appointmentValidation'
import { formatToE164 } from '@/utils/phoneUtils'


// Define validation schema for the request body
const publicAppointmentSchema = z.object({
  customerName: z.string().min(2, 'Name must be at least 2 characters'),
  customerEmail: z.string().email('Please enter a valid email address'),
  customerPhone: z.string()
    .refine(
      (value) => {
        // Try to format to E.164 and check if valid
        const formatted = formatToE164(value);
        return formatted !== null;
      },
      {
        message: "Please enter a valid phone number (e.g., +254712345678 or 0712345678).",
      }
    ),
  serviceId: z.string().min(1, 'Service is required'),
  staffId: z.string().min(1, 'Staff member is required'),
  spaceId: z.string().min(1, 'Space ID is required'),
  startTime: z.string().min(1, 'Appointment time is required'),
  notes: z.string().optional(),
})

export async function POST(request: Request) {
  try {
    // Parse and validate the request body
    const body = await request.json()
    const validatedData = publicAppointmentSchema.parse(body)
    
    // Convert startTime to Date
    const startTime = new Date(validatedData.startTime)
    
    // Fetch required data
    const [staff, service, staffAppointments, space] = await Promise.all([
      staffService.getById(validatedData.staffId),
      serviceService.getById(validatedData.serviceId),
      appointmentService.getStaffAppointments(validatedData.staffId, startTime),
      spaceService.getById(validatedData.spaceId)
    ])
    
    if (!staff || !service || !spaceService || !space) {
      return NextResponse.json(
        { error: 'Staff, service, or space not found' },
        { status: 404 }
      )
    }
    
    // Get business hours from space if available
    const defaultBusinessHours = [
      { day: 'Monday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Tuesday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Wednesday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Thursday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Friday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Saturday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Sunday', open: '09:00', close: '18:00', enabled: false },
    ]
    
    const businessHours = Array.isArray(space.businessHours) ? space.businessHours : defaultBusinessHours
    
    // Validate appointment
    const validation = await appointmentValidationService.validateAppointment(
      startTime,
      validatedData.serviceId,
      validatedData.staffId,
      staff,
      {
        staffId: validatedData.staffId,
        appointments: staffAppointments.map(apt => ({
          startTime: new Date(apt.startTime),
          endTime: new Date(apt.endTime)
        }))
      },
      service,
      businessHours
    )
    
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error || 'Invalid appointment' },
        { status: 400 }
      )
    }
    
    // Calculate end time
    const endTime = new Date(startTime.getTime() + service.duration * 60000)
    
    // Create a temporary customer ID
    // In a real implementation, you might want to create a real customer record
    const customerId = 'guest-customer'
    
    // Create the appointment
    const appointment = await appointmentService.create({
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      serviceId: validatedData.serviceId,
      staffId: validatedData.staffId,
      customerId: customerId,
      spaceId: validatedData.spaceId,
      status: 'scheduled',
      notes: validatedData.notes || `Guest booking by ${validatedData.customerName} (${validatedData.customerEmail}, ${validatedData.customerPhone})`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'Appointment created successfully',
        appointment
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating public appointment:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create appointment' },
      { status: 500 }
    )
  }
}
