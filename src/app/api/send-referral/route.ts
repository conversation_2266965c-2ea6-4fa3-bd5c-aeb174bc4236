import { NextResponse } from 'next/server';
import { notificationService } from '@/services/notificationService';
import { requireFeatureAccess, ApiResponse } from '@/utils/server-auth';

export async function POST(request: Request) {
  try {
    // Check if user has access to referral system
    await requireFeatureAccess(request, 'advanced_referral_system');
    
    const { email, message, senderName, referralCode, referralUrl } = await request.json();
    
    if (!email || !referralCode || !senderName) {
      return ApiResponse.badRequest('Missing required fields');
    }
    
    // Send the referral email
    await notificationService.sendReferralInvitation({
      recipientEmail: email,
      senderName,
      referralCode,
      referralUrl,
      customMessage: message
    });
    
    return ApiResponse.success({ 
      success: true, 
      message: 'Referral invitation sent successfully' 
    });
  } catch (error) {
    console.error('Error sending referral invitation:', error);
    
    if (error instanceof Error && error.message.includes('Access denied')) {
      return ApiResponse.forbidden(error.message);
    }
    
    return ApiResponse.error('Failed to send referral invitation');
  }
}
