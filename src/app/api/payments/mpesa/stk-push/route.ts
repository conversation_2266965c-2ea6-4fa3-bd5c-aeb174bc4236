import { NextResponse } from 'next/server'
import { stkPushSchema } from './schema'

export async function GET() {
  return NextResponse.json(
    { message: 'M-PESA STK Push API endpoint' },
    { status: 200 }
  )
}

export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json()
    const validatedData = stkPushSchema.parse(body)

    // TODO
    // This is where you would integrate with your payment service
    // For example, using a service like Flutterwave, Pesapal, etc.
    // They will provide the actual implementation for initiating the STK push

    // Placeholder response - replace with actual payment service integration
    const response = {
      transactionId: Math.random().toString(36).substring(2, 11),
      transactionCode: `PYT${Date.now()}`,
      status: 'pending',
      message: 'STK push initiated successfully',
      amount: validatedData.amount,
      phoneNumber: validatedData.phoneNumber,
      invoiceId: validatedData.invoiceId
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error processing STK push:', error)
    return NextResponse.json(
      { error: 'Failed to process payment' },
      { status: 500 }
    )
  }
}