import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/utils/email';
import { z } from 'zod';

// Create a schema to validate the request body
const emailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1),
  text: z.string().optional(),
  html: z.string(),
  from: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate the request body
    const body = await request.json();
    const result = emailSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: result.error.format() 
        }, 
        { status: 400 }
      );
    }
    
    // Send the email
    const emailData = {
      ...result.data,
      text: result.data.text || result.data.html // Use html content as fallback for text
    };
    const success = await sendEmail(emailData);
    
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in email API route:', error);
    return NextResponse.json(
      { success: false, error: 'Server error' },
      { status: 500 }
    );
  }
}
