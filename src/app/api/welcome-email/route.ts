import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/utils/email';
import { render } from '@react-email/render';
import WelcomeEmail from '@/emails/WelcomeEmail';
import { z } from 'zod';

// Create a schema to validate the request body
const welcomeEmailSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
  dashboardUrl: z.string().url().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate the request body
    const body = await request.json();
    const result = welcomeEmailSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: result.error.format() 
        }, 
        { status: 400 }
      );
    }
    
    const { email, name, dashboardUrl } = result.data;

    // Generate welcome email HTML content using React Email
    const htmlContent = await render(WelcomeEmail({ 
      userName: name || '', 
      dashboardUrl: dashboardUrl || 'https://groombook.me/dashboard' 
    }));

    // Send the welcome email
    const success = await sendEmail({
      to: email,
      subject: 'Welcome to Your GroomBook Journey!',
      html: htmlContent,
      text: `Welcome to Your GroomBook Journey!

Hello${name ? ` ${name}` : ''},

We're thrilled to welcome you to the GroomBook family! You've just taken the first step toward transforming how you manage your salon or service business. Your success is our priority, and we're here to support you every step of the way.

WHY THOUSANDS CHOOSE GROOMBOOK:
- Save 5+ hours weekly on administration and booking management
- Reduce no-shows by 80% with automated reminders and confirmations
- Increase client satisfaction with 24/7 online booking availability
- Gain valuable insights into your business performance with detailed analytics

YOUR QUICK-START GUIDE:
1. Set Up Your Space Profile: Customize your business details, operating hours, and upload photos that showcase your space.
2. Add Your Services & Staff: Create your service menu with prices and durations, then invite your team members to join.
3. Accept Your First Booking: Share your booking link with clients or embed it on your website and social media.

Get started now: ${dashboardUrl || 'https://groombook.me/dashboard'}

WE'RE HERE TO HELP:
- Email: <EMAIL>
- Help Center & Tutorials: https://help.groombook.me
- Schedule a Free Onboarding Call: https://groombook.me/onboarding

Warm regards,
Brian Mwangi
CEO & Founder, GroomBook

© ${new Date().getFullYear()} GroomBook. All rights reserved.`
    });
    
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send welcome email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in welcome email API route:', error);
    return NextResponse.json(
      { success: false, error: 'Server error' },
      { status: 500 }
    );
  }
}
