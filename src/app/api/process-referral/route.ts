import { NextResponse } from 'next/server';
import { customerService } from '@/services/firestore';

export async function POST(request: Request) {
  try {
    const { customerId, referralCode } = await request.json();
    
    if (!customerId || !referralCode) {
      return NextResponse.json(
        { error: 'Missing required fields' }, 
        { status: 400 }
      );
    }
    
    // Process the referral
    const result = await customerService.processReferral(customerId, referralCode);
    
    return NextResponse.json({
      success: true, 
      message: 'Referral processed successfully',
      referrerId: result.referrerId
    }, { status: 200 });
  } catch (error) {
    console.error('Error processing referral:', error);
    
    return NextResponse.json(
      { error: 'Failed to process referral' },
      { status: 500 }
    );
  }
}