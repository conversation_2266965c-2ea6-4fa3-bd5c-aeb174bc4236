import { NextRequest, NextResponse } from 'next/server';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export async function POST(
  request: NextRequest,
  { params }: { params: { spaceId: string } }
) {
  try {
    // Note: Authentication should be handled on the client side before calling this API
    // For now, we'll trust that the client has verified the user's permissions

    const { paymentModes } = await request.json();

    // Validate payment modes structure
    if (!paymentModes || typeof paymentModes !== 'object') {
      return NextResponse.json(
        { error: 'Invalid payment modes data' },
        { status: 400 }
      );
    }

    // Validate that at least one payment mode is enabled
    const { cash, card, mpesa } = paymentModes;
    if (!cash && !card && !mpesa) {
      return NextResponse.json(
        { error: 'At least one payment method must be enabled' },
        { status: 400 }
      );
    }

    const spaceId = params.spaceId;
    const spaceRef = doc(db, 'spaces', spaceId);

    // Update the space document with payment modes
    await updateDoc(spaceRef, {
      paymentModes: {
        cash: <PERSON><PERSON><PERSON>(cash),
        card: <PERSON><PERSON><PERSON>(card),
        mpesa: <PERSON><PERSON><PERSON>(mpesa),
      },
      updatedAt: new Date().toISOString(),
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Payment modes updated successfully' 
    });
  } catch (error) {
    console.error('Error updating payment modes:', error);
    return NextResponse.json(
      { error: 'Failed to update payment modes' },
      { status: 500 }
    );
  }
}
