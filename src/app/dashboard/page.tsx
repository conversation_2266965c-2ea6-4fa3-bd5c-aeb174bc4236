'use client';

import { useState, useEffect } from "react"
import { useAppSelector } from '@/store/hooks';
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  ArrowRight,
  Users,
  Scissors,
  Clock,
  Store,
  QrCode,
  Calendar
} from "lucide-react"
import { format, parseISO } from "date-fns"
import Link from "next/link"
import { CreateSpaceDialog } from "@/components/CreateSpaceDialog"
import { BookingQRCode } from "@/components/space/BookingQRCode"
import { RevenueChart } from "@/components/RevenueChart"
import { WelcomeBanner } from "@/components/WelcomeBanner"
import {
  customerService,
  serviceService,
  staffService,
  activeServiceService,
  appointmentService
} from "@/services/firestore"
import {
  Customer,
  Service,
  Staff,
  ActiveService
} from "@/services/types/models"
import { formatPrice } from "@/utils/price-formatter"
import { AppointmentWithDetails } from "@/services/appointmentService";

export default function DashboardPage() {
  // All state hooks defined at the top level
  const { user, loading: authLoading } = useAppSelector((state) => state.auth);
  const { currentSpace, spaces, loading: spaceLoading } = useAppSelector((state) => state.space);
  const [isLoading, setIsLoading] = useState(true);
  const [recentCustomers, setRecentCustomers] = useState<Customer[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  // Add activeServices to state
  const [activeServices, setActiveServices] = useState<ActiveService[]>([]);
  // Use our custom hook to detect if we're coming from onboarding/space creation

  
  const [appointments, setAppointments] = useState<AppointmentWithDetails[]>([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const isLoadingDashboard = authLoading || spaceLoading;

  const formatServicePrice = (price: number) => {
    return formatPrice(price, currentSpace);
  };

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);

        const today = new Date();
        const startOfDay = new Date(today.setHours(0, 0, 0, 0));
        const endOfDay = new Date(today.setHours(23, 59, 59, 999));

        const [customersData, servicesData, staffData, appointmentsData, activeServicesData] = await Promise.all([
          currentSpace ? customerService.getBySpace(currentSpace.id) : [],
          currentSpace ? serviceService.getBySpace(currentSpace.id) : [],
          currentSpace ? staffService.getBySpace(currentSpace.id) : [],
          currentSpace ? appointmentService.getByDateRange(currentSpace.id, startOfDay, endOfDay) : [],
          currentSpace ? activeServiceService.getInProgressBySpace(currentSpace.id) : []
        ]);

        // Sort appointments and active services
        const sortedAppointments = appointmentsData.sort((a, b) =>
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        const sortedActiveServices = activeServicesData.sort((a, b) =>
          new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
        );

        setRecentCustomers(customersData.slice(0, 5));
        setServices(servicesData);
        setStaff(staffData);
        setAppointments(sortedAppointments);
        setActiveServices(sortedActiveServices);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (currentSpace) {
      loadDashboardData();
    }
  }, [currentSpace]);

  // Loading state
  if (isLoadingDashboard) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Render create shop dialog for all cases
  const createSpaceDialog = (
    <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
      <DialogContent className="sm:max-w-[500px] bg-white p-0 gap-0 overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-4 border-b">
          <DialogTitle>Create New Shop</DialogTitle>
          <DialogDescription>
            Fill in the details below to create your shop.
          </DialogDescription>
        </DialogHeader>
        <CreateSpaceDialog onClose={() => setCreateDialogOpen(false)} />
      </DialogContent>
    </Dialog>
  );

  // If no shop is selected and user has shops, show a message
  if (!currentSpace && spaces && spaces.length > 0) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <div className="bg-white p-8 rounded-xl shadow-sm border text-center max-w-md">
          <div className="bg-[#FFF8E0] p-4 rounded-full inline-flex mb-6">
            <Store className="h-12 w-12 text-[#F5B800]" />
          </div>
          <h2 className="text-2xl font-semibold mb-3">Select a Shop</h2>
          <p className="text-gray-500 mb-6">
            Please select a shop from the dropdown in the sidebar to view your dashboard and manage your business.
          </p>
          <div className="flex items-center justify-center text-[#F5B800]">
            <svg className="animate-bounce w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
            <span className="text-sm font-medium">Use the shop selector above</span>
          </div>
        </div>
      </div>
    );
  }

  // If user has no shops, show create shop message with dialog
  if (!spaces || spaces.length === 0) {
    return (
      <>
        {createSpaceDialog}
        <div className="flex h-full items-center justify-center p-4">
          <div className="bg-white p-8 rounded-xl shadow-sm border text-center max-w-md">
            <div className="bg-[#FFF8E0] p-4 rounded-full inline-flex mb-6">
              <Store className="h-12 w-12 text-[#F5B800]" />
            </div>
            <h2 className="text-2xl font-semibold mb-3">No Shops Found</h2>
            <p className="text-gray-500 mb-6">
              You don&apos;t have any shops yet. Create your first shop to start managing your business.
            </p>
            <Button
              onClick={() => setCreateDialogOpen(true)}
              className="bg-[#F5B800] hover:bg-[#E5AB00] text-black"
            >
              <Store className="h-4 w-4 mr-2" />
              Create Shop
            </Button>
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="flex-1 space-y-4 sm:space-y-6 p-4 sm:p-8 pt-4 sm:pt-6">
      {/* Welcome Banner for new users */}
      <WelcomeBanner />
      
      <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            Overview of your business performance and activities
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recentCustomers.length}</div>
            <p className="text-xs text-muted-foreground">
              Active customer profiles
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Services Offered</CardTitle>
            <Scissors className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{services.length}</div>
            <p className="text-xs text-muted-foreground">
              Available services
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Staff Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{staff.length}</div>
            <p className="text-xs text-muted-foreground">
              Active staff members
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Services</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeServices.length}</div>
            <p className="text-xs text-muted-foreground">
              Services in progress
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-7">
        {/* Revenue Chart */}
        <Card className="lg:col-span-7">
          <CardHeader>
            <CardTitle>Revenue Overview</CardTitle>
            <CardDescription>
              Your revenue trends and target performance
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            {currentSpace ? (
              <RevenueChart spaceId={currentSpace.id}  />
            ) : (
              <div className="h-[400px] flex items-center justify-center text-muted-foreground">
                Select a space to view revenue data
              </div>
            )}
          </CardContent>
        </Card>

        {/* Appointments and Services Section */}
        <div className="grid gap-4 lg:col-span-7 grid-cols-1 lg:grid-cols-7">
          {/* Recent Appointments */}
          <Card className="lg:col-span-4">
            <CardHeader>
              <CardTitle>Recent Appointments</CardTitle>
              <CardDescription>
                Your upcoming appointments for today
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <div className="h-16 rounded-lg bg-slate-100 animate-pulse" />
                  <div className="h-16 rounded-lg bg-slate-100 animate-pulse" />
                  <div className="h-16 rounded-lg bg-slate-100 animate-pulse" />
                </div>
              ) : appointments.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <div className="bg-[#FFF8E0] p-4 rounded-full">
                    <Calendar className="h-8 w-8 text-[#F5B800]" />
                  </div>
                  <div className="mt-4 space-y-1">
                    <h3 className="font-medium text-lg">No active appointments</h3>
                    <p className="text-muted-foreground text-sm">You have no appointments scheduled for today.</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {appointments.map(service => (
                    <div
                      key={service.id}
                      className="flex items-center justify-between border rounded-lg p-3"
                    >
                      <div className="flex items-center gap-3">
                        <div className="bg-[#FFF8E0] p-2 rounded-full">
                          <Clock className="h-4 w-4 text-[#F5B800]" />
                        </div>
                        <div>
                          <p className="font-medium">
                            {service.services[0]?.name || 'Unnamed Service'}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {format(service.startTime, 'h:mm a')}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-[#FFF8E0] text-[#F5B800] border-0">
                        {service.status}
                      </Badge>
                    </div>
                  ))}
                  <div className="mt-4 text-center">
                    <Link href="/dashboard/appointments" passHref>
                      <Button className="bg-[#F5B800] hover:bg-[#E5AB00] text-black">
                        View All Appointments
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Popular Services */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle>Popular Services</CardTitle>
              <CardDescription>
                Your most booked services
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <div className="h-16 rounded-lg bg-slate-100 animate-pulse" />
                  <div className="h-16 rounded-lg bg-slate-100 animate-pulse" />
                </div>
              ) : services.length === 0 ? (
                <div className="text-center py-8">
                  <div className="bg-[#FFF8E0] p-4 rounded-full inline-flex">
                    <Scissors className="h-8 w-8 text-[#F5B800]" />
                  </div>
                  <div className="mt-4">
                    <h3 className="font-medium text-lg">No services yet</h3>
                    <p className="text-muted-foreground text-sm">Add some services to get started.</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {services.slice(0, 5).map(service => (
                    <div key={service.id} className="flex items-center justify-between p-2">
                      <div className="flex items-center gap-3">
                        <div>
                          <p className="font-medium">{service.name}</p>
                          <p className="text-sm text-muted-foreground">{service.duration} min</p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-[#FFF8E0] text-[#F5B800] border-0">
                        {formatServicePrice(service.price)}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Direct Booking Card */}
      {currentSpace && (
        <Card className="bg-gradient-to-r from-yellow-50 to-red-50 border-yellow-100">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Direct Booking Link</CardTitle>
                <CardDescription>
                  Let customers book appointments directly with a QR code or link
                </CardDescription>
              </div>
              <div className="bg-white p-2 rounded-full">
                <Calendar className="h-6 w-6 text-red-500" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="max-w-md">
                <p className="text-sm mb-4">
                  Share this booking link with your customers to allow them to book appointments directly without calling.
                  You can print the QR code and display it in your space.
                </p>
                <BookingQRCode
                  spaceId={currentSpace.id}
                  spaceName={currentSpace.name}
                />
              </div>
              <div className="hidden md:block bg-white p-4 rounded-lg border">
                <QrCode className="h-24 w-24 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}