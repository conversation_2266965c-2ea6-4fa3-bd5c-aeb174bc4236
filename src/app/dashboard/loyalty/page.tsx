'use client'

import { useState, useEffect } from 'react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Ta<PERSON>, 
  <PERSON>bs<PERSON>ontent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { useAppSelector } from '@/store/hooks'
import { formatDistance, parseISO } from 'date-fns'
import { 
  ArrowDown, 
  ArrowUp, 
  Award, 
  Calendar, 
  Clock, 
  Coins, 
  Gift, 
  PlusCircle, 
  Search, 
  Trophy, 
  User 
} from 'lucide-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { loyaltyService, LoyaltyReward, LoyaltyTransaction } from '@/services/loyaltyService'
import { FeatureGate } from '@/components/FeatureGate'
import { RestrictedFeature } from '@/hooks/use-feature-access'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { customerService } from '@/services/firestore'

const rewardFormSchema = z.object({
  name: z.string().min(2, {
    message: 'Name must be at least 2 characters.',
  }),
  description: z.string().min(5, {
    message: 'Description must be at least 5 characters.',
  }),
  pointsCost: z.coerce.number().positive({
    message: 'Points cost must be a positive number.',
  }),
  type: z.enum(['discount', 'free_service', 'product']),
  value: z.coerce.number().nonnegative({
    message: 'Value must be a non-negative number.',
  }),
  isActive: z.boolean(),
})

type RewardFormValues = z.infer<typeof rewardFormSchema>

function LoyaltyDashboardContent() {
  const [activeTab, setActiveTab] = useState('overview')
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateRewardOpen, setIsCreateRewardOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [rewards, setRewards] = useState<LoyaltyReward[]>([])
  const [transactions, setTransactions] = useState<LoyaltyTransaction[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const { toast } = useToast()
  const { currentSpace } = useAppSelector((state) => state.space)

  // Form for creating rewards
  const rewardForm = useForm<RewardFormValues>({
    resolver: zodResolver(rewardFormSchema),
    defaultValues: {
      name: '',
      description: '',
      pointsCost: 100,
      type: 'discount',
      value: 10,
      isActive: true,
    },
  })

  useEffect(() => {
    const loadData = async () => {
      if (!currentSpace?.id) return
      
      setIsLoading(true)
      try {
        const [customersData, rewardsData, transactionsData] = await Promise.all([
          customerService.getAll(),
          loyaltyService.getAllRewards(),
          loyaltyService.getAllTransactions(currentSpace.id)
        ])
        
        setCustomers(customersData)
        setRewards(rewardsData)
        setTransactions(transactionsData)
      } catch (error) {
        console.error('Error loading loyalty data:', error)
        toast({
          title: 'Error',
          description: 'Failed to load loyalty data',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }
    
    // Initial load
    loadData()

    // Set up periodic refresh every 30 seconds
    const refreshInterval = setInterval(loadData, 30000)

    // Cleanup function
    return () => {
      clearInterval(refreshInterval)
    }
  }, [toast, currentSpace?.id])

  // Calculate summary statistics
  const totalLoyaltyPoints = customers.reduce((total, customer) => {
    return total + (customer.loyaltyPoints?.balance || 0)
  }, 0)
  
  const customersInLoyaltyProgram = customers.filter(
    customer => customer.loyaltyPoints?.balance !== undefined
  ).length
  
  const tierCounts = customers.reduce((counts, customer) => {
    const tier = customer.loyaltyPoints?.tier || 'none'
    counts[tier] = (counts[tier] || 0) + 1
    return counts
  }, {} as Record<string, number>)
  
  const topCustomers = [...customers]
    .filter(customer => customer.loyaltyPoints?.balance)
    .sort((a, b) => (b.loyaltyPoints?.balance || 0) - (a.loyaltyPoints?.balance || 0))
    .slice(0, 5)
  
  const filteredCustomers = customers.filter(customer => 
    customer.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phoneNumber.includes(searchTerm) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreateReward = async (data: RewardFormValues) => {
    setIsSubmitting(true)
    
    try {
      await loyaltyService.createReward({
        ...data,
        metadata: {
          salonId: currentSpace?.id || '',
          createdAt: new Date().toISOString(),
        },
      })
      
      setIsCreateRewardOpen(false)
      rewardForm.reset()
      toast({
        title: 'Success',
        description: 'Reward created successfully',
      })

    } catch (error) {
      console.error('Error creating reward:', error)
      toast({
        title: 'Error',
        description: 'Failed to create reward',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const getRewardTypeLabel = (type: string) => {
    switch (type) {
      case 'discount': return 'Discount'
      case 'free_service': return 'Free Service'
      case 'product': return 'Product'
      default: return type
    }
  }
  
  const formatRewardValue = (type: string, value: number) => {
    switch (type) {
      case 'discount': return `${value}% off`
      case 'free_service': return 'Free service'
      case 'product': return 'Free product'
      default: return `${value}`
    }
  }
  
  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-8">
      <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Loyalty Program</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Manage your salon's loyalty program and rewards
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="w-full overflow-x-auto items-center justify-start">
          <TabsTrigger value="overview" className="whitespace-nowrap flex-1 sm:flex-none">Overview</TabsTrigger>
          <TabsTrigger value="customers" className="whitespace-nowrap flex-1 sm:flex-none">Customers</TabsTrigger>
          <TabsTrigger value="rewards" className="whitespace-nowrap">Rewards</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Top row stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Loyalty Points
                </CardTitle>
                <Coins className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalLoyaltyPoints.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Points across all customers
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Program Members
                </CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{customersInLoyaltyProgram}</div>
                <p className="text-xs text-muted-foreground">
                  Customers enrolled in loyalty program
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Rewards
                </CardTitle>
                <Gift className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {rewards.filter(r => r.isActive).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Available reward options
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Top Tier Members
                </CardTitle>
                <Trophy className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {tierCounts['gold'] || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Gold tier customers
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Bento Grid Layout */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-3">
            {/* Left Column - Top Customers */}
            <Card className="lg:row-span-2">
              <CardHeader>
                <CardTitle>Top Customers</CardTitle>
                <CardDescription>
                  Your most loyal customers
                </CardDescription>
              </CardHeader>
              <CardContent>
                {topCustomers.length > 0 ? (
                  <div className="space-y-4">
                    {topCustomers.map(customer => (
                      <div key={customer.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div>
                            <p className="font-medium">{customer.displayName}</p>
                            <p className="text-xs text-muted-foreground">
                              {customer.loyaltyPoints?.tier ? 
                                `${customer.loyaltyPoints.tier.charAt(0).toUpperCase() + customer.loyaltyPoints.tier.slice(1)} tier` 
                                : 'No tier'
                              }
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Coins className="h-3 w-3" />
                          {customer.loyaltyPoints?.balance || 0}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-6 text-center">
                    <User className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No customers enrolled in loyalty program</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Middle Column - Recent Transactions */}
            <Card className="lg:row-span-2">
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>
                  Latest loyalty point activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                {transactions.length > 0 ? (
                  <div className="space-y-4 max-h-[400px] overflow-auto">
                    {transactions.map((transaction) => {
                      const customer = customers.find((c: any) => c.id === transaction.customerId)
                      
                      return (
                        <div key={transaction.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className={`rounded-full p-2 ${
                              transaction.type === 'earn' ? 'bg-green-100' : 'bg-orange-100'
                            }`}>
                              {transaction.type === 'earn' ? (
                                <ArrowUp className="h-3 w-3 text-green-700" />
                              ) : (
                                <ArrowDown className="h-3 w-3 text-orange-700" />
                              )}
                            </div>
                            <div>
                              <p className="text-sm font-medium">
                                {customer?.displayName || 'Unknown Customer'}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {transaction.description}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatDistance(parseISO(transaction.createdAt), new Date(), { addSuffix: true })}
                              </p>
                            </div>
                          </div>
                          <span className={`font-medium ${
                            transaction.type === 'earn' ? 'text-green-700' : 'text-orange-700'
                          }`}>
                            {transaction.type === 'earn' ? '+' : '-'}{Math.abs(transaction.points)}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-6 text-center">
                    <Clock className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No recent transactions</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Right Column - Tier Distribution */}
            <Card className="lg:row-span-2">
              <CardHeader>
                <CardTitle>Loyalty Program Tiers</CardTitle>
                <CardDescription>
                  Customer distribution across loyalty tiers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['bronze', 'silver', 'gold', 'platinum'].map((tier) => (
                    <div key={tier} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className={`bg-${
                              tier === 'bronze' ? 'amber-700' :
                              tier === 'silver' ? 'gray-400' :
                              tier === 'gold' ? 'yellow-500' :
                              'indigo-500'
                            } text-white`}
                          >
                            {tier.charAt(0).toUpperCase() + tier.slice(1)}
                          </Badge>
                          <span className="text-sm">{
                            tier === 'bronze' ? '0-499 points' :
                            tier === 'silver' ? '500-1,499 points' :
                            tier === 'gold' ? '1,500-4,999 points' :
                            '5,000+ points'
                          }</span>
                        </div>
                        <span className="font-medium">{tierCounts[tier] || 0} customers</span>
                      </div>
                      <Progress 
                        value={(tierCounts[tier] || 0) / customers.length * 100} 
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Customers Tab */}
        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle>Loyalty Program Customers</CardTitle>
                <div className="relative w-64">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search customers..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <CardDescription>
                Manage customers in your loyalty program
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : filteredCustomers.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <User className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No customers found</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Customer</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Tier</TableHead>
                      <TableHead>Last Updated</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCustomers.map((customer) => (
                      <TableRow key={customer.id}>
                        <TableCell className="font-medium">{customer.displayName}</TableCell>
                        <TableCell>
                          <div className="text-sm">{customer.phoneNumber}</div>
                          <div className="text-sm text-muted-foreground">{customer.email}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Coins className="h-3 w-3" />
                            {customer.loyaltyPoints?.balance || 0}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {customer.loyaltyPoints?.tier ? (
                            <Badge
                              className={
                                customer.loyaltyPoints.tier === 'bronze'
                                  ? 'bg-amber-700 text-white'
                                  : customer.loyaltyPoints.tier === 'silver'
                                  ? 'bg-gray-400 text-white'
                                  : customer.loyaltyPoints.tier === 'gold'
                                  ? 'bg-yellow-500 text-white'
                                  : 'bg-indigo-500 text-white'
                              }
                            >
                              {customer.loyaltyPoints.tier.charAt(0).toUpperCase() + 
                               customer.loyaltyPoints.tier.slice(1)}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">Not enrolled</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {customer.loyaltyPoints?.updatedAt ? (
                            <div className="text-sm">
                              {formatDistance(
                                parseISO(customer.loyaltyPoints.updatedAt),
                                new Date(),
                                { addSuffix: true }
                              )}
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Rewards Tab */}
        <TabsContent value="rewards" className="space-y-4">
          <div className="flex justify-end">
            <Dialog open={isCreateRewardOpen} onOpenChange={setIsCreateRewardOpen}>
              <DialogTrigger asChild>
                <Button>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create Reward
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Reward</DialogTitle>
                  <DialogDescription>
                    Create a new reward for your loyalty program.
                  </DialogDescription>
                </DialogHeader>
                
                <Form {...rewardForm}>
                  <form onSubmit={rewardForm.handleSubmit(handleCreateReward)} className="space-y-4">
                    <FormField
                      control={rewardForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Reward Name</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., 10% Off Next Service" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={rewardForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Describe what the customer gets with this reward" 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={rewardForm.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Type</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="discount">Discount</SelectItem>
                                <SelectItem value="free_service">Free Service</SelectItem>
                                <SelectItem value="product">Free Product</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={rewardForm.control}
                        name="value"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Value</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={rewardForm.control}
                      name="pointsCost"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Points Cost</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            How many points customers need to redeem this reward
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <DialogFooter>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                            Creating...
                          </>
                        ) : (
                          'Create Reward'
                        )}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Available Rewards</CardTitle>
              <CardDescription>
                Rewards customers can redeem with their loyalty points
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : rewards.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Gift className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No rewards yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Create rewards to engage your customers and build loyalty
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reward</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Points</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rewards.map((reward) => (
                      <TableRow key={reward.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{reward.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {reward.description}
                            </p>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          {getRewardTypeLabel(reward.type)}
                          <p className="text-sm text-muted-foreground">
                            {formatRewardValue(reward.type, reward.value)}
                          </p>
                        </TableCell>
                        
                        <TableCell>
                          <Badge variant="secondary">
                            {reward.pointsCost} points
                          </Badge>
                        </TableCell>
                        
                        <TableCell>
                          {reward.isActive ? (
                            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-muted-foreground">
                              Inactive
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function LoyaltyDashboard() {
  return (
    <FeatureGate
      feature={RestrictedFeature.LOYALTY_PROGRAM}
      title="Loyalty Program"
      description="Reward your customers and increase retention with our loyalty system"
    >
      <LoyaltyDashboardContent />
    </FeatureGate>
  )
}
