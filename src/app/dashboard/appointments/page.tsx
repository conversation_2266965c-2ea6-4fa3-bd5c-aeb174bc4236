"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Plus, Clock, CalendarDays, Mail, Phone, User, ChevronLeft, ChevronRight } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { format } from "date-fns"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import AppointmentCalendar from "@/components/appointments/AppointmentCalendar"
import { useEffect } from "react"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { fetchAppointmentsWithDetails, cancelAppointment, rescheduleAppointment, updateAppointmentStatus } from "@/store/slices/appointmentSlice"
import { AppointmentWithDetails } from "@/services/appointmentService"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { formatPrice } from "@/utils/price-formatter"
import { Label } from "@/components/ui/label"

export default function AppointmentsPage() {
  const dispatch = useAppDispatch()
  const { appointmentsWithDetails, loading, error } = useAppSelector((state) => state.appointments)
  const { currentSpace } = useAppSelector((state) => state.space)

  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentWithDetails | null>(null)
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false)
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState("")
  const [viewMode, setViewMode] = useState("table")
  const [rescheduledDate, setRescheduledDate] = useState<Date | null>(null)

  // Fetch appointments when component mounts
  useEffect(() => {
    if (currentSpace) {
      dispatch(fetchAppointmentsWithDetails(currentSpace.id))
    }
  }, [dispatch, currentSpace])

  const handleReschedule = () => {
    if (selectedAppointment && currentSpace && rescheduledDate) {
      dispatch(rescheduleAppointment({
        appointmentId: selectedAppointment.id,
        newStartTime: rescheduledDate,
        spaceId: currentSpace.id
      }))
    }
    setIsRescheduleDialogOpen(false)
  }

  const handleCancel = () => {
    if (selectedAppointment && currentSpace) {
      dispatch(cancelAppointment({
        appointmentId: selectedAppointment.id,
        spaceId: currentSpace.id
      }))
    }
    setIsCancelDialogOpen(false)
  }

  const handleMarkNoShow = () => {
    if (selectedAppointment && currentSpace) {
      dispatch(updateAppointmentStatus({
        appointmentId: selectedAppointment.id,
        status: 'no-show',
        spaceId: currentSpace.id
      }));
      setSelectedAppointment(null);
    }
  };

  const handleEventClick = (appointment: AppointmentWithDetails) => {
    setSelectedAppointment(appointment)
  }

  const handleEventDrop = (appointmentId: string, newStart: Date, newEnd: Date) => {
    if (currentSpace) {
      dispatch(rescheduleAppointment({
        appointmentId,
        newStartTime: newStart,
        spaceId: currentSpace.id
      }))
    }
  }

  // Calculate pagination values
  const filteredAppointments = appointmentsWithDetails.filter((appointment: AppointmentWithDetails) =>
    appointment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    appointment.service.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalPages = Math.ceil(filteredAppointments.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedAppointments = filteredAppointments.slice(startIndex, endIndex)

  // Helper function for price formatting
  const formatAppointmentPrice = (price: number) => {
    return formatPrice(price, currentSpace);
  };

  return (
    <>
      <div className="h-full flex-1 space-y-4 sm:space-y-8 p-4 sm:p-8">
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Appointments</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              View and manage your salon appointments
            </p>
          </div>
          <Button className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            New Appointment
          </Button>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search appointments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full sm:max-w-sm"
            />
          </div>
        </div>

        <Tabs defaultValue="table" className="w-full" onValueChange={setViewMode}>
          <TabsList className="w-full max-w-full overflow-x-auto flex items-center justify-start">
            <TabsTrigger value="table" className="whitespace-nowrap flex-1 sm:flex-none">Table View</TabsTrigger>
            <TabsTrigger value="calendar" className="whitespace-nowrap flex-1 sm:flex-none">Calendar View</TabsTrigger>
          </TabsList>

          <TabsContent value="table">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>All Appointments</CardTitle>
                <CardDescription>
                  View and manage upcoming appointments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative w-full overflow-auto">
                  {loading ? (
                    <div className="space-y-3">
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                    </div>
                  ) : error ? (
                    <div className="flex h-[400px] flex-col items-center justify-center space-y-3 text-center">
                      <div className="bg-red-100 p-4 rounded-full">
                        <Calendar className="h-8 w-8 text-red-500" />
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-medium text-lg">Error loading appointments</h3>
                        <p className="text-muted-foreground text-sm">{error}</p>
                      </div>
                      <Button
                        className="mt-4"
                        onClick={() => currentSpace && dispatch(fetchAppointmentsWithDetails(currentSpace.id))}
                      >
                        Try Again
                      </Button>
                    </div>
                  ) : appointmentsWithDetails.length === 0 ? (
                    <div className="flex h-[400px] flex-col items-center justify-center space-y-3 text-center">
                      <div className="bg-[#FFF8E0] p-4 rounded-full">
                        <Calendar className="h-8 w-8 text-[#F5B800]" />
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-medium text-lg">No appointments yet</h3>
                        <p className="text-muted-foreground text-sm">
                          {searchTerm
                            ? "Try a different search term."
                            : "Start by scheduling your first appointment."
                          }
                        </p>
                      </div>
                      {!searchTerm && (
                        <Button className="mt-4">
                          <Plus className="mr-2 h-4 w-4" />
                          New Appointment
                        </Button>
                      )}
                    </div>
                  ) : (
                    <>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-end pb-4 gap-2">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm text-muted-foreground">Rows per page</p>
                          <Select
                            value={pageSize.toString()}
                            onValueChange={(value) => {
                              setPageSize(Number(value))
                              setCurrentPage(1)
                            }}
                          >
                            <SelectTrigger className="h-8 w-[70px]">
                              <SelectValue placeholder={pageSize} />
                            </SelectTrigger>
                            <SelectContent side="top">
                              {[10, 20, 30, 40, 50].map((size) => (
                                <SelectItem key={size} value={size.toString()}>
                                  {size}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="hidden sm:block">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="w-[200px]">Customer</TableHead>
                              <TableHead className="w-[150px]">Service</TableHead>
                              <TableHead className="w-[150px]">Date</TableHead>
                              <TableHead className="w-[100px]">Time</TableHead>
                              <TableHead className="w-[100px]">Status</TableHead>
                              <TableHead className="w-[100px] text-right">Price</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {paginatedAppointments.map((appointment: AppointmentWithDetails) => (
                              <TableRow
                                key={appointment.id}
                                className="cursor-pointer hover:bg-muted/50"
                                onClick={() => setSelectedAppointment(appointment)}
                              >
                                <TableCell>{appointment.customerName}</TableCell>
                                <TableCell>{appointment.service}</TableCell>
                                <TableCell>{format(appointment.date, 'MMM d, yyyy')}</TableCell>
                                <TableCell>{appointment.time}</TableCell>
                                <TableCell>
                                  <span
                                    className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                      appointment.status === "completed"
                                        ? "bg-green-50 text-green-700"
                                        : appointment.status === "scheduled"
                                        ? "bg-yellow-50 text-yellow-700"
                                        : appointment.status === "cancelled"
                                        ? "bg-red-50 text-red-700"
                                        : appointment.status === "no-show"
                                        ? "bg-gray-50 text-gray-700"
                                        : "bg-blue-50 text-blue-700"
                                    }`}
                                  >
                                    {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                                  </span>
                                </TableCell>
                                <TableCell className="text-right">
                                  {formatAppointmentPrice(appointment.price)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                      
                      {/* Mobile Cards View */}
                      <div className="sm:hidden space-y-4">
                        {paginatedAppointments.map((appointment: AppointmentWithDetails) => (
                          <Card 
                            key={appointment.id}
                            className="cursor-pointer hover:bg-muted/50"
                            onClick={() => setSelectedAppointment(appointment)}
                          >
                            <CardContent className="p-4">
                              <div className="flex flex-col space-y-2">
                                <div className="flex items-center justify-between">
                                  <h3 className="font-semibold">{appointment.customerName}</h3>
                                  <span
                                    className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                      appointment.status === "completed"
                                        ? "bg-green-50 text-green-700"
                                        : appointment.status === "scheduled"
                                        ? "bg-yellow-50 text-yellow-700"
                                        : appointment.status === "cancelled"
                                        ? "bg-red-50 text-red-700"
                                        : appointment.status === "no-show"
                                        ? "bg-gray-50 text-gray-700"
                                        : "bg-blue-50 text-blue-700"
                                    }`}
                                  >
                                    {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                                  </span>
                                </div>
                                <p className="text-sm text-muted-foreground">{appointment.service}</p>
                                <div className="flex items-center justify-between text-sm">
                                  <span>{format(appointment.date, 'MMM d, yyyy')} at {appointment.time}</span>
                                  <span className="font-medium">{formatAppointmentPrice(appointment.price)}</span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                      <div className="flex flex-col sm:flex-row items-center justify-between py-4 gap-2">
                        <p className="text-sm text-muted-foreground text-center sm:text-left">
                          Showing {startIndex + 1} to {Math.min(endIndex, filteredAppointments.length)} of{" "}
                          {filteredAppointments.length} entries
                        </p>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(currentPage - 1)}
                            disabled={currentPage === 1}
                            className="flex items-center"
                          >
                            <ChevronLeft className="h-4 w-4" />
                            <span className="hidden sm:inline">Previous</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className="flex items-center"
                          >
                            <span className="hidden sm:inline">Next</span>
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="calendar">
            <Card className="w-full">
              <CardHeader>
                <CardTitle>Appointment Calendar</CardTitle>
                <CardDescription>
                  View and manage your appointments in a calendar view
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative w-full">
                  {loading ? (
                    <Skeleton className="h-[600px] w-full" />
                  ) : error ? (
                    <div className="flex h-[400px] flex-col items-center justify-center space-y-3 text-center">
                      <div className="bg-red-100 p-4 rounded-full">
                        <Calendar className="h-8 w-8 text-red-500" />
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-medium text-lg">Error loading appointments</h3>
                        <p className="text-muted-foreground text-sm">{error}</p>
                      </div>
                      <Button
                        className="mt-4"
                        onClick={() => currentSpace && dispatch(fetchAppointmentsWithDetails(currentSpace.id))}
                      >
                        Try Again
                      </Button>
                    </div>
                  ) : (
                    <AppointmentCalendar 
                      appointments={appointmentsWithDetails}
                      onEventClick={handleEventClick}
                      onEventDrop={handleEventDrop}
                      loading={loading}
                      onDateSelect={(start, end) => {
                        console.log("Date selected:", start, end)
                        // Implement new appointment creation here
                      }}
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <Sheet open={!!selectedAppointment} onOpenChange={() => setSelectedAppointment(null)}>
        <SheetContent className="sm:max-w-[600px] h-full p-0">
          <SheetHeader className="px-6 pt-6">
            <SheetTitle>Appointment Details</SheetTitle>
            <SheetDescription>
              View and manage appointment information
            </SheetDescription>
          </SheetHeader>
          {selectedAppointment && (
            <ScrollArea className="h-[calc(100vh-8rem)]">
              <div className="px-6 pb-6">
                <div className="mt-6 space-y-6">
                  <div className="space-y-2">
                    <h3 className="font-medium text-sm text-muted-foreground">Customer Information</h3>
                    <div className="rounded-lg border bg-card">
                      <div className="flex items-center gap-4 p-4">
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#FFF8E0]">
                          <User className="h-6 w-6 text-[#F5B800]" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium leading-none">{selectedAppointment.customerName}</p>
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Mail className="h-3 w-3" />
                              <span>{selectedAppointment.customerEmail}</span>
                            </div>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Phone className="h-3 w-3" />
                              <span>{selectedAppointment.customerPhone}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="font-medium text-sm text-muted-foreground">Appointment Details</h3>
                    <div className="rounded-lg border bg-card text-card-foreground">
                      <div className="p-4 grid gap-3">
                        <div className="flex items-center gap-2">
                          <CalendarDays className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {format(selectedAppointment.date, 'MMMM d, yyyy')} at {selectedAppointment.time}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{selectedAppointment.duration}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="font-medium text-sm text-muted-foreground">Services</h3>
                    <div className="rounded-lg border bg-card">
                      <div className="divide-y">
                        {selectedAppointment.services.map((service: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-4">
                            <div>
                              <div className="text-sm font-medium">{service.name}</div>
                              <div className="text-sm text-muted-foreground">{service.duration}</div>
                            </div>
                            <div className="text-sm font-medium">
                              {formatAppointmentPrice(service.price)}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="font-medium text-sm text-muted-foreground">Status</h3>
                    <Badge
                      variant={
                        selectedAppointment.status === "completed"
                          ? "secondary"
                          : selectedAppointment.status === "cancelled"
                          ? "destructive"
                          : selectedAppointment.status === "no-show"
                          ? "outline"
                          : "default"
                      }
                    >
                      {selectedAppointment.status.charAt(0).toUpperCase() + selectedAppointment.status.slice(1)}
                    </Badge>
                  </div>

                  {selectedAppointment.notes && (
                    <>
                      <Separator />
                      <div className="space-y-2">
                        <h3 className="font-medium text-sm text-muted-foreground">Notes</h3>
                        <div className="rounded-lg border bg-card p-4">
                          <p className="text-sm">{selectedAppointment.notes}</p>
                        </div>
                      </div>
                    </>
                  )}

                  <div className="flex space-x-2 mt-6">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedAppointment(null)
                        setIsRescheduleDialogOpen(true)
                      }}
                    >
                      Reschedule
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => {
                        setSelectedAppointment(null)
                        setIsCancelDialogOpen(true)
                      }}
                    >
                      Cancel Appointment
                    </Button>
                    <Button
                      variant="secondary"
                      onClick={handleMarkNoShow}
                    >
                      Mark as No-Show
                    </Button>
                  </div>
                </div>
              </div>
            </ScrollArea>
          )}
        </SheetContent>
      </Sheet>

      <Dialog open={isRescheduleDialogOpen} onOpenChange={setIsRescheduleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reschedule Appointment</DialogTitle>
            <DialogDescription>
              Choose a new date and time for this appointment.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="date" className="text-right">
                New Date and Time
              </Label>
              <div className="col-span-3">
                <Input
                  id="date"
                  type="datetime-local"
                  onChange={(e) => setRescheduledDate(new Date(e.target.value))}
                  className="col-span-3"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRescheduleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleReschedule}>Reschedule</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Appointment</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this appointment? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCancelDialogOpen(false)}>
              No, keep it
            </Button>
            <Button variant="destructive" onClick={handleCancel}>
              Yes, cancel it
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}