'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { staffPortalService } from '@/services/staffPortalService';
import { staffAuthService } from '@/services/staffAuthService';
import { useAppSelector } from '@/store/hooks';
import { selectCurrentSpace } from '@/store/slices/spaceSlice';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';

interface PinResetRequest {
  id: string;
  staffId: string;
  staffName: string;
  staffEmail: string;
  createdAt: Date;
  status: 'pending' | 'completed' | 'rejected';
}

interface StaffPinItem {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  lastLogin?: Date;
  pin: string;
}

export default function StaffPinManagementPage() {
  const router = useRouter();
  const [pinRequests, setPinRequests] = useState<PinResetRequest[]>([]);
  const [staffList, setStaffList] = useState<StaffPinItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStaff, setSelectedStaff] = useState<StaffPinItem | null>(null);
  const [newPin, setNewPin] = useState<string>('');
  const [isPinDialogOpen, setIsPinDialogOpen] = useState(false);
  // First check the space state, then fallback to salon state for backward compatibility
  const currentSpace = useAppSelector((state) => state.space.currentSpace || state.space.currentSpace);

  useEffect(() => {
    if (!currentSpace) {
      router.push('/dashboard');
      return;
    }

    fetchPinData();
  }, [currentSpace, router]);

  async function fetchPinData() {
    try {
      setIsLoading(true);
      
      if (!currentSpace) {
        return;
      }
      
      // In a real implementation, you'd fetch this data from your backend
      // This is a placeholder implementation
      const pinRequestsData = await staffPortalService.getPinResetRequests(currentSpace.id);
      // Ensure the data matches the PinResetRequest interface
      const formattedPinRequests = pinRequestsData.map(request => ({
        id: request.id || '',
        staffId: 'staffId' in request ? request.staffId : '',
        staffName: 'staffName' in request ? request.staffName : '',
        staffEmail: 'staffEmail' in request ? request.staffEmail : '',
        createdAt: request.createdAt,
        status: 'status' in request ? request.status : 'pending'
      } as PinResetRequest));
      setPinRequests(formattedPinRequests);
      
      const staffData = await staffPortalService.getStaffWithPins(currentSpace.id);
      setStaffList(staffData);
    } catch (error) {
      console.error('Error fetching PIN data:', error);
      toast.error('Failed to load staff PIN data');
    } finally {
      setIsLoading(false);
    }
  }

  const handleResetPin = (staff: StaffPinItem) => {
    setSelectedStaff(staff);
    // Generate a new PIN automatically
    const newRandomPin = staffAuthService.generateNewPin();
    setNewPin(newRandomPin);
    setIsPinDialogOpen(true);
  };

  const handleConfirmPinReset = async () => {
    if (!selectedStaff) return;
    
    try {
      // In a real implementation, you'd call your service to update the PIN
      const success = await staffPortalService.adminResetPin(selectedStaff.id, newPin);
      
      if (success) {
        toast.success(`PIN reset for ${selectedStaff.name}`);
        // Refresh data
        fetchPinData();
      } else {
        toast.error('Failed to reset PIN');
      }
    } catch (error) {
      console.error('Error resetting PIN:', error);
      toast.error('An error occurred while resetting PIN');
    } finally {
      setIsPinDialogOpen(false);
      setSelectedStaff(null);
    }
  };

  const handleResolveRequest = async (request: PinResetRequest, action: 'approve' | 'reject') => {
    try {
      // In a real implementation, you'd update the request status
      if (action === 'approve') {
        const newRandomPin = staffAuthService.generateNewPin();
        const success = await staffPortalService.adminResetPin(request.staffId, newRandomPin);
        
        if (success) {
          // Update request status
          await staffPortalService.updatePinResetRequestStatus(request.id, 'completed');
          toast.success(`Reset PIN for ${request.staffName}. New PIN: ${newRandomPin}`);
          fetchPinData();
        }
      } else {
        await staffPortalService.updatePinResetRequestStatus(request.id, 'rejected');
        toast.info(`Rejected PIN reset request for ${request.staffName}`);
        fetchPinData();
      }
    } catch (error) {
      console.error('Error handling request:', error);
      toast.error('Failed to process request');
    }
  };

  return (
    <div className="container mx-auto py-10 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Staff PIN Management</CardTitle>
          <CardDescription>
            Manage staff portal access and handle PIN reset requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medium mb-4">PIN Reset Requests</h3>
              {pinRequests.length === 0 ? (
                <p className="text-muted-foreground">No pending PIN reset requests</p>
              ) : (
                <Table>
                  <TableCaption>Staff PIN reset requests</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Staff Member</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Requested</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pinRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">{request.staffName}</TableCell>
                        <TableCell>{request.staffEmail}</TableCell>
                        <TableCell>{new Date(request.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            request.status === 'pending' 
                              ? 'bg-amber-100 text-amber-800' 
                              : request.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                          </span>
                        </TableCell>
                        <TableCell>
                          {request.status === 'pending' && (
                            <div className="flex space-x-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleResolveRequest(request, 'approve')}
                              >
                                Approve & Reset
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleResolveRequest(request, 'reject')}
                              >
                                Reject
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Staff Portal Access</h3>
              <Table>
                <TableCaption>Manage staff portal access PINs</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Staff Member</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Date Added</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {staffList.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">{staff.name}</TableCell>
                      <TableCell>{staff.email}</TableCell>
                      <TableCell>{new Date(staff.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>
                        {staff.lastLogin 
                          ? new Date(staff.lastLogin).toLocaleDateString() 
                          : 'Never'}
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleResetPin(staff)}
                        >
                          Reset PIN
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reset PIN Dialog */}
      <Dialog open={isPinDialogOpen} onOpenChange={setIsPinDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Staff PIN</DialogTitle>
            <DialogDescription>
              {selectedStaff && `Resetting PIN for ${selectedStaff.name}`}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            <div>
              <label htmlFor="new-pin" className="text-sm font-medium block mb-1">
                New PIN (auto-generated)
              </label>
              <div className="flex gap-2">
                <Input 
                  id="new-pin"
                  value={newPin} 
                  onChange={(e) => setNewPin(e.target.value)}
                  maxLength={4}
                  pattern="[0-9]*"
                  className="text-xl tracking-wider text-center"
                />
                <Button
                  variant="outline"
                  onClick={() => setNewPin(staffAuthService.generateNewPin())}
                >
                  Regenerate
                </Button>
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground">
              The staff member will need to use this PIN to access their portal.
              You should communicate this PIN to them securely.
            </p>
          </div>
          
          <DialogFooter>
            <Button variant="ghost" onClick={() => setIsPinDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirmPinReset}>
              Reset PIN
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
