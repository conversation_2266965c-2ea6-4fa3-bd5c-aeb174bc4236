'use client'

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { PaymentIntegration } from "@/services/types/models"

const mpesaConfigSchema = z.object({
  isActive: z.boolean(),
  paybillNumber: z.string().optional(),
  tillNumber: z.string().optional(),
  shortCode: z.string().optional(),
  consumerKey: z.string().optional(),
  consumerSecret: z.string().optional(),
})

type MpesaConfigValues = z.infer<typeof mpesaConfigSchema>

export default function PaymentSettingsPage() {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<MpesaConfigValues>({
    resolver: zodResolver(mpesaConfigSchema),
    defaultValues: {
      isActive: false,
    },
  })

  async function onSubmit(data: MpesaConfigValues) {
    try {
      setIsLoading(true)

      // Here you would save the configuration to your backend
      // This is a placeholder for the actual implementation
      const paymentIntegration: PaymentIntegration = {
        id: 'mpesa', // Use a proper ID in production
        type: 'mpesa',
        isActive: data.isActive,
        details: {
          paybillNumber: data.paybillNumber,
          tillNumber: data.tillNumber,
          shortCode: data.shortCode,
          consumerKey: data.consumerKey,
          consumerSecret: data.consumerSecret,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      // Save to your backend
      await fetch('/api/settings/payment-integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentIntegration),
      })

      toast.success("Payment settings saved successfully")
    } catch (error) {
      console.error('Error saving payment settings:', error)
      toast.error("Failed to save payment settings")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Payment Settings</h3>
        <p className="text-sm text-muted-foreground">
          Configure your payment integrations and preferences
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>M-PESA Integration</CardTitle>
          <CardDescription>
            Configure M-PESA payment settings for your business
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Enable M-PESA Payments</FormLabel>
                      <FormDescription>
                        Allow customers to pay via M-PESA
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="paybillNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Paybill Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter paybill number" {...field} />
                        </FormControl>
                        <FormDescription>
                          Your M-PESA paybill number
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tillNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Till Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter till number" {...field} />
                        </FormControl>
                        <FormDescription>
                          Your M-PESA till number (if applicable)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="shortCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Short Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter short code" {...field} />
                      </FormControl>
                      <FormDescription>
                        Your M-PESA short code for API integration
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="consumerKey"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Consumer Key</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Enter consumer key"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Your M-PESA API consumer key
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="consumerSecret"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Consumer Secret</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Enter consumer secret"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Your M-PESA API consumer secret
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
} 