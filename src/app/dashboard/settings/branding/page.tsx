'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';
import { Switch } from '@/components/ui/switch';
import { Upload, Palette, Brush } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const CUSTOM_BRANDING_BENEFITS = [
  "Remove GroomBook branding from customer communications",
  "Add your logo to all customer emails and notifications",
  "Customize colors to match your brand identity",
  "Create a branded customer booking experience",
  "Custom domain for your booking portal"
];

function CustomBrandingContent() {
  const { toast } = useToast();
  const [brandSettings, setBrandSettings] = useState({
    primaryColor: '#6366f1',
    accentColor: '#4f46e5',
    textColor: '#1f2937',
    logoUrl: '',
    removeGroomBookBranding: true,
    customDomain: '',
    customEmailHeader: ''
  });

  const handleColorChange = (color: string, type: 'primaryColor' | 'accentColor' | 'textColor') => {
    setBrandSettings(prev => ({
      ...prev,
      [type]: color
    }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setBrandSettings(prev => ({
      ...prev,
      removeGroomBookBranding: checked
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBrandSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveChanges = () => {
    // Here you would save the branding settings to your backend
    toast({
      title: "Brand settings saved",
      description: "Your custom branding changes have been applied.",
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Custom Branding</h2>
        <p className="text-muted-foreground">Customize your brand appearance for your customers</p>
      </div>

      <Tabs defaultValue="colors">
        <TabsList>
          <TabsTrigger value="colors">
            <Palette className="h-4 w-4 mr-2" />
            Colors
          </TabsTrigger>
          <TabsTrigger value="logo">
            <Upload className="h-4 w-4 mr-2" />
            Logo & Images
          </TabsTrigger>
          <TabsTrigger value="domain">
            <Brush className="h-4 w-4 mr-2" />
            Domain & Emails
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="colors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Brand Colors</CardTitle>
              <CardDescription>Set the colors that represent your brand</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="primary-color">Primary Color</Label>
                  <div className="flex items-center gap-2">
                    <div 
                      className="h-10 w-10 rounded-md border" 
                      style={{ backgroundColor: brandSettings.primaryColor }}
                    />
                    <Input 
                      id="primary-color"
                      value={brandSettings.primaryColor}
                      onChange={(e) => handleColorChange(e.target.value, 'primaryColor')}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="accent-color">Accent Color</Label>
                  <div className="flex items-center gap-2">
                    <div 
                      className="h-10 w-10 rounded-md border" 
                      style={{ backgroundColor: brandSettings.accentColor }}
                    />
                    <Input 
                      id="accent-color"
                      value={brandSettings.accentColor}
                      onChange={(e) => handleColorChange(e.target.value, 'accentColor')}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="text-color">Text Color</Label>
                  <div className="flex items-center gap-2">
                    <div 
                      className="h-10 w-10 rounded-md border" 
                      style={{ backgroundColor: brandSettings.textColor }}
                    />
                    <Input 
                      id="text-color"
                      value={brandSettings.textColor}
                      onChange={(e) => handleColorChange(e.target.value, 'textColor')}
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 py-2">
                <Switch 
                  id="remove-branding"
                  checked={brandSettings.removeGroomBookBranding}
                  onCheckedChange={handleSwitchChange}
                />
                <Label htmlFor="remove-branding">Remove GroomBook Branding</Label>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveChanges}>Save Color Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="logo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Logo & Branding Assets</CardTitle>
              <CardDescription>Upload your logo and branding assets</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="logo-upload">Upload Logo</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center">
                  {brandSettings.logoUrl ? (
                    <div className="w-full flex flex-col items-center gap-2">
                      <div className="h-32 w-32 relative">
                        <img
                          src={brandSettings.logoUrl}
                          alt="Company Logo"
                          className="h-full w-full object-contain"
                        />
                      </div>
                      <Button variant="outline" size="sm">Replace Logo</Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Upload className="h-10 w-10 text-muted-foreground mb-2 mx-auto" />
                      <p className="text-sm font-medium">Drag and drop your logo here</p>
                      <p className="text-xs text-muted-foreground">SVG, PNG or JPG (max. 5MB)</p>
                      <Button variant="outline" size="sm" className="mt-4">
                        Select Logo
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveChanges}>Save Logo Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="domain" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Domain & Email Branding</CardTitle>
              <CardDescription>Customize your customer-facing domain and email templates</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="custom-domain">Custom Domain</Label>
                <Input 
                  id="custom-domain"
                  name="customDomain"
                  placeholder="booking.yourbrand.com"
                  value={brandSettings.customDomain}
                  onChange={handleInputChange}
                />
                <p className="text-sm text-muted-foreground">
                  You'll need to set up DNS records to point your domain to our servers
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="custom-email-header">Email Header Text</Label>
                <Input 
                  id="custom-email-header"
                  name="customEmailHeader"
                  placeholder="Your brand tagline or welcome message"
                  value={brandSettings.customEmailHeader}
                  onChange={handleInputChange}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveChanges}>Save Domain Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function CustomBrandingPage() {
  return (
    <FeatureGate
      feature={RestrictedFeature.CUSTOM_BRANDING}
      title="Custom Branding"
      description="Personalize your customer experience with your own branding."
      // Either the FeatureGate component doesn't accept benefits prop or it has a different name
      // Pass benefits through children or remove it if not needed
    >
      <CustomBrandingContent />
    </FeatureGate>
  );
}
