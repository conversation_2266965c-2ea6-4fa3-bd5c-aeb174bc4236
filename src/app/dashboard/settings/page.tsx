"use client"

import { useState, useRef } from "react"

import Image from "next/image"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Store, Globe, Instagram, Facebook, Upload, User, Info, QrCode, DollarSign } from "lucide-react"
import { BookingQRCode } from "@/components/space/BookingQRCode"
import { CurrencySettings } from "@/components/settings/CurrencySettings"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useAppSelector, useAppDispatch } from "@/store/hooks"
import { updateSpaceDetails, clearSpaceState } from "@/store/slices/spaceSlice"
import { useAuth } from "@/utils/auth"
import { toast } from "sonner"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Space } from "@/types/space"; // Added import

// Extend the Space type locally to include optional businessHours, notifications, and mpesaEnabled
interface BusinessHour {
  day: string;
  open: string;
  close: string;
  enabled: boolean;
}

export default function SettingsPage() {
  const { user } = useAuth()
  const currentSpace = useAppSelector((state) => state.space.currentSpace) as Space | null;
  const [isLoading, setIsLoading] = useState(false)
  const [deleteConfirmation, setDeleteConfirmation] = useState("")
  const [deleteSpaceConfirmation, setDeleteSpaceConfirmation] = useState("")
  const spaceLogoInputRef = useRef<HTMLInputElement>(null)
  const userAvatarInputRef = useRef<HTMLInputElement>(null)

  // Add M-PESA validation schema
  const mpesaSchema = z.object({
    mpesaBusinessName: z.string()
      .min(3, "Business name must be at least 3 characters")
      .max(100, "Business name must not exceed 100 characters"),
    mpesaAccountType: z.enum(["till", "paybill"]),
    mpesaShortcode: z.string()
      .min(5, "Till/Paybill number must be at least 5 digits")
      .max(10, "Till/Paybill number must not exceed 10 digits")
      .regex(/^\d+$/, "Till/Paybill number must contain only digits"),
    mpesaAccountNumber: z.string()
      .min(1, "Account number is required for Paybill")
      .max(20, "Account number must not exceed 20 characters")
      .optional()
      .refine((val) => {
        // Only validate if account type is paybill
        if (form?.getValues("mpesaAccountType") === "paybill") {
          return !!val;
        }
        return true;
      }, "Account number is required for Paybill"),
    mpesaPhoneNumber: z.string()
      .regex(/^254[0-9]{9}$/, "Phone number must start with 254 followed by 9 digits")
      .min(12, "Phone number must be 12 digits")
      .max(12, "Phone number must be 12 digits"),
    mpesaAutoConfirm: z.boolean()
  });

  type MpesaFormValues = z.infer<typeof mpesaSchema>;

  const form = useForm<MpesaFormValues>({
    resolver: zodResolver(mpesaSchema),
    defaultValues: {
      mpesaBusinessName: currentSpace?.mpesaBusinessName || "",
      mpesaAccountType: currentSpace?.mpesaAccountType || "till",
      mpesaShortcode: currentSpace?.mpesaShortcode || "",
      mpesaAccountNumber: currentSpace?.mpesaAccountNumber || "",
      mpesaPhoneNumber: currentSpace?.mpesaPhoneNumber || "",
      mpesaAutoConfirm: currentSpace?.mpesaAutoConfirm || false
    }
  });

  // Add state for business hours
  const [businessHours, setBusinessHours] = useState<BusinessHour[]>(() => {
    // Try to load from currentSpace, fallback to default
    return (
      currentSpace?.businessHours ||
      [
        { day: 'Monday', open: '', close: '', enabled: false },
        { day: 'Tuesday', open: '', close: '', enabled: false },
        { day: 'Wednesday', open: '', close: '', enabled: false },
        { day: 'Thursday', open: '', close: '', enabled: false },
        { day: 'Friday', open: '', close: '', enabled: false },
        { day: 'Saturday', open: '', close: '', enabled: false },
        { day: 'Sunday', open: '', close: '', enabled: false },
      ]
    );
  });

  // Add state for notification preferences
  const [notificationSettings, setNotificationSettings] = useState(() => ({
    email: currentSpace?.notificationPreferences?.email ?? false,
    sms: currentSpace?.notificationPreferences?.sms ?? false,
    reminders: currentSpace?.notificationPreferences?.push ?? false,// Mapped from reminders
  }));

  // Add state for mpesaEnabled
  const [mpesaEnabled, setMpesaEnabled] = useState<boolean>(currentSpace?.mpesaEnabled ?? false);

  // Add state for payment modes
  const [paymentModes, setPaymentModes] = useState(() => ({
    cash: currentSpace?.paymentModes?.cash ?? true, // Default to true for cash
    card: currentSpace?.paymentModes?.card ?? false,
    mpesa: currentSpace?.paymentModes?.mpesa ?? false,
  }));

  // Currency settings
  const [currency, setCurrency] = useState(() => currentSpace?.currency || {
    code: 'KES',
    symbol: 'Ksh',
    name: 'Kenyan Shilling',
    position: 'prefix' as const
  });

  const availableCurrencies = [
    { code: 'KES', symbol: 'Ksh', name: 'Kenyan Shilling', position: 'prefix' as const },
    { code: 'USD', symbol: '$', name: 'US Dollar', position: 'prefix' as const },
    { code: 'EUR', symbol: '€', name: 'Euro', position: 'prefix' as const },
    { code: 'GBP', symbol: '£', name: 'British Pound', position: 'prefix' as const },
    { code: 'NGN', symbol: '₦', name: 'Nigerian Naira', position: 'prefix' as const },
    { code: 'ZAR', symbol: 'R', name: 'South African Rand', position: 'prefix' as const },
    { code: 'UGX', symbol: 'USh', name: 'Ugandan Shilling', position: 'prefix' as const },
    { code: 'TZS', symbol: 'TSh', name: 'Tanzanian Shilling', position: 'prefix' as const },
    { code: 'RWF', symbol: 'RF', name: 'Rwandan Franc', position: 'prefix' as const },
  ];

  const handleSpaceLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Here you would typically upload to your storage service
      toast.success('Logo uploaded successfully')
    }
  }

  const handleUserAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Here you would typically upload to your storage service
      toast.success('Avatar uploaded successfully')
    }
  }

  const handleSave = async (data: MpesaFormValues) => {
    try {
      setIsLoading(true);

      // Validate M-PESA details before saving
      if (data.mpesaAccountType === "paybill" && !data.mpesaAccountNumber) {
        toast.error("Account number is required for Paybill");
        return;
      }

      // Here you would typically make an API call to save the settings
      // Make sure to use HTTPS and proper encryption for sensitive data
      console.log('Saving M-PESA settings:', {
        ...data,
        // Don't log sensitive data in production
        mpesaShortcode: '***',
        mpesaAccountNumber: '***',
        mpesaPhoneNumber: '***'
      });

      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (deleteConfirmation !== "DELETE") {
      toast.error("Please type DELETE to confirm")
      return
    }

    try {
      // Here you would typically make an API call to delete the account
      toast.success("Account deleted successfully")
    } catch (_) {
      toast.error("Failed to delete account")
    }
  }

  const handleDeleteSpace = async () => {
    if (!currentSpace) {
      toast.error("No space selected")
      return
    }

    if (deleteSpaceConfirmation !== currentSpace.name) {
      toast.error("Please type the space name to confirm")
      return
    }

    try {
      setIsLoading(true)
      // Call the API endpoint to delete the space
      const response = await fetch(`/api/spaces/${currentSpace.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete space')
      }

      // Clear the current space from Redux
      dispatch(clearSpaceState())

      // Redirect to the dashboard
      window.location.href = '/dashboard'

      toast.success("Space deleted successfully")
    } catch (error) {
      console.error('Error deleting space:', error)
      toast.error(error instanceof Error ? error.message : "Failed to delete space")
    } finally {
      setIsLoading(false)
    }
  }

  const dispatch = useAppDispatch();

  // Handler to save business hours
  const handleSaveBusinessHours = async () => {
    if (!currentSpace) {
      toast.error('No space selected');
      return;
    }

    setIsLoading(true);
    try {
      // First update via API
      await fetch(`/api/spaces/${currentSpace.id}/business-hours`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ businessHours }),
      });

      // Then update Redux store to keep UI in sync
      await dispatch(updateSpaceDetails({
        id: currentSpace.id,
        businessHours
      }));

      toast.success('Business hours saved');
    } catch (error) {
      console.error('Error saving business hours:', error);
      toast.error('Failed to save business hours');
    } finally {
      setIsLoading(false);
    }
  };

  // Handler to save notification preferences
  const handleSaveNotifications = async () => {
    if (!currentSpace) {
      toast.error('No space selected');
      return;
    }
    setIsLoading(true);
    try {
      // Save notifications to space document
      const payload = { notificationPreferences: notificationSettings };
      await fetch(`/api/spaces/${currentSpace.id}/notifications`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      
      // Optionally, update Redux store if these preferences are also stored there
      // This depends on whether spaceSlice.updateSpaceDetails handles notificationPreferences
      dispatch(updateSpaceDetails({ 
        id: currentSpace.id, 
        notifications: notificationSettings 
      }));

      toast.success('Notification preferences saved');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast.error('Failed to save notification preferences');
    } finally {
      setIsLoading(false);
    }
  };

  // Handler to save mpesaEnabled
  const handleSaveMpesaEnabled = async () => {
    if (!currentSpace) {
      toast.error('No space selected');
      return;
    }
    setIsLoading(true);
    try {
      await fetch(`/api/spaces/${currentSpace.id}/mpesa-enabled`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ mpesaEnabled }),
      });
      toast.success('M-PESA integration setting saved');
    } catch (_) {
      toast.error('Failed to save M-PESA integration setting');
    } finally {
      setIsLoading(false);
    }
  };

  // Handler to save payment modes
  const handleSavePaymentModes = async () => {
    if (!currentSpace) {
      toast.error('No space selected');
      return;
    }

    setIsLoading(true);
    try {
      // Update via API
      await fetch(`/api/spaces/${currentSpace.id}/payment-modes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ paymentModes }),
      });

      // Update Redux store
      await dispatch(updateSpaceDetails({
        id: currentSpace.id,
        paymentModes
      }));

      toast.success('Payment methods saved successfully');
    } catch (error) {
      console.error('Error saving payment methods:', error);
      toast.error('Failed to save payment methods');
    } finally {
      setIsLoading(false);
    }
  };

  // Handler to save currency settings
  const handleSaveCurrency = async () => {
    if (!currentSpace) {
      toast.error('No space selected');
      return;
    }

    setIsLoading(true);
    try {
      await dispatch(updateSpaceDetails({
        id: currentSpace.id,
        currency
      }));

      toast.success('Currency settings updated successfully');
    } catch (error) {
      console.error('Error updating currency settings:', error);
      toast.error('Failed to update currency settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full flex-1 flex flex-col p-4 sm:p-8">
      <div className="mb-6 sm:mb-8">
        <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Settings</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Manage your account and business settings
        </p>
      </div>

      <Tabs defaultValue="profile" >
       
          <TabsList className="w-full overflow-x-auto items-center justify-start">
            <TabsTrigger value="profile" className="text-sm whitespace-nowrap flex-1 sm:flex-none">
              Profile
            </TabsTrigger>
            <TabsTrigger value="business" className="text-sm whitespace-nowrap">
              Business
            </TabsTrigger>
            <TabsTrigger value="business-hours" className="text-sm whitespace-nowrap">
              Business Hours
            </TabsTrigger>
            <TabsTrigger value="integrations" className="text-sm whitespace-nowrap">
              Integrations
            </TabsTrigger>
            <TabsTrigger value="notifications" className="text-sm whitespace-nowrap">
              Notifications
            </TabsTrigger>
            <TabsTrigger value="currency" className="text-sm whitespace-nowrap">
              Currency
            </TabsTrigger>
            <TabsTrigger value="danger" className="text-sm text-red-600 whitespace-nowrap">
              Danger Zone
            </TabsTrigger>
            
          </TabsList>
        

        <div className="flex-1 overflow-auto pt-6">
          <TabsContent value="profile" className="mt-0 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>User Profile</CardTitle>
              <CardDescription>
                Manage your personal information and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center gap-6">
                <div className="relative">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={user?.photoURL || ''} />
                    <AvatarFallback>
                      <User className="h-12 w-12" />
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    size="icon"
                    variant="outline"
                    className="absolute bottom-0 right-0"
                    onClick={() => userAvatarInputRef.current?.click()}
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                  <input
                    type="file"
                    ref={userAvatarInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={handleUserAvatarUpload}
                  />
                </div>
                <div className="space-y-1">
                  <h3 className="font-medium">{user?.displayName}</h3>
                  <p className="text-sm text-muted-foreground">{user?.email}</p>
                </div>
              </div>

              <Separator />

              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input id="name" defaultValue={user?.displayName || ''} />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" defaultValue={user?.email || ''} />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" type="tel" defaultValue={user?.phoneNumber || ''} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business" className="mt-0 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Business Profile</CardTitle>
              <CardDescription>
                Update your business information and branding
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center gap-6">
                <div className="relative">
                  <div className="h-24 w-24 rounded-lg border-2 border-dashed flex items-center justify-center">
                    {currentSpace?.logo ? (
                      <Image
                      src={currentSpace.logo}
                      alt="Business logo"
                      width={96}
                      height={96}
                      className="h-full w-full object-cover rounded-lg"
                      />
                    ) : (
                      <Store className="h-12 w-12 text-muted-foreground" />
                    )}
                  </div>
                  <Button
                    size="icon"
                    variant="outline"
                    className="absolute bottom-0 right-0"
                    onClick={() => spaceLogoInputRef.current?.click()}
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                  <input
                    type="file"
                    ref={spaceLogoInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={handleSpaceLogoUpload}
                  />
                </div>
                <div className="space-y-1">
                  <h3 className="font-medium">{currentSpace?.name}</h3>
                  <p className="text-sm text-muted-foreground">Business Logo</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="businessType">Business Type</Label>
                  <Select 
                    defaultValue={currentSpace?.type || 'salon'}
                    onValueChange={(value) => {
                      // This assumes direct update or part of a larger form save elsewhere.
                      // If immediate save is needed, dispatch updateSpaceDetails here.
                      // dispatch(updateSpaceDetails({ id: currentSpace.id, type: value as Space['type'] }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select business type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="salon">Salon</SelectItem>
                      <SelectItem value="barbershop">Barbershop</SelectItem>
                      <SelectItem value="wellness">Wellness Center</SelectItem>
                      <SelectItem value="fitness">Fitness Studio</SelectItem>
                      <SelectItem value="space">General Space</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="businessName">Business Name</Label>
                  <Input id="businessName" defaultValue={currentSpace?.name} />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    defaultValue={currentSpace?.description}
                    className="min-h-[100px]"
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" defaultValue={currentSpace?.phone} />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="email">Business Email</Label>
                  <Input id="email" type="email" defaultValue={currentSpace?.email} />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Location</h3>
                <div className="grid gap-2">
                  <Label htmlFor="address">Address</Label>
                  <Input id="address" defaultValue={currentSpace?.address} />
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="city">City</Label>
                    <Input id="city" defaultValue={currentSpace?.city} />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="state">State/Province</Label>
                    <Input id="state" defaultValue={currentSpace?.state} />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="postal-code">Postal Code</Label>
                    <Input id="postal-code" defaultValue={currentSpace?.postalCode} />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Social Media</h3>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="website">Website</Label>
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <Input id="website" defaultValue={currentSpace?.website} />
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="instagram">Instagram</Label>
                    <div className="flex items-center gap-2">
                      <Instagram className="h-4 w-4 text-muted-foreground" />
                      <Input id="instagram" defaultValue={currentSpace?.instagram} />
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="facebook">Facebook</Label>
                    <div className="flex items-center gap-2">
                      <Facebook className="h-4 w-4 text-muted-foreground" />
                      <Input id="facebook" defaultValue={currentSpace?.facebook} />
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Currency Settings Section */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  <h3 className="text-lg font-medium">Currency Settings</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Set the currency for your business transactions and pricing
                </p>

                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="currency-select">Business Currency</Label>
                    <Select
                      value={currency.code}
                      onValueChange={(value) => {
                        const selectedCurrency = availableCurrencies.find(c => c.code === value);
                        if (selectedCurrency) {
                          setCurrency(selectedCurrency);
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select currency" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableCurrencies.map((curr) => (
                          <SelectItem key={curr.code} value={curr.code}>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{curr.symbol}</span>
                              <span>{curr.name} ({curr.code})</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      Current: {currency.symbol} {currency.name} ({currency.code})
                    </p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleSaveCurrency} disabled={isLoading}>
                    {isLoading ? 'Saving...' : 'Save Currency Settings'}
                  </Button>
                </div>
              </div>

              <Separator />

              {/* Payment Methods Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Payment Methods</h3>
                <p className="text-sm text-muted-foreground">
                  Select which payment methods your business accepts
                </p>
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="payment-cash">Cash Payments</Label>
                      <p className="text-sm text-muted-foreground">
                        Accept cash payments for services
                      </p>
                    </div>
                    <Switch
                      id="payment-cash"
                      checked={paymentModes.cash}
                      onCheckedChange={(checked) => setPaymentModes(prev => ({ ...prev, cash: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="payment-card">Card Payments</Label>
                      <p className="text-sm text-muted-foreground">
                        Accept credit/debit card payments
                      </p>
                    </div>
                    <Switch
                      id="payment-card"
                      checked={paymentModes.card}
                      onCheckedChange={(checked) => setPaymentModes(prev => ({ ...prev, card: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="payment-mpesa">M-PESA Payments</Label>
                      <p className="text-sm text-muted-foreground">
                        Accept M-PESA mobile money payments
                      </p>
                    </div>
                    <Switch
                      id="payment-mpesa"
                      checked={paymentModes.mpesa}
                      onCheckedChange={(checked) => setPaymentModes(prev => ({ ...prev, mpesa: checked }))}
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button onClick={handleSavePaymentModes} disabled={isLoading}>
                    {isLoading ? 'Saving...' : 'Save Payment Methods'}
                  </Button>
                </div>
              </div>

              <Separator />

              {/* Direct Booking Section */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Direct Booking</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Allow customers to book appointments directly using a QR code or link
                  </p>

                  {currentSpace && (
                    <BookingQRCode
                      spaceId={currentSpace.id}
                      spaceName={currentSpace.name}
                    />
                  )}
                </div>
              </div>

              <Separator />

              {/* M-PESA Info Banner if not enabled */}
              {!mpesaEnabled && (
                <div className="flex items-start gap-3 rounded-md border border-green-200 bg-green-50 p-3 mb-4">
                  <Info className="h-5 w-5 text-green-500 mt-1" />
                  <div>
                    <div className="font-semibold text-green-700">M-PESA Integration Not Enabled</div>
                    <div className="text-sm text-green-700">
                      M-PESA business details are hidden because M-PESA integration is not enabled for this space.<br />
                      To activate, go to the <b>Integrations</b> tab and enable M-PESA. Once enabled, you can enter your business details here.
                    </div>
                  </div>
                </div>
              )}

              {/* M-PESA Section */}
              {mpesaEnabled && (
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleSave)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="mpesaBusinessName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Business Name (As registered with M-PESA)</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter your M-PESA registered business name"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="mpesaAccountType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Account Type</FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select account type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="till">Till</SelectItem>
                              <SelectItem value="paybill">Paybill</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="mpesaShortcode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Till/Paybill Number</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter your Till/Paybill number"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch("mpesaAccountType") === "paybill" && (
                      <FormField
                        control={form.control}
                        name="mpesaAccountNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Account Number</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Enter your account number (for Paybill only)"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <FormField
                      control={form.control}
                      name="mpesaPhoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Business Phone Number</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter phone number linked to M-PESA (e.g., ************)"
                            />
                          </FormControl>
                          <FormDescription>
                            Must start with 254 followed by 9 digits
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="mpesaAutoConfirm"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between space-y-0">
                          <div className="space-y-0.5">
                            <FormLabel>Auto-Confirm Payments</FormLabel>
                            <FormDescription>
                              Automatically mark services as paid when M-PESA payment is received
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business-hours" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Business Hours</CardTitle>
              <CardDescription>
                Set your space&apos;s operating hours
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {businessHours.map((day: BusinessHour, idx: number) => (
                  <div key={day.day} className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Switch
                        checked={day.enabled}
                        onCheckedChange={v => setBusinessHours((businessHours: BusinessHour[]) => businessHours.map((d: BusinessHour, i: number) => i === idx ? { ...d, enabled: v } : d))}
                        id={day.day.toLowerCase()}
                      />
                      <Label htmlFor={day.day.toLowerCase()}>{day.day}</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        type="time"
                        value={day.open}
                        onChange={e => setBusinessHours((businessHours: BusinessHour[]) => businessHours.map((d: BusinessHour, i: number) => i === idx ? { ...d, open: e.target.value } : d))}
                        disabled={!day.enabled}
                      />
                      <span className="text-muted-foreground">to</span>
                      <Input
                        type="time"
                        value={day.close}
                        onChange={e => setBusinessHours((businessHours: BusinessHour[]) => businessHours.map((d: BusinessHour, i: number) => i === idx ? { ...d, close: e.target.value } : d))}
                        disabled={!day.enabled}
                      />
                    </div>
                  </div>
                ))}
                <div className="flex justify-end">
                  <Button onClick={handleSaveBusinessHours} disabled={isLoading}>
                    {isLoading ? 'Saving...' : 'Save Business Hours'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Manage how you receive notifications about your space
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <Switch
                  id="email-notifications"
                  checked={notificationSettings.email}
                  onCheckedChange={(checked) => setNotificationSettings(prev => ({ ...prev, email: checked }))}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="sms-notifications">SMS Notifications</Label>
                <Switch
                  id="sms-notifications"
                  checked={notificationSettings.sms}
                  onCheckedChange={(checked) => setNotificationSettings(prev => ({ ...prev, sms: checked }))}
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="push-notifications">Push Notifications</Label>
                <Switch
                  id="push-notifications"
                  checked={notificationSettings.reminders}
                  onCheckedChange={(checked) => setNotificationSettings(prev => ({ ...prev, push: checked }))}
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={handleSaveNotifications} disabled={isLoading}>
                  {isLoading ? 'Saving...' : 'Save Notification Preferences'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Integrations</CardTitle>
              <CardDescription>
                Manage your third-party integrations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="mpesa-integration" className="font-medium">M-PESA Integration</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable or disable M-PESA payments for your space.
                  </p>
                </div>
                <Switch
                  id="mpesa-integration"
                  checked={mpesaEnabled}
                  onCheckedChange={setMpesaEnabled}
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={handleSaveMpesaEnabled} disabled={isLoading}>
                  {isLoading ? 'Saving...' : 'Save Integration Settings'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="currency" className="space-y-6">
          <CurrencySettings />
        </TabsContent>

        <TabsContent value="danger" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Danger Zone</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-medium">Delete Space</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  Permanently delete your space and all its associated data. This action cannot be undone.
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" disabled={!currentSpace || isLoading}>
                      Delete Space
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="max-w-[95vw] w-full sm:max-w-md">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the
                        space &quot;<b>{currentSpace?.name}</b>&quot; and remove all associated data.
                        Please type <b>{currentSpace?.name}</b> to confirm.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <Input
                      type="text"
                      value={deleteSpaceConfirmation}
                      onChange={(e) => setDeleteSpaceConfirmation(e.target.value)}
                      placeholder={currentSpace?.name || "Type space name"}
                      className="my-2"
                    />
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeleteSpace}
                        disabled={deleteSpaceConfirmation !== currentSpace?.name || isLoading}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        {isLoading ? 'Deleting...' : 'Delete Space'}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>

              <Separator />

              <div>
                <h3 className="font-medium">Delete Account</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  Permanently delete your account and all associated data. This action cannot be undone.
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" disabled={isLoading}>Delete My Account</Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="max-w-[95vw] w-full sm:max-w-md">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete your
                        account and all your data. Please type DELETE to confirm.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <Input
                      type="text"
                      value={deleteConfirmation}
                      onChange={(e) => setDeleteConfirmation(e.target.value)}
                      placeholder="DELETE"
                      className="my-2"
                    />
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeleteAccount}
                        disabled={deleteConfirmation !== "DELETE" || isLoading}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        {isLoading ? 'Deleting...' : 'Delete Account'}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </div>
    </Tabs>
    </div>
  );
}
