"use client"

import { useState, useEffect } from "react"
import { CustomerForm } from "@/components/CustomerForm"
import { Customer } from "@/services/types/models"
import { customerService } from "@/services/firestore"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { User2, Phone, Mail, CalendarDays } from "lucide-react"
import { CustomerEditForm } from "@/components/CustomerEditForm"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  // These state variables will be used when implementing edit functionality
  const [isEditFormOpen, setIsEditFormOpen] = useState(false)
  const [selectedCustomer] = useState<Customer | undefined>(undefined)

  useEffect(() => {
    loadCustomers()
  }, [])

  const loadCustomers = async () => {
    try {
      setIsLoading(true)
      const data = await customerService.getAll()
      setCustomers(data as Customer[])
    } catch (error) {
      console.error('Error loading customers:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCustomerAdded = (newCustomer: Customer) => {
    setCustomers(prev => [...prev, newCustomer])
  }

  const handleCustomerUpdated = (updatedCustomer: Customer) => {
    setCustomers(prev =>
      prev.map(customer =>
        customer.id === updatedCustomer.id ? updatedCustomer : customer
      )
    )
  }

  // These functions will be used when implementing edit/delete functionality
  // Removing for now to fix linting errors
  // const handleEditCustomer = (customer: Customer) => {
  //   setSelectedCustomer(customer)
  //   setIsEditFormOpen(true)
  // }

  // const handleDeleteCustomer = async (id: string) => {
  //   try {
  //     await customerService.delete(id)
  //     setCustomers(prev => prev.filter(customer => customer.id !== id))
  //     toast.success("Customer deleted successfully")
  //   } catch (error) {
  //     console.error('Error deleting customer:', error)
  //     toast.error("Failed to delete customer")
  //   }
  // }

  const filteredCustomers = customers.filter(customer =>
    (customer?.displayName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (customer?.phoneNumber || '').includes(searchTerm) ||
    (customer?.email?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  )

  return (
    <div className="h-full flex-1 space-y-4 sm:space-y-8 p-4 sm:p-8">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Customers</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Manage your customer database
          </p>
        </div>
        <div className="w-full sm:w-auto">
          <CustomerForm onSuccess={handleCustomerAdded} />
        </div>
        <CustomerEditForm
          customer={selectedCustomer}
          isOpen={isEditFormOpen}
          onOpenChange={setIsEditFormOpen}
          onSuccess={handleCustomerUpdated}
        />
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search by name, phone, or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:max-w-sm"
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer List</CardTitle>
          <CardDescription>
            View and manage all your customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12 gap-3">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
              <p className="text-sm text-muted-foreground">Loading customers...</p>
            </div>
          ) : customers.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 gap-3">
              <User2 className="h-12 w-12 text-muted-foreground" />
              <h3 className="font-medium text-lg">No customers yet</h3>
              <p className="text-sm text-muted-foreground">
                Add your first customer to get started.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Visits</TableHead>
                  <TableHead>Last Visit</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead className="w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell>
                      <div className="font-medium">
                        <Link href={`/dashboard/customers/${customer.id}`} className="hover:underline">
                          {customer.displayName}
                        </Link>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                          {customer.phoneNumber}
                        </div>
                        <div className="flex items-center text-sm">
                          <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                          {customer.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{customer.visits} visits</div>
                    </TableCell>
                    <TableCell>
                      {customer.lastVisit ? (
                        <div className="flex items-center text-sm">
                          <CalendarDays className="mr-2 h-4 w-4 text-muted-foreground" />
                          {new Intl.DateTimeFormat('en-US', {
                            dateStyle: 'medium'
                          }).format(new Date(customer.lastVisit))}
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">Never</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-muted-foreground line-clamp-2">
                        {customer.notes || '-'}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        asChild
                      >
                        <Link href={`/dashboard/customers/${customer.id}`}>
                          View
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}