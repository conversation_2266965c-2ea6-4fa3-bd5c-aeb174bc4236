"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Customer } from "@/services/types/models"
import { customerService } from "@/services/firestore"
import { CustomerEditForm } from "@/components/CustomerEditForm"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  <PERSON>bs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  User, 
  Phone, 
  Mail, 
  CalendarDays, 
  Clock, 
  Edit, 
  Gift,
  ChevronLeft,
  Scissors,
  Users 
} from "lucide-react"
import { CustomerReferrals } from "@/components/loyalty/CustomerReferrals"
import { LoyaltyCard } from "@/components/loyalty/LoyaltyCard"
import { ReferralSystem } from "@/components/loyalty/ReferralSystem"
import { ReferralTracking } from "@/components/loyalty/ReferralTracking"
import Link from "next/link"
import { toast } from "sonner"

export default function CustomerDetailPage() {
  const { id } = useParams<{ id: string }>()
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditFormOpen, setIsEditFormOpen] = useState(false)

  useEffect(() => {
    if (id) {
      loadCustomer()
    }
  }, [id])

  const loadCustomer = async () => {
    try {
      setIsLoading(true)
      const data = await customerService.get(id as string)
      setCustomer(data)
    } catch (error) {
      console.error('Error loading customer:', error)
      toast.error("Failed to load customer details")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCustomerUpdated = (updatedCustomer: Customer) => {
    setCustomer(updatedCustomer)
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-12 gap-3">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
        <p className="text-sm text-muted-foreground">Loading customer details...</p>
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-12 gap-3">
        <User className="h-12 w-12 text-muted-foreground" />
        <h3 className="font-medium text-lg">Customer not found</h3>
        <p className="text-sm text-muted-foreground mb-4">
          The customer you're looking for doesn't exist or has been deleted.
        </p>
        <Button asChild>
          <Link href="/dashboard/customers">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Customers
          </Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="h-full flex-1 space-y-8 p-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            asChild
          >
            <Link href="/dashboard/customers">
              <ChevronLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            {customer.displayName}
          </h1>
          {customer.loyaltyPoints?.tier && (
            <Badge variant="outline" className="ml-2">
              {customer.loyaltyPoints.tier.charAt(0).toUpperCase() + customer.loyaltyPoints.tier.slice(1)} Member
            </Badge>
          )}
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setIsEditFormOpen(true)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Customer
          </Button>
        </div>
        
        <CustomerEditForm
          customer={customer}
          isOpen={isEditFormOpen}
          onOpenChange={setIsEditFormOpen}
          onSuccess={handleCustomerUpdated}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
            <CardDescription>Basic details and contact information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-1">
              <div className="text-sm font-medium">Name</div>
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4 text-muted-foreground" />
                {customer.displayName}
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Phone</div>
              <div className="flex items-center">
                <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                {customer.phoneNumber}
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Email</div>
              <div className="flex items-center">
                <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                {customer.email}
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Visit Stats</div>
              <div className="flex items-center">
                <Scissors className="mr-2 h-4 w-4 text-muted-foreground" />
                {customer.visits} total visits
              </div>
              
              {customer.lastVisit ? (
                <div className="flex items-center">
                  <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                  Last visit: {new Intl.DateTimeFormat('en-US', {
                    dateStyle: 'medium'
                  }).format(new Date(customer.lastVisit))}
                </div>
              ) : (
                <div className="flex items-center">
                  <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                  No visits yet
                </div>
              )}
            </div>
            
            {customer.loyaltyPoints?.birthdate && (
              <div className="space-y-1">
                <div className="text-sm font-medium">Birthdate</div>
                <div className="flex items-center">
                  <CalendarDays className="mr-2 h-4 w-4 text-muted-foreground" />
                  {new Intl.DateTimeFormat('en-US', {
                    dateStyle: 'medium'
                  }).format(new Date(customer.loyaltyPoints.birthdate))}
                </div>
              </div>
            )}
            
            {customer.notes && (
              <div className="space-y-1">
                <div className="text-sm font-medium">Notes</div>
                <div className="text-sm text-muted-foreground whitespace-pre-line">
                  {customer.notes}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        
        <div className="space-y-6">
          <Tabs defaultValue="loyalty">
            <TabsList className="w-full overflow-x-auto flex items-center justify-start">
              <TabsTrigger value="loyalty" className="whitespace-nowrap">
                <Gift className="h-4 w-4 mr-2" />
                Loyalty & Rewards
              </TabsTrigger>
              <TabsTrigger value="referrals" className="whitespace-nowrap">
                <Users className="h-4 w-4 mr-2" />
                Referrals
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="loyalty" className="pt-4">
              {customer.loyaltyPoints ? (
                <LoyaltyCard 
                  customer={customer} 
                  onPointsUpdate={loadCustomer}
                />
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Not Enrolled</CardTitle>
                    <CardDescription>This customer is not enrolled in the loyalty program</CardDescription>
                  </CardHeader>
                  <CardContent className="text-center py-6">
                    <Gift className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <p className="mb-4">
                      Enroll this customer in the loyalty program to enable points, rewards, and referrals.
                    </p>
                    <Button onClick={() => setIsEditFormOpen(true)}>
                      Enroll in Loyalty Program
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="referrals" className="pt-4 space-y-6">
              <ReferralSystem 
                customer={customer}
                onReferralSent={loadCustomer}
              />
              
              <ReferralTracking 
                customer={customer}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
