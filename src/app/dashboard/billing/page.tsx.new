'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/utils/auth'
import { useAppSelector } from '@/store/hooks'
import { getSubscriptionStatus, createCheckoutSession, manageSubscription } from '@/services/paddleService'
import { PLANS, PricePlan } from '@/lib/billing'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  CheckCircle2,
  CreditCard,
  Package,
  RefreshCw,
  ShieldCheck,
  XCircle
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from 'sonner'
import { SubscriptionTier } from '@/services/subscriptionService'

interface SubscriptionStatus {
  tier: SubscriptionTier;
  isActive: boolean;
  expiresAt: Date | null;
  features: string[];
}

export default function BillingPage() {
  const { user } = useAuth()
  const { currentSpace } = useAppSelector((state) => state.space)
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>('month')

  useEffect(() => {
    const loadSubscription = async () => {
      if (!user?.uid) return
      
      setLoading(true)
      try {
        const subscriptionData = await getSubscriptionStatus(user.uid)
        setSubscription(subscriptionData)
      } catch (error) {
        console.error('Error loading subscription:', error)
        toast.error('Failed to load subscription information')
      } finally {
        setLoading(false)
      }
    }

    loadSubscription()
  }, [user])

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Please log in to view billing information.</p>
      </div>
    )
  }

  if (!currentSpace) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <div className="bg-white p-8 rounded-xl shadow-sm border text-center max-w-md">
          <h2 className="text-2xl font-semibold mb-3">Select a Space</h2>
          <p className="text-gray-500">
            Please select a space from the dropdown in the sidebar to view billing information.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Billing</h2>
          <p className="text-muted-foreground">
            Manage your subscription and billing information
          </p>
        </div>
      </div>

      <Tabs defaultValue="subscription" className="space-y-4">
        <TabsList>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
        </TabsList>

        <TabsContent value="subscription" className="space-y-4">
          <div className="container max-w-7xl mx-auto p-6 space-y-8">
            {/* Current Subscription Card */}
            <Card className="relative overflow-hidden">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Current Subscription
                </CardTitle>
                <CardDescription>Your active subscription details</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                ) : subscription ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <p className="text-lg font-medium capitalize">{subscription.tier} Plan</p>
                        {subscription.expiresAt && (
                          <p className="text-sm text-muted-foreground">
                            Expires on {subscription.expiresAt.toLocaleDateString()}
                          </p>
                        )}
                      </div>
                      <Badge variant={subscription.isActive ? 'default' : 'destructive'}>
                        {subscription.isActive ? (
                          <CheckCircle2 className="mr-1 h-3 w-3" />
                        ) : (
                          <XCircle className="mr-1 h-3 w-3" />
                        )}
                        {subscription.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    {subscription.features && subscription.features.length > 0 && (
                      <div className="pt-4 border-t">
                        <h4 className="text-sm font-medium mb-2">Included Features:</h4>
                        <ul className="grid gap-2 text-sm">
                          {subscription.features.map((feature: string, i: number) => (
                            <li key={i} className="flex items-center gap-2">
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground">No active subscription found</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Plans Section */}
            <div className="space-y-6">
              <div className="flex flex-col items-center gap-4">
                <h2 className="text-2xl font-bold text-center">Available Plans</h2>
                <div className="w-full max-w-xs">
                  <div className="flex rounded-lg border p-1">
                    <Button
                      variant={billingInterval === 'month' ? 'default' : 'ghost'}
                      className="w-full"
                      onClick={() => setBillingInterval('month')}
                    >
                      Monthly
                    </Button>
                    <Button
                      variant={billingInterval === 'year' ? 'default' : 'ghost'}
                      className="w-full"
                      onClick={() => setBillingInterval('year')}
                    >
                      Yearly
                    </Button>
                  </div>
                </div>
              </div>

              <div className="grid gap-6 lg:grid-cols-3">
                {PLANS.map((plan: PricePlan) => (
                  <Card key={plan.id} className={`relative ${plan.popular ? 'border-primary shadow-lg' : ''}`}>
                    {plan.popular && (
                      <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                        <Badge variant="default" className="bg-primary">Most Popular</Badge>
                      </div>
                    )}
                    <CardHeader>
                      <CardTitle>{plan.name}</CardTitle>
                      <CardDescription>
                        <span className="text-2xl font-bold">
                          ${((billingInterval === 'month' ? plan.price.monthly : plan.price.yearly) / 100).toFixed(2)}
                        </span>
                        /{billingInterval}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="grid gap-2 text-sm">
                        {plan.features.map((feature: string, i: number) => (
                          <li key={i} className="flex items-center gap-2">
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    <CardFooter>
                      <Button
                        className="w-full"
                        variant={plan.popular ? 'default' : 'outline'}
                        onClick={() => {
                          toast.loading('Initializing checkout...')
                          createCheckoutSession(plan.id, user.uid)
                            .then(() => toast.success('Redirecting to checkout...'))
                            .catch(() => toast.error('Failed to initialize checkout'))
                        }}
                      >
                        <CreditCard className="mr-2 h-4 w-4" />
                        Subscribe
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>

            {/* Security Note */}
            <Card className="bg-muted/50">
              <CardContent className="pt-6">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <ShieldCheck className="h-4 w-4" />
                  <p>All payments are processed securely through Paddle</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
