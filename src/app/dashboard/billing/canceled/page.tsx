import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { AlertCircle, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'Payment Canceled - GroomLoop',
  description: 'Your payment process was cancelled',
};

export default function PaymentCanceledPage() {
  return (
    <div className="container max-w-4xl py-12">
      <div className="mb-8 flex items-center justify-center">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-amber-100">
          <AlertCircle className="h-10 w-10 text-amber-600" />
        </div>
      </div>
      
      <h1 className="mb-4 text-center text-3xl font-bold">Payment Canceled</h1>
      <p className="mb-8 text-center text-gray-600">
        Your payment process was canceled. No charges were made to your account.
      </p>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>What happened?</CardTitle>
          <CardDescription>Here's what you should know</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="space-y-4">
            <li className="flex items-start">
              <div className="mr-3 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-amber-100">
                <AlertCircle className="h-4 w-4 text-amber-600" />
              </div>
              <div>
                <p className="font-medium">Payment not completed</p>
                <p className="text-sm text-gray-500">
                  Your payment was not processed, and no charges were applied to your account.
                </p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-3 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-amber-100">
                <AlertCircle className="h-4 w-4 text-amber-600" />
              </div>
              <div>
                <p className="font-medium">Your account status</p>
                <p className="text-sm text-gray-500">
                  Your account remains on the current plan, with no changes to your features or access.
                </p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-3 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-amber-100">
                <AlertCircle className="h-4 w-4 text-amber-600" />
              </div>
              <div>
                <p className="font-medium">Try again?</p>
                <p className="text-sm text-gray-500">
                  You can try subscribing again anytime from the billing page.
                </p>
              </div>
            </li>
          </ul>
        </CardContent>
      </Card>

      <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
        <Button asChild className="w-full sm:w-auto">
          <Link href="/dashboard">
            Go to Dashboard <ChevronRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
        <Button asChild variant="outline" className="w-full sm:w-auto">
          <Link href="/dashboard/billing">
            View Billing Options
          </Link>
        </Button>
      </div>
    </div>
  );
}
