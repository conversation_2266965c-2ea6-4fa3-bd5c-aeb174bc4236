import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { Check, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { auth } from '@/lib/firebase';
// Using direct subscription calls for serverless environment
import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { SubscriptionTier } from '@/services/subscriptionService';
import { pricingPlans } from '@/lib/billing';

export const metadata: Metadata = {
  title: 'Payment Success - GroomLoop',
  description: 'Your payment has been successfully processed',
};

// Get subscription details to display on the success page
async function getSubscriptionDetails(userId: string) {
  try {
    // Get user document
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.error('User not found:', userId);
      return {
        tier: SubscriptionTier.FREE,
        isActive: false,
        expiresAt: null,
        features: []
      };
    }
    
    const userData = userDoc.data();
    const subscriptionId = userData.subscriptionId;
    
    // If no subscription, return free tier
    if (!subscriptionId) {
      return {
        tier: SubscriptionTier.FREE,
        isActive: false,
        expiresAt: null,
        features: pricingPlans.basic.features
      };
    }
    
    // Get subscription details
    const subscriptionRef = doc(db, 'subscriptions', subscriptionId);
    const subscriptionDoc = await getDoc(subscriptionRef);
    
    if (!subscriptionDoc.exists()) {
      return {
        tier: SubscriptionTier.FREE,
        isActive: false,
        expiresAt: null,
        features: pricingPlans.basic.features
      };
    }
    
    const subscription = subscriptionDoc.data();
    
    // Map subscription to tier and features
    let features: string[] = pricingPlans.basic.features;
    let tier = subscription.tier || SubscriptionTier.FREE;
    
    if (tier === SubscriptionTier.PRO) {
      features = pricingPlans.pro.features;
    } else if (tier === SubscriptionTier.ENTERPRISE) {
      features = pricingPlans.enterprise.features;
    }
    
    return {
      tier,
      isActive: subscription.status === 'active',
      expiresAt: subscription.currentPeriodEnd?.toDate() || null,
      features,
      subscription
    };
  } catch (error) {
    console.error('Error fetching subscription details:', error);
    return {
      tier: SubscriptionTier.FREE,
      isActive: false,
      expiresAt: null,
      features: pricingPlans.basic.features
    };
  }
}

export default async function PaymentSuccessPage({
  searchParams,
}: {
  searchParams: { session_id?: string; userId?: string };
}) {
  const { session_id, userId } = searchParams;

  // If no user ID or session ID, redirect to billing page
  if (!userId || !session_id) {
    redirect('/dashboard/billing');
  }

  // Get the subscription details to show the user what they purchased
  const subscriptionDetails = await getSubscriptionDetails(userId);

  return (
    <div className="container max-w-4xl py-12">
      <div className="mb-8 flex items-center justify-center">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
          <Check className="h-10 w-10 text-green-600" />
        </div>
      </div>
      
      <h1 className="mb-4 text-center text-3xl font-bold">Payment Successful!</h1>
      <p className="mb-8 text-center text-gray-600">
        Thank you for your payment. Your subscription has been activated.
      </p>

      {subscriptionDetails && (
        <Card className="mb-8 border-green-100">
          <CardHeader>
            <CardTitle>Subscription Details</CardTitle>
            <CardDescription>Your active subscription information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium">Plan</span>
                <span className="font-bold">{subscriptionDetails.tier.charAt(0).toUpperCase() + subscriptionDetails.tier.slice(1)}</span>
              </div>
              
              <div className="flex justify-between border-b pb-2">
                <span className="font-medium">Status</span>
                <span className="inline-flex items-center rounded-full bg-green-50 px-2.5 py-0.5 text-sm font-medium text-green-700">
                  Active
                </span>
              </div>
              
              {subscriptionDetails.expiresAt && (
                <div className="flex justify-between border-b pb-2">
                  <span className="font-medium">Next billing date</span>
                  <span>{new Date(subscriptionDetails.expiresAt).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>What's Next?</CardTitle>
          <CardDescription>Here's what you can do now</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="space-y-4">
            <li className="flex items-start">
              <div className="mr-3 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-green-100">
                <Check className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium">Explore your new features</p>
                <p className="text-sm text-gray-500">
                  Check out the new features available with your subscription
                </p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-3 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-green-100">
                <Check className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium">Set up your profile</p>
                <p className="text-sm text-gray-500">
                  Complete your profile to get the most out of GroomLoop
                </p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-3 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-green-100">
                <Check className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium">Manage your subscription</p>
                <p className="text-sm text-gray-500">
                  You can view and manage your subscription anytime
                </p>
              </div>
            </li>
          </ul>
        </CardContent>
      </Card>

      <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
        <Button asChild className="w-full sm:w-auto">
          <Link href="/dashboard">
            Go to Dashboard <ChevronRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
        <Button asChild variant="outline" className="w-full sm:w-auto">
          <Link href="/dashboard/billing">
            Manage Subscription
          </Link>
        </Button>
      </div>
    </div>
  );
}
