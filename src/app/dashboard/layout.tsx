'use client';

import { useEffect, useState } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { useAuth } from '@/utils/auth';
import { Toaster } from 'sonner';
import { ErrorBoundary } from '@/components/error-boundary';
import { Bell, HelpCircle } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Label,
} from "@/components/ui/label"
import { notificationService, Notification } from '@/services/notificationService';
import { addDoc, collection } from 'firebase/firestore';
import { db } from '@/utils/firebase';
import { fetchSpaces } from '@/store/slices/spaceSlice';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [helpType, setHelpType] = useState('assistance');
  const [businessType, setBusinessType] = useState('space');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loadingNotifications, setLoadingNotifications] = useState(false);
  const [currentSpaceId, setCurrentSpaceId] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      dispatch(fetchSpaces(user.uid));

      // Get current space ID from localStorage
      const spaceId = localStorage.getItem('currentSpaceId');
      setCurrentSpaceId(spaceId);
    }
  }, [dispatch, user]);

  // Fetch all notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!currentSpaceId) return;

      setLoadingNotifications(true);
      try {
        // Get all notifications for the space
        const notificationData = await notificationService.getForSpace(currentSpaceId);

        // Sort by creation date (newest first)
        const sortedNotifications = notificationData.sort((a, b) => {
          const dateA = a.createdAt instanceof Date ? a.createdAt : a.createdAt.toDate();
          const dateB = b.createdAt instanceof Date ? b.createdAt : b.createdAt.toDate();
          return dateB.getTime() - dateA.getTime();
        });

        setNotifications(sortedNotifications);
        setUnreadCount(sortedNotifications.filter(n => !n.read).length);
      } catch (e) {
        console.error('Error fetching notifications:', e);
        setNotifications([]);
        setUnreadCount(0);
      } finally {
        setLoadingNotifications(false);
      }
    };

    fetchNotifications();
  }, [currentSpaceId]);

  const handleFeedbackSubmit = async () => {
    if (!currentSpaceId) return;

    try {
      // Create a new feature request document in Firestore
      const docRef = await addDoc(collection(db, 'feature-requests'), {
        type: helpType,
        businessType,
        message: feedbackText,
        spaceId: currentSpaceId,
        status: 'new',
        createdAt: new Date().toISOString()
      });

      toast.success('Thank you for your feedback!');
      setFeedbackText('');
      setHelpDialogOpen(false);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback. Please try again.');
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!currentSpaceId) return;

    try {
      await notificationService.markAllAsRead(currentSpaceId);

      // Update local state
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      setUnreadCount(0);

      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      toast.error('Failed to mark notifications as read');
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);

      // Update local state
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  return (
    <ErrorBoundary>
      <SidebarProvider>
        <div className="flex h-screen w-screen overflow-hidden">
          <AppSidebar />
          <div className="flex-1 flex flex-col overflow-hidden">
            <header className="flex h-14 items-center justify-between border-b px-4 bg-white">
              <div className="flex items-center">
                <SidebarTrigger />
                <div className="ml-4 text-sm font-medium">GroomBook</div>
              </div>
              <div className="flex items-center space-x-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="relative">
                      <Bell className="h-5 w-5" />
                      {unreadCount > 0 && (
                        <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center text-[10px] text-white">
                          {unreadCount > 9 ? '9+' : unreadCount}
                        </span>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-80">
                    <div className="flex items-center justify-between px-4 py-2 border-b">
                      <span className="font-medium">Notifications</span>
                      <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead}>
                        Mark all as read
                      </Button>
                    </div>
                    <div className="py-2 max-h-[400px] overflow-y-auto">
                      {loadingNotifications ? (
                        <div className="text-sm text-center text-muted-foreground py-4">Loading...</div>
                      ) : notifications.length === 0 ? (
                        <div className="text-sm text-center text-muted-foreground py-4">No new notifications</div>
                      ) : (
                        notifications.map((n) => (
                          <div
                            key={n.id}
                            className={`px-4 py-2 border-b last:border-b-0 cursor-pointer hover:bg-gray-50 ${!n.read ? 'bg-blue-50' : ''}`}
                            onClick={() => handleMarkAsRead(n.id!)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="font-medium text-sm">{n.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {(n.createdAt instanceof Date ? n.createdAt : n.createdAt.toDate()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </div>
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">{n.message}</div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {(n.createdAt instanceof Date ? n.createdAt : n.createdAt.toDate()).toLocaleDateString()}
                            </div>
                            {!n.read && (
                              <div className="mt-1">
                                <span className="inline-block h-2 w-2 bg-blue-500 rounded-full mr-1"></span>
                                <span className="text-xs text-blue-500">New</span>
                              </div>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setHelpDialogOpen(true)}
                >
                  <HelpCircle className="h-5 w-5" />
                </Button>
              </div>
            </header>
            <main className="flex-1 overflow-y-auto bg-gray-50">
              {children}

            </main>
          </div>
        </div>
        <Dialog open={helpDialogOpen} onOpenChange={setHelpDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>How can we help?</DialogTitle>
              <DialogDescription>
                Let us know what type of assistance you need. We&apos;ll get back to you as soon as possible.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>Business Type</Label>
                <Select value={businessType} onValueChange={setBusinessType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="space">Service Space</SelectItem>
                    <SelectItem value="salon">Salon</SelectItem>
                    <SelectItem value="barbershop">Barbershop</SelectItem>
                    <SelectItem value="wellness">Wellness Center</SelectItem>
                    <SelectItem value="fitness">Fitness Studio</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Request Type</Label>
                <Select value={helpType} onValueChange={setHelpType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select request type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="assistance">General Assistance</SelectItem>
                    <SelectItem value="feature">Feature Request</SelectItem>
                    <SelectItem value="bug">Report a Bug</SelectItem>
                    <SelectItem value="feedback">General Feedback</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Message</Label>
                <Textarea
                  placeholder={
                    helpType === 'assistance' ? "Describe what you need help with..." :
                    helpType === 'feature' ? "Describe the feature you'd like to see..." :
                    helpType === 'bug' ? "Describe the issue you're experiencing..." :
                    "Share your feedback..."
                  }
                  value={feedbackText}
                  onChange={(e) => setFeedbackText(e.target.value)}
                  className="min-h-[150px]"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setHelpDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleFeedbackSubmit} disabled={!feedbackText.trim()}>
                Submit
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        <Toaster />
      </SidebarProvider>
    </ErrorBoundary>
  );
}