"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useAppSelector } from "@/store/hooks"
import { toast } from "sonner"
import { db } from "@/utils/firebase"
import { collection, query, where, getDocs, updateDoc, doc, addDoc, deleteDoc } from "firebase/firestore"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"

interface InventoryItem {
  id: string
  name: string
  type: string
  quantity: number
  thresholdLow: number
  thresholdCritical: number
  status?: "ok" | "low" | "critical"
  lastUpdated: string
  spaceId: string
}

export default function InventoryPage() {
  const currentSpace = useAppSelector((state) => state.space.currentSpace)
  const [items, setItems] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(false)
  const [updating, setUpdating] = useState<string | null>(null)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [newItem, setNewItem] = useState({
    name: '',
    type: '',
    quantity: 0,
    thresholdLow: 5,
    thresholdCritical: 2,
  })
  const [adding, setAdding] = useState(false)

  useEffect(() => {
    if (!currentSpace) return
    setLoading(true)
    const fetchItems = async () => {
      const q = query(collection(db, "inventory"), where("spaceId", "==", currentSpace.id))
      const snapshot = await getDocs(q)
      const data = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as InventoryItem[]
      // Calculate status if not present
      const withStatus = data.map(item => ({
        ...item,
        status: getStatus(item.quantity, item.thresholdLow, item.thresholdCritical)
      }))
      setItems(withStatus)
      setLoading(false)
    }
    fetchItems()
  }, [currentSpace])

  const getStatus = (quantity: number, low: number, critical: number): "ok" | "low" | "critical" => {
    if (quantity <= critical) return "critical"
    if (quantity <= low) return "low"
    return "ok"
  }

  const getStatusColor = (status: string) => {
    if (status === "critical") return "bg-red-100 text-red-700"
    if (status === "low") return "bg-yellow-100 text-yellow-700"
    return "bg-green-100 text-green-700"
  }

  const handleUpdateQuantity = async (id: string, quantity: number, item: InventoryItem) => {
    setUpdating(id)
    try {
      const status = getStatus(quantity, item.thresholdLow, item.thresholdCritical)
      const itemRef = doc(db, "inventory", id)
      await updateDoc(itemRef, { quantity, status, lastUpdated: new Date().toISOString() })
      setItems(items => items.map(i => i.id === id ? { ...i, quantity, status } : i))
      toast.success("Quantity updated")
    } catch (_) {
      toast.error("Failed to update quantity")
    } finally {
      setUpdating(null)
    }
  }

  const handleAddItem = async () => {
    if (!currentSpace) return
    setAdding(true)
    try {
      const status = getStatus(newItem.quantity, newItem.thresholdLow, newItem.thresholdCritical)
      const docRef = await addDoc(collection(db, "inventory"), {
        ...newItem,
        status,
        lastUpdated: new Date().toISOString(),
        spaceId: currentSpace.id,
      })
      setItems(items => [
        ...items,
        {
          id: docRef.id,
          ...newItem,
          status,
          lastUpdated: new Date().toISOString(),
          spaceId: currentSpace.id,
        }
      ])
      setAddDialogOpen(false)
      setNewItem({ name: '', type: '', quantity: 0, thresholdLow: 5, thresholdCritical: 2 })
      toast.success("Item added")
    } catch (_) {
      toast.error("Failed to add item")
    } finally {
      setAdding(false)
    }
  }

  const handleDeleteItem = async (id: string) => {
    try {
      await deleteDoc(doc(db, "inventory", id))
      setItems(items => items.filter(i => i.id !== id))
      toast.success("Item deleted")
    } catch (_) {
      toast.error("Failed to delete item")
    }
  }

  return (
    <div className="p-4 sm:p-8">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between mb-6 sm:space-y-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold">Inventory</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Track and manage your salon supplies
          </p>
        </div>
        <Button onClick={() => setAddDialogOpen(true)} className="w-full sm:w-auto">Add Item</Button>
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : items.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <div className="text-lg font-semibold mb-2">No inventory items found.</div>
          <div className="mb-4 text-muted-foreground">Start by adding your first product to keep track of your supplies.</div>
          <Button onClick={() => setAddDialogOpen(true)}>Add Inventory Item</Button>
        </div>
      ) : (
        <div className="space-y-4">
          {items.map(item => (
            <div key={item.id} className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border rounded-md">
              <div className="flex-1">
                <div className="font-semibold">{item.name}</div>
                <div className="text-sm text-muted-foreground">{item.type}</div>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center gap-4 w-full sm:w-auto">
                <div className={`px-3 py-1 rounded-full text-xs font-medium self-start sm:self-center ${getStatusColor(item.status || 'ok')}`}>
                  {(item.status || 'OK').toUpperCase()}
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    className="w-20 sm:w-24"
                    value={item.quantity}
                    min={0}
                    onChange={e => handleUpdateQuantity(item.id, Number(e.target.value), item)}
                    disabled={updating === item.id}
                  />
                  <Button variant="destructive" size="sm" onClick={() => handleDeleteItem(item.id)} title="Delete item">
                    &times;
                  </Button>
                </div>
                <span className="text-xs text-muted-foreground">
                  Last updated: {new Date(item.lastUpdated).toLocaleString()}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Add Inventory Item</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <label className="block text-sm font-medium">Product Name</label>
            <div className="text-xs text-muted-foreground mb-1">The name of the product (e.g. Shampoo, Baby Powder).</div>
            <Input
              placeholder="Product Name"
              value={newItem.name}
              onChange={e => setNewItem({ ...newItem, name: e.target.value })}
            />
            <label className="block text-sm font-medium">Type (e.g. Cream, Powder)</label>
            <div className="text-xs text-muted-foreground mb-1">The category or type of product (e.g. Cream, Powder, Spray).</div>
            <Input
              placeholder="Type (e.g. Cream, Powder)"
              value={newItem.type}
              onChange={e => setNewItem({ ...newItem, type: e.target.value })}
            />
            <label className="block text-sm font-medium">Quantity</label>
            <div className="text-xs text-muted-foreground mb-1">How many units of this product you currently have in stock.</div>
            <Input
              type="number"
              placeholder="Quantity"
              value={newItem.quantity}
              min={0}
              onChange={e => setNewItem({ ...newItem, quantity: Number(e.target.value) })}
            />
            <label className="block text-sm font-medium">Low Stock Threshold</label>
            <div className="text-xs text-muted-foreground mb-1">When the quantity drops to this number, the product will be marked as low stock and you will get a warning.</div>
            <Input
              type="number"
              placeholder="Low Stock Threshold"
              value={newItem.thresholdLow}
              min={1}
              onChange={e => setNewItem({ ...newItem, thresholdLow: Number(e.target.value) })}
            />
            <label className="block text-sm font-medium">Critical Stock Threshold</label>
            <div className="text-xs text-muted-foreground mb-1">When the quantity drops to this number, the product will be marked as critical and you will get an urgent alert.</div>
            <Input
              type="number"
              placeholder="Critical Stock Threshold"
              value={newItem.thresholdCritical}
              min={0}
              onChange={e => setNewItem({ ...newItem, thresholdCritical: Number(e.target.value) })}
            />
          </div>
          <DialogFooter>
            <Button onClick={handleAddItem} disabled={adding}>
              {adding ? "Adding..." : "Add Item"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 