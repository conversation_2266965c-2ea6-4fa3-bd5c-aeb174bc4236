'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';
import { marketingService } from '@/services/marketingService';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import { Download, PieChart, TrendingUp } from 'lucide-react';
import { useAuth } from '@/utils/auth';

const MARKETING_BENEFITS = [
  "Send targeted marketing campaigns to your customers",
  "Track campaign effectiveness and conversion rates",
  "Create promotional codes and special offers",
  "Analyze customer acquisition channels",
  "Export marketing data for external analysis"
];

function MarketingDashboardContent() {
  const { user } = useAuth();
  const [marketingData, setMarketingData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchMarketingData = async () => {
      if (!user?.uid) return;
      
      try {
        setIsLoading(true);
        // Assuming the user has a default space ID stored in their profile
        const spaceId = user.uid; // Use appropriate space ID
        const summary = await marketingService.getSummary(spaceId);
        setMarketingData(summary);
      } catch (error) {
        console.error('Error fetching marketing data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMarketingData();
  }, [user]);

  const handleExportData = async () => {
    if (!user?.uid) return;
    
    try {
      const spaceId = user.uid; // Use appropriate space ID
      const csvData = await marketingService.exportMarketingDataCSV(spaceId);
      
      // Create a downloadable blob
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.setAttribute('hidden', '');
      a.setAttribute('href', url);
      a.setAttribute('download', 'marketing-data.csv');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-8">
      <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Marketing Dashboard</h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            Track customer acquisition and campaign performance
          </p>
        </div>
        <Button variant="outline" size="sm" onClick={handleExportData} className="w-full sm:w-auto">
          <Download className="mr-2 h-4 w-4" /> Export Data
        </Button>
      </div>

      <Tabs defaultValue="sources">
        <TabsList className="w-full overflow-x-auto items-center justify-start">
          <TabsTrigger value="sources" className="whitespace-nowrap">Customer Sources</TabsTrigger>
          <TabsTrigger value="campaigns" className="whitespace-nowrap">Campaigns</TabsTrigger>
        </TabsList>
        
        <TabsContent value="sources" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Acquisition Sources</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">Loading data...</p>
                </div>
              ) : !marketingData || !marketingData.sources || marketingData.sources.length === 0 ? (
                <div className="h-[300px] flex flex-col items-center justify-center text-center">
                  <PieChart className="h-12 w-12 text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">No data available</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Start tracking where your customers come from by adding marketing sources to your booking forms.
                  </p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart 
                    data={marketingData.sources} 
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <XAxis dataKey="source" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Marketing Campaigns</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex flex-col items-center justify-center text-center">
                <TrendingUp className="h-12 w-12 text-muted-foreground mb-2" />
                <h3 className="text-lg font-medium">Coming Soon</h3>
                <p className="text-sm text-muted-foreground max-w-md">
                  The ability to create and track marketing campaigns will be available in the next update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function MarketingDashboard() {
  return (
    <FeatureGate
      feature={RestrictedFeature.MARKETING}
      title="Marketing Tools"
      description="Track and optimize your marketing efforts to grow your business."
      benefits={MARKETING_BENEFITS}
    >
      <MarketingDashboardContent />
    </FeatureGate>
  );
}