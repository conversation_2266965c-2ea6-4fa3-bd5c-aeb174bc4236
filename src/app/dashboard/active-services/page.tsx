'use client'

import { useState, useEffect } from "react"
import { useRouter } from 'next/navigation';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Plus, Clock, User2, Store, Scissors, CheckCircle2 } from "lucide-react"
import { ActionMenu } from "@/components/ui/action-menu"
import { toast } from "sonner"
import { useAppSelector } from "@/store/hooks"
import { Service, Staff, Customer } from "@/services/types/models"
import { customerService, staffService, serviceService } from "@/services/firestore"
import { notificationService } from "@/services/notificationService"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CustomerForm } from "@/components/CustomerForm"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { activeServiceService } from "@/services/firestore"
import { formatCurrency } from '@/utils/currency';
import { formatPrice } from "@/utils/price-formatter"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ServiceReviewDialog } from '@/components/ServiceReviewDialog';

type ServiceStatus = 'in_progress' | 'completed' | 'awaiting_payment' | 'paid' | 'cancelled'

interface ActiveService {
  id: string
  customerId: string
  customerName: string
  customerPhone: string
  staffId: string
  staffName: string
  services: {
    id: string
    name: string
    price: number
    duration: number
  }[]
  status: ServiceStatus
  startTime: Date
  endTime?: Date
  totalAmount: number
  progress: number
  notes?: string
  staffAvatar: string
  spaceId: string
}

export default function ActiveServicesPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [newServiceDialogOpen, setNewServiceDialogOpen] = useState(false)
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false)
  const [customerSearchValue, setCustomerSearchValue] = useState("")
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [selectedStaff, setSelectedStaff] = useState("")
  const [selectedServices, setSelectedServices] = useState<string[]>([])
  const currentSpace = useAppSelector((state) => state.space.currentSpace)

  // State for available data
  const [activeServices, setActiveServices] = useState<ActiveService[]>([])
  const [availableServices, setAvailableServices] = useState<Service[]>([])
  const [availableStaff, setAvailableStaff] = useState<Staff[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [filteredServices, setFilteredServices] = useState<Service[]>([])
  const [isCompleteDialogOpen, setIsCompleteDialogOpen] = useState(false)
  const [selectedService, setSelectedService] = useState<ActiveService | null>(null)
  const [completionNotes, setCompletionNotes] = useState("")
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false)

  const formatAmount = (value: number) => {
    return formatPrice(value, currentSpace);
  };

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true)

        if (!currentSpace) {
          toast.error("No space selected. Please select a space first.")
          setIsLoading(false)
          return
        }

        const [staffData, servicesData, customersData, activeServicesData] = await Promise.all([
          staffService.getBySpace(currentSpace.id),
          serviceService.getBySpace(currentSpace.id),
          customerService.getAll(), 
          activeServiceService.getInProgressBySpace(currentSpace.id)
        ])

        // Log active services for debugging
        console.log("Active services loaded from Firestore:", activeServicesData);

        setAvailableStaff(staffData)
        setAvailableServices(servicesData)
        setCustomers(customersData)
        setActiveServices(activeServicesData)
      } catch (error) {
        console.error('Error loading data:', error)
        toast.error('Failed to load data')
      } finally {
        setIsLoading(false)
      }
    }

    if (currentSpace) {
      loadData()
    }
  }, [currentSpace])

  // Add progress update interval
  useEffect(() => {
    // Function to calculate progress for a service
    const calculateProgress = (service: ActiveService) => {
      const now = new Date()
      const startTime = new Date(service.startTime)
      const totalDuration = service.services.reduce((total, s) => total + s.duration, 0)
      const elapsedMinutes = (now.getTime() - startTime.getTime()) / (1000 * 60)
      return Math.min(Math.round((elapsedMinutes / totalDuration) * 100), 100)
    }

    // Update progress every minute
    const intervalId = setInterval(() => {
      setActiveServices(prevServices =>
        prevServices.map(service => ({
          ...service,
          progress: calculateProgress(service)
        }))
      )
    }, 60000) // Update every minute

    // Initial progress calculation
    setActiveServices(prevServices =>
      prevServices.map(service => ({
        ...service,
        progress: calculateProgress(service)
      }))
    )

    return () => clearInterval(intervalId)
  }, [])

  // Filter services based on selected staff
  useEffect(() => {
    if (!selectedStaff) {
      setFilteredServices(availableServices)
      return
    }

    const staff = availableStaff.find(s => s.id === selectedStaff)
    if (!staff) {
      setFilteredServices([])
      return
    }

    const staffServices = availableServices.filter(service =>
      staff.services.includes(service.id)
    )
    setFilteredServices(staffServices)
  }, [selectedStaff, availableServices, availableStaff])

  const handleStartService = async () => {
    try {
      setIsLoading(true)

      // Validate inputs
      if (!selectedCustomer || !selectedStaff || selectedServices.length === 0) {
        toast.error("Please fill in all required fields")
        return
      }

      // Calculate total duration for progress tracking (will be used in future implementation)
      // const totalDuration = selectedServices.reduce((total, id) => {
      //   const service = availableServices.find(s => s.id === id)!
      //   return total + service.duration
      // }, 0)

      // Create new active service
      const newService: ActiveService = {
        id: Math.random().toString(36).substring(2, 11), // This will be replaced by Firestore
        customerId: selectedCustomer.id,
        customerName: selectedCustomer.displayName,
        customerPhone: selectedCustomer.phoneNumber,
        staffId: selectedStaff,
        staffName: availableStaff.find(s => s.id === selectedStaff)?.displayName || '',
        services: selectedServices.map(id => {
          const service = availableServices.find(s => s.id === id)!
          return {
            id: service.id,
            name: service.name,
            price: service.price,
            duration: service.duration
          }
        }),
        status: 'in_progress',
        startTime: new Date(),
        totalAmount: selectedServices.reduce((total, id) => {
          const service = availableServices.find(s => s.id === id)!
          return total + service.price
        }, 0),
        progress: 0,
        staffAvatar: availableStaff.find(s => s.id === selectedStaff)?.photoURL || '',
        spaceId: currentSpace?.id || ''
      }

      // Save to Firestore
      await activeServiceService.create(newService)

      // Refresh active services
      const updatedActiveServices = await activeServiceService.getInProgress()
      setActiveServices(updatedActiveServices)

      setNewServiceDialogOpen(false)
      resetForm()
      toast.success("Service started successfully")
    } catch (error) {
      toast.error("Failed to start service")
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }



  const handleCompleteService = async (service: ActiveService) => {
    try {
      if (!service?.id) {
        toast.error("Invalid service ID");
        return;
      }

      console.log("Attempting to complete service with ID:", service.id);

      // First, verify the service exists
      let serviceId = service.id;
      let serviceExists = await activeServiceService.exists(serviceId);

      // If service doesn't exist with the given ID, try to find it by properties
      if (!serviceExists) {
        console.log(`Service with ID ${serviceId} not found in Firestore. Attempting to find by properties...`);

        // Try to find the service by its properties
        const foundServiceId = await activeServiceService.findServiceByProperties(service);

        if (foundServiceId) {
          console.log(`Found matching service with different ID: ${foundServiceId}`);
          serviceId = foundServiceId;
          serviceExists = true;

          // Show a warning to the user
          toast.warning("Service ID mismatch detected. Using the correct ID from the database.");
        } else {
          console.error(`Could not find any matching service in Firestore`);
          toast.error("This service could not be found in the database. Refreshing data...");

          // Refresh active services
          const updatedActiveServices = await activeServiceService.getInProgress();
          setActiveServices(updatedActiveServices);

          // Close dialog and reset state
          setIsCompleteDialogOpen(false);
          setSelectedService(null);
          setCompletionNotes("");
          return;
        }
      } else {
        console.log(`Service with ID ${serviceId} found in Firestore`);
      }

      // Update service status in Firestore using the correct ID
      await activeServiceService.update(serviceId, {
        status: "awaiting_payment",  // Change to awaiting_payment instead of completed
        endTime: new Date(),
        notes: completionNotes
      });

      // Create a notification for the service completion
      try {
        // Get the primary service name (first service in the list)
        const primaryServiceName = service.services[0]?.name || "Service";

        // Create notification
        await notificationService.createServiceCompletionNotification(
          service.spaceId,
          serviceId,
          service.customerName,
          primaryServiceName,
          service.staffName,
          service.totalAmount
        );

        console.log("Service completion notification created");
      } catch (notificationError) {
        console.error("Error creating service completion notification:", notificationError);
        // Don't fail the whole operation if notification creation fails
      }

      // Refresh active services
      const updatedActiveServices = await activeServiceService.getInProgress();
      setActiveServices(updatedActiveServices);

      // Close dialog and reset state
      setIsCompleteDialogOpen(false);
      setSelectedService(null);
      setCompletionNotes("");

      toast.success("Service completed successfully");

      // Notify the user to check the history tab
      toast.info("Service is now in the History tab under 'Awaiting Payment'", {
        duration: 5000
      });
    } catch (error) {
      console.error('Error completing service:', error);

      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);

        if (error.message.includes('does not exist')) {
          toast.error("This service no longer exists. It may have been deleted.");

          // Try to get more information about the service
          console.log("Service that failed to update:", service);

          // Refresh the list to remove the non-existent service
          const updatedActiveServices = await activeServiceService.getInProgress();
          setActiveServices(updatedActiveServices);
        } else {
          toast.error(`Failed to complete service: ${error.message}`);
        }
      } else {
        toast.error("Failed to complete service due to an unknown error");
      }
    }
  };



  const handleDeleteActiveService = async (id: string) => {
    try {
      await activeServiceService.delete(id)

      // Remove from local state
      setActiveServices(prev => prev.filter(service => service.id !== id))

      toast.success("Service deleted successfully")
    } catch (error) {
      console.error('Error deleting active service:', error)
      toast.error("Failed to delete service")
    }
  }

  const resetForm = () => {
    setSelectedCustomer(null)
    setCustomerSearchValue("")
    setSelectedStaff("")
    setSelectedServices([])
  }

  const filteredActiveServices = activeServices.filter(service =>
    service.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.customerPhone.includes(searchTerm)
  )

  return (
    <div className="h-full flex-1 space-y-4 sm:space-y-8 p-4 sm:p-8">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Active Services</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Manage ongoing services and payments
          </p>
        </div>
        <Dialog open={newServiceDialogOpen} onOpenChange={setNewServiceDialogOpen}>
          <DialogTrigger asChild>
            <Button className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              New Service
            </Button>
          </DialogTrigger>
          <DialogContent className="w-[95vw] max-w-[500px] sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Start New Service</DialogTitle>
              <DialogDescription>
                Enter customer details and select services to begin
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>Customer</Label>
                <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={customerSearchOpen}
                      className="justify-between"
                    >
                      {selectedCustomer ? (
                        <div className="flex items-center gap-2">
                          <User2 className="h-4 w-4 text-muted-foreground" />
                          <span>{selectedCustomer.displayName}</span>
                          <span className="text-muted-foreground">
                            ({selectedCustomer.phoneNumber})
                          </span>
                        </div>
                      ) : (
                        "Select customer..."
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0">
                    <Command>
                      <CommandInput
                        placeholder="Search customers..."
                        value={customerSearchValue}
                        onValueChange={setCustomerSearchValue}
                      />
                      <CommandEmpty>
                        <div className="flex flex-col items-center justify-center py-6 gap-2">
                          <User2 className="h-8 w-8 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">No customers found</p>
                          <CustomerForm
                            onSuccess={(customer) => {
                              setSelectedCustomer(customer)
                              setCustomerSearchOpen(false)
                            }}
                          />
                        </div>
                      </CommandEmpty>
                      <CommandGroup>
                        {customers
                          .filter(customer =>
                            customer.displayName.toLowerCase().includes(customerSearchValue.toLowerCase()) ||
                            customer.phoneNumber.includes(customerSearchValue)
                          )
                          .map(customer => (
                            <CommandItem
                              key={customer.id}
                              value={customer.id}
                              onSelect={() => {
                                setSelectedCustomer(customer)
                                setCustomerSearchOpen(false)
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <User2 className="h-4 w-4 text-muted-foreground" />
                                <span>{customer.displayName}</span>
                                <span className="text-muted-foreground">
                                  ({customer.phoneNumber})
                                </span>
                              </div>
                            </CommandItem>
                          ))
                        }
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="staff">Select Staff</Label>
                <Select value={selectedStaff} onValueChange={setSelectedStaff}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableStaff.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-6 gap-2">
                        <Store className="h-8 w-8 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground">No staff members available</p>
                      </div>
                    ) : (
                      availableStaff.map((staff) => (
                        <SelectItem key={staff.id} value={staff.id}>
                          {staff.displayName}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label>Select Services</Label>
                <ScrollArea className="h-[200px] rounded-md border p-4">
                  {filteredServices.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-6 gap-2">
                      <Scissors className="h-8 w-8 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">
                        {selectedStaff
                          ? "No services available for selected staff member"
                          : "Select a staff member to view available services"
                        }
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {filteredServices.map((service) => (
                        <div key={service.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={service.id}
                            checked={selectedServices.includes(service.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedServices([...selectedServices, service.id])
                              } else {
                                setSelectedServices(selectedServices.filter(id => id !== service.id))
                              }
                            }}
                            className="h-4 w-4 rounded border-gray-300"
                          />
                          <label htmlFor={service.id} className="flex-1 text-sm">
                            {service.name}
                          </label>
                          <span className="text-sm text-muted-foreground">
                            {formatAmount(service.price)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setNewServiceDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleStartService} disabled={isLoading}>
                {isLoading ? "Starting..." : "Start Service"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search by customer name or phone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:max-w-sm"
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Current Services</CardTitle>
          <CardDescription>
            View and manage ongoing services
          </CardDescription>
        </CardHeader>
        <CardContent>
          {activeServices.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 gap-3">
              <Clock className="h-12 w-12 text-muted-foreground" />
              <h3 className="font-medium text-lg">No active services</h3>
              <p className="text-sm text-muted-foreground">
                Start a new service by clicking the &quot;New Service&quot; button above.
              </p>
            </div>
          ) : (
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {filteredActiveServices.map((service) => (
                <Card key={service.id}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{service.services[0].name}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            service.status === "completed"
                              ? "default"
                              : service.status === "in_progress"
                              ? "secondary"
                              : "destructive"
                          }
                        >
                          {service.status === "in_progress"
                            ? "In Progress"
                            : service.status === "completed"
                            ? "Completed"
                            : "Cancelled"}
                        </Badge>
                        <ActionMenu
                          onDelete={() => handleDeleteActiveService(service.id)}
                          deleteLabel="Delete"
                          deleteDialogTitle="Delete Active Service"
                          deleteDialogDescription={`Are you sure you want to delete this active service for ${service.customerName}?`}
                        />
                      </div>
                    </div>
                    <CardDescription>{service.customerName}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarImage src={service.staffAvatar} />
                        <AvatarFallback>
                          {service.staffName.split(" ").map((n) => n[0]).join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{service.staffName}</p>
                        <p className="text-sm text-muted-foreground">Staff</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>
                            Started at {new Date(service.startTime).toLocaleTimeString()}
                          </span>
                        </div>
                        <span>{service.services[0].duration} min</span>
                      </div>
                      <Progress value={service.progress} className="h-2" />
                    </div>

                    {service.status === "in_progress" && (
                      <Button
                        className="w-full"
                        onClick={() => {
                          setSelectedService(service)
                          setIsCompleteDialogOpen(true)
                        }}
                      >
                        <CheckCircle2 className="mr-2 h-4 w-4" />
                        Complete Service
                      </Button>
                    )}

                    {service.notes && (
                      <div className="rounded-lg bg-muted p-3">
                        <p className="text-sm font-medium">Notes</p>
                        <p className="text-sm text-muted-foreground">{service.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isCompleteDialogOpen} onOpenChange={setIsCompleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Complete Service</DialogTitle>
            <DialogDescription>
              Add any notes or comments about the service completion
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Textarea
                placeholder="Add completion notes (optional)"
                value={completionNotes}
                onChange={(e) => setCompletionNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsCompleteDialogOpen(false)
                setSelectedService(null)
                setCompletionNotes("")
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => selectedService && handleCompleteService(selectedService)}
            >
              Complete Service
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Review Dialog */}
      {selectedService && (
        <ServiceReviewDialog
          open={reviewDialogOpen}
          onOpenChange={setReviewDialogOpen}
          activeServiceId={selectedService.id}
          staffId={selectedService.staffId}
          staffName={selectedService.staffName}
          customerId={selectedService.customerId}
          customerName={selectedService.customerName}
          spaceId={selectedService.spaceId}
          serviceIds={selectedService.services.map(s => s.id)}
          serviceDate={selectedService.startTime.toISOString()}
          onSuccess={() => {
            setReviewDialogOpen(false)
            setSelectedService(null)
          }}
        />
      )}
    </div>
  )
}