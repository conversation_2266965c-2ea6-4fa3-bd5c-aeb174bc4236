"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { db } from "@/utils/firebase"
import { collection, getDocs, orderBy, query } from "firebase/firestore"
import { <PERSON><PERSON>, <PERSON>etContent, SheetHeader, SheetTitle, SheetFooter } from "@/components/ui/sheet"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MultiSelect } from "@/components/ui/multiselect"

interface Campaign {
  id: string
  campaignName: string
  message: string
  recipients: string[]
  sentAt: string
  status?: string
}

interface Person {
  id: string
  displayName: string
  phoneNumber: string
}

export default function MarketingCampaignPage() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(false)
  const [sheetOpen, setSheetOpen] = useState(false)
  const [campaignName, setCampaignName] = useState("")
  const [message, setMessage] = useState("")
  const [target, setTarget] = useState<'customers' | 'staff'>('customers')
  const [recipients, setRecipients] = useState<string[]>([])
  const [customerOptions, setCustomerOptions] = useState<Person[]>([])
  const [staffOptions, setStaffOptions] = useState<Person[]>([])
  const [sending, setSending] = useState(false)

  useEffect(() => {
    setLoading(true)
    const fetchCampaigns = async () => {
      const q = query(collection(db, "campaigns"), orderBy("sentAt", "desc"))
      const snapshot = await getDocs(q)
      const data = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Campaign[]
      setCampaigns(data)
      setLoading(false)
    }
    fetchCampaigns()
  }, [])

  useEffect(() => {
    if (!sheetOpen) return;
    const fetchPeople = async () => {
      const customerSnap = await getDocs(collection(db, "customers"));
      setCustomerOptions(customerSnap.docs.map(doc => ({
        id: doc.id,
        displayName: doc.data().displayName,
        phoneNumber: doc.data().phoneNumber
      })));
      const staffSnap = await getDocs(collection(db, "staff"));
      setStaffOptions(staffSnap.docs.map(doc => ({
        id: doc.id,
        displayName: doc.data().displayName,
        phoneNumber: doc.data().phoneNumber
      })));
    };
    fetchPeople();
  }, [sheetOpen]);

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault()
    setSending(true)
    try {
      if (!message || recipients.length === 0) {
        toast.error("Please enter a message and select at least one recipient.")
        setSending(false)
        return
      }
      const res = await fetch("/api/send-marketing-campaign", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ campaignName, message, recipients })
      })
      if (!res.ok) throw new Error("Failed to send campaign")
      const newCampaign: Campaign = {
        id: Math.random().toString(36).slice(2),
        campaignName,
        message,
        recipients,
        sentAt: new Date().toISOString(),
        status: "Sent"
      }
      setCampaigns(campaigns => [newCampaign, ...campaigns])
      setSheetOpen(false)
      setCampaignName("")
      setMessage("")
      setRecipients([])
      toast.success("Campaign sent successfully!")
    } catch (e) {
      toast.error("Failed to send campaign")
    } finally {
      setSending(false)
    }
  }

  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Marketing Campaigns</h1>
        <Button onClick={() => setSheetOpen(true)}>New Campaign</Button>
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : campaigns.length === 0 ? (
        <div className="text-center text-muted-foreground py-16">No campaigns sent yet.</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full border text-sm">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-4 py-2 text-left">Campaign Name</th>
                <th className="px-4 py-2 text-left">Message</th>
                <th className="px-4 py-2 text-left">Recipients</th>
                <th className="px-4 py-2 text-left">Date Sent</th>
                <th className="px-4 py-2 text-left">Status</th>
              </tr>
            </thead>
            <tbody>
              {campaigns.map(c => (
                <tr key={c.id} className="border-b">
                  <td className="px-4 py-2 font-medium">{c.campaignName}</td>
                  <td className="px-4 py-2 max-w-xs truncate" title={c.message}>{c.message}</td>
                  <td className="px-4 py-2">{c.recipients.length}</td>
                  <td className="px-4 py-2">{new Date(c.sentAt).toLocaleString()}</td>
                  <td className="px-4 py-2">{c.status || "Sent"}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
        <SheetContent side="right" className="sm:max-w-md w-full">
          <SheetHeader>
            <SheetTitle>New Marketing Campaign</SheetTitle>
          </SheetHeader>
          <form onSubmit={handleSubmit} className="space-y-6 mt-4">
            <div>
              <label className="block text-sm font-medium mb-1">Target</label>
              <Select value={target} onValueChange={v => setTarget(v as 'customers' | 'staff')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customers">Customers</SelectItem>
                  <SelectItem value="staff">Staff</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Campaign Name</label>
              <Input
                placeholder="e.g. Weekend Promo"
                value={campaignName}
                onChange={e => setCampaignName(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Message</label>
              <Textarea
                placeholder="Enter your marketing message..."
                value={message}
                onChange={e => setMessage(e.target.value)}
                className="min-h-[120px]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Recipients</label>
              <MultiSelect
                options={(target === 'customers' ? customerOptions : staffOptions).map(p => ({
                  value: p.phoneNumber,
                  label: `${p.displayName} (${p.phoneNumber})`
                }))}
                value={recipients}
                onChange={setRecipients}
                placeholder={`Select ${target === 'customers' ? 'customers' : 'staff'}...`}
              />
            </div>
            <SheetFooter>
              <Button type="submit" disabled={sending}>
                {sending ? "Sending..." : "Send Campaign"}
              </Button>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>
    </div>
  )
} 