'use client'

import { useState, useEffect } from "react"
import { useAppSelector, useAppDispatch } from "@/store/hooks"
import {
  fetchAllStaff,
  createStaff,
  updateStaff,
  setSelectedStaff,
  selectAllStaff,
  selectSelectedStaff,
  selectStaffLoading
} from "@/store/slices/staffSlice"
import { Button } from "@/components/ui/button"
import { Plus, Users, Check, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { serviceService, activeServiceService } from "@/services/firestore"
import { Service, ActiveService, Staff } from "@/services/types/models"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { staffAuthService } from "@/services/staffAuthService"

// Import our new components
import { ServicesTab } from "./components/ServicesTab"
import { StaffTab } from "./components/StaffTab"
import { HistoryTab } from "./components/HistoryTab"

export default function ServicesPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [services, setServices] = useState<Service[]>([])
  const [completedServices, setCompletedServices] = useState<ActiveService[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddStaffDialogOpen, setIsAddStaffDialogOpen] = useState(false)
  const [selectedServices, setSelectedServices] = useState<string[]>([])

  // Redux staff state
  const dispatch = useAppDispatch()
  const staff = useAppSelector(selectAllStaff)
  const selectedStaff = useAppSelector(selectSelectedStaff)
  const isStaffLoading = useAppSelector(selectStaffLoading)
  const [isAddServiceDialogOpen, setIsAddServiceDialogOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [currentServiceId, setCurrentServiceId] = useState<string | null>(null)
  const [newService, setNewService] = useState({
    name: "",
    description: "",
    category: "",
    price: "",
    duration: "",
    status: "Active" as "Active" | "Inactive"
  })
  const [newStaff, setNewStaff] = useState({
    displayName: "",
    email: "",
    phoneNumber: "",
    commission: "",
    bio: "",
    photoURL: "",
    experience: {
      years: 0,
      since: new Date().toISOString().split('T')[0],
      previousWorkplaces: [] as string[]
    },
    certifications: [] as string[]
  })
  const [isServicesOpen, setIsServicesOpen] = useState(false)
  const [newStaffPin, setNewStaffPin] = useState<string | null>(null)
  const { currentSpace } = useAppSelector(state => state.space)

  // Add timeout to prevent infinite loading
  useEffect(() => {
    // Set a timeout to force the loading state to false after 10 seconds
    const timeoutId = setTimeout(() => {
      if (isLoading) {
        console.log("Force stopping loading state after timeout");
        setIsLoading(false);
      }
    }, 10000); // 10 seconds timeout
    
    return () => clearTimeout(timeoutId);
  }, [isLoading]);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true)

        if (!currentSpace) {
          console.log("No space selected, skipping data load")
          setIsLoading(false)
          return
        }

        // Fetch staff using Redux
        dispatch(fetchAllStaff())
        
        // Use either space or salon ID for backward compatibility
        const spaceId = currentSpace?.id 

        // Fetch services for the current space
        const spaceServices = await serviceService.getBySpace(spaceId)
        // Ensure we set services to an empty array if result is undefined
        setServices(spaceServices || [])

        // Fetch completed services for the current space
        try {
          console.log("Loading completed services for space:", spaceId)
          const completedServicesData = await activeServiceService.getCompletedBySpace(spaceId)
          console.log("Completed services loaded:", completedServicesData)

          if (completedServicesData.length === 0) {
            console.log("No completed services found for this space")
          }

          setCompletedServices(completedServicesData)
        } catch (completedError) {
          console.error('Error loading completed services:', completedError)
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Error loading data:', error)
        setIsLoading(false)
      }
    }

    if (currentSpace) {
      loadData()
    }
  }, [dispatch, currentSpace])

  const handleAddOrUpdateStaff = async () => {
    try {
      // Validate form
      if (!newStaff.displayName || !newStaff.email || !newStaff.phoneNumber || !newStaff.commission || selectedServices.length === 0) {
        toast.error("Please fill in all required fields and select at least one service")
        return
      }

      const baseStaffData = {
        displayName: newStaff.displayName,
        email: newStaff.email,
        phoneNumber: newStaff.phoneNumber,
        commission: Number(newStaff.commission),
        role: 'staff' as const,
        services: selectedServices,
        specialties: services
          .filter(service => selectedServices.includes(service.id))
          .map(service => service.category)
          .filter((value, index, self) => self.indexOf(value) === index), // Get unique categories
        updatedAt: new Date().toISOString(),
      }

      if (selectedStaff) {
        // Update existing staff using Redux
        await dispatch(updateStaff({
          id: selectedStaff.id,
          data: baseStaffData
        }))
        toast.success("Staff member updated successfully")
      } else {
        // Create new staff with required fields using Redux
        if (!currentSpace) {
          toast.error("No space selected. Please select a space first.")
          return
        }
        
        // Use space ID 
        const spaceId = currentSpace?.id 
        
        // Generate a PIN for staff portal access
        const pin = staffAuthService.generateNewPin();
        
        const newStaffData = {
          ...baseStaffData,
          availability: {}, // Default empty availability
          createdAt: new Date().toISOString(),
          photoURL: '', // Default empty photo URL
          spaceId: spaceId, // Use the new spaceId field
          salonId: spaceId, // Keep salonId for backward compatibility
          pin: {
            value: pin,
            hasChangedDefault: false,
            lastUpdated: new Date().toISOString()
          }
        }
        
        const newStaffResult = await dispatch(createStaff(newStaffData)).unwrap();
        
        // Store the PIN to show in the dialog
        setNewStaffPin(pin);
        toast.success("Staff member added successfully")
      }

      // Reset form and close dialog
      resetStaffForm()
      setIsAddStaffDialogOpen(false)
    } catch (error) {
      console.error('Error saving staff:', error)
      toast.error(selectedStaff ? "Failed to update staff member" : "Failed to add staff member")
    }
  }

  const resetStaffForm = () => {
    // Clear selected staff in Redux
    dispatch(setSelectedStaff(null))

    // Reset local form state
    setNewStaff({
      displayName: "",
      email: "",
      phoneNumber: "",
      commission: "",
      bio: "",
      photoURL: "",
      experience: {
        years: 0,
        since: new Date().toISOString().split('T')[0],
        previousWorkplaces: []
      },
      certifications: []
    })
    setSelectedServices([])
  }

  const handleAddOrUpdateService = async () => {
    try {
      // Validate form
      if (!newService.name || !newService.category || !newService.price || !newService.duration) {
        toast.error("Please fill in all required fields")
        return
      }

      const baseServiceData = {
        name: newService.name,
        description: newService.description,
        category: newService.category,
        price: Number(newService.price),
        duration: Number(newService.duration),
        status: newService.status,
        updatedAt: new Date().toISOString()
      }

      if (isEditMode && currentServiceId) {
        // Update existing service
        await serviceService.update(currentServiceId, baseServiceData)
        toast.success("Service updated successfully")
      } else {
        // Create new service with required createdAt field
        if (!currentSpace) {
          toast.error("No space selected. Please select a space first.")
          return
        }
        
        const spaceId = currentSpace?.id 

        const newServiceData = {
          ...baseServiceData,
          createdAt: new Date().toISOString(),
          spaceId: spaceId, // Use the new field name
          salonId: spaceId  // Keep for backward compatibility
        }
        await serviceService.create(newServiceData)
        toast.success("Service added successfully")
      }

      // Refresh services list for the current salon
      if (currentSpace) {
        const updatedServices = await serviceService.getBySpace(currentSpace.id)
        setServices(updatedServices)
      }

      // Reset form and close dialog
      resetServiceForm()
      setIsAddServiceDialogOpen(false)
    } catch (error) {
      console.error('Error saving service:', error)
      toast.error(isEditMode ? "Failed to update service" : "Failed to add service")
    }
  }

  const resetServiceForm = () => {
    setIsEditMode(false)
    setCurrentServiceId(null)
    setNewService({
      name: "",
      description: "",
      category: "",
      price: "",
      duration: "",
      status: "Active"
    })
  }

  return (
    <div className="h-full flex-1 space-y-4 sm:space-y-8 p-4 sm:p-8">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Services</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Manage your space services and staff specialties
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4">
          <Button onClick={() => setIsAddStaffDialogOpen(true)} className="w-full sm:w-auto">
            <Users className="mr-2 h-4 w-4" />
            Add Staff
          </Button>
          <Button onClick={() => setIsAddServiceDialogOpen(true)} className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            Add Service
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search services or staff..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:max-w-sm"
          />
        </div>
      </div>

      <Dialog
        open={isAddStaffDialogOpen}
        onOpenChange={(open) => {
          setIsAddStaffDialogOpen(open)
          if (!open) resetStaffForm()
        }}
      >
        
          <DialogContent className="w-[95vw] max-w-[500px] sm:max-w-[600px] max-h-[90vh]">
            <ScrollArea className="max-h-[70vh]">
            <DialogHeader className="pr-3">
              <DialogTitle>{selectedStaff ? "Edit Staff Member" : "Add New Staff Member"}</DialogTitle>
              <DialogDescription>
                {selectedStaff
                  ? "Update staff member details and assigned services."
                  : "Add a new staff member to your space and assign their services."}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 pr-3">
              <div className="flex flex-col items-center gap-4 mb-2">
                <div className="h-24 w-24 rounded-full border-2 border-muted flex items-center justify-center bg-muted relative overflow-hidden">
                  {newStaff.photoURL ? (
                    <img 
                      src={newStaff.photoURL} 
                      alt={newStaff.displayName || "Staff photo"} 
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <Users className="h-12 w-12 text-muted-foreground" />
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => document.getElementById('staff-photo-upload')?.click()}
                  >
                    Upload Photo
                  </Button>
                  {newStaff.photoURL && (
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="mt-2"
                      onClick={() => setNewStaff({ ...newStaff, photoURL: '' })}
                    >
                      Remove
                    </Button>
                  )}
                  <input
                    id="staff-photo-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        // This is a placeholder - in a real implementation you would upload to storage
                        // For now, just create a preview URL
                        const url = URL.createObjectURL(file);
                        setNewStaff({ ...newStaff, photoURL: url });
                        toast.success("Photo added - will be saved with staff profile");
                      }
                    }}
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={newStaff.displayName}
                  onChange={(e) => setNewStaff({ ...newStaff, displayName: e.target.value })}
                  placeholder="Enter staff member's full name"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newStaff.email}
                  onChange={(e) => setNewStaff({ ...newStaff, email: e.target.value })}
                  placeholder="Enter email address"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={newStaff.phoneNumber}
                  onChange={(e) => setNewStaff({ ...newStaff, phoneNumber: e.target.value })}
                  placeholder="Enter phone number"
                />
              </div>
              <div className="grid gap-2">
                <Label>Services</Label>
                <Popover open={isServicesOpen} onOpenChange={setIsServicesOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={isServicesOpen}
                      className="justify-between"
                    >
                      {selectedServices.length > 0
                        ? `${selectedServices.length} service${selectedServices.length === 1 ? '' : 's'} selected`
                        : "Select services..."}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0" align="start">
                    <Command>
                      <CommandInput placeholder="Search services..." />
                      <CommandEmpty>No services found.</CommandEmpty>
                      <CommandGroup>
                        <ScrollArea className="h-[200px]">
                          {services.map((service) => (
                            <CommandItem
                              key={service.id}
                              onSelect={() => {
                                setSelectedServices(prev =>
                                  prev.includes(service.id)
                                    ? prev.filter(id => id !== service.id)
                                    : [...prev, service.id]
                                )
                              }}
                            >
                              <div className="flex items-center gap-2 flex-1">
                                <div
                                  className={cn(
                                    "flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                                    selectedServices.includes(service.id)
                                      ? "bg-primary text-primary-foreground"
                                      : "opacity-50 [&_svg]:invisible"
                                  )}
                                >
                                  <Check className={cn("h-3 w-3")} />
                                </div>
                                <span>{service.name}</span>
                                <span className="ml-auto text-xs text-muted-foreground">
                                  {service.category}
                                </span>
                              </div>
                            </CommandItem>
                          ))}
                        </ScrollArea>
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                {selectedServices.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {services
                      .filter(service => selectedServices.includes(service.id))
                      .map(service => (
                        <Badge
                          key={service.id}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => {
                            setSelectedServices(prev =>
                              prev.filter(id => id !== service.id)
                            )
                          }}
                        >
                          {service.name}
                          <X className="ml-1 h-3 w-3" />
                        </Badge>
                      ))
                    }
                  </div>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="commission">Commission Rate (%)              </Label>
                <Input
                  id="commission"
                  type="number"
                  value={newStaff.commission}
                  onChange={(e) => setNewStaff({ ...newStaff, commission: e.target.value })}
                  placeholder="Enter commission percentage"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={newStaff.bio}
                  onChange={(e) => setNewStaff({ ...newStaff, bio: e.target.value })}
                  placeholder="Enter staff member's bio"
                  className="min-h-[100px]"
                />
              </div>
              <div className="grid gap-4">
                <Label>Experience</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="years" className="text-sm">Years of Experience</Label>
                    <Input
                      id="years"
                      type="number"
                      value={newStaff.experience.years}
                      onChange={(e) => setNewStaff({
                        ...newStaff,
                        experience: { ...newStaff.experience, years: Number(e.target.value) }
                      })}
                      placeholder="Years"
                    />
                  </div>
                  <div>
                    <Label htmlFor="since" className="text-sm">Working Since</Label>
                    <Input
                      id="since"
                      type="date"
                      value={newStaff.experience.since}
                      onChange={(e) => setNewStaff({
                        ...newStaff,
                        experience: { ...newStaff.experience, since: e.target.value }
                      })}
                    />
                  </div>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="certifications">Certifications</Label>
                <div className="flex flex-wrap gap-2">
                  {newStaff.certifications.map((cert, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => {
                      setNewStaff({
                        ...newStaff,
                        certifications: newStaff.certifications.filter((_, i) => i !== index)
                      })
                    }}>
                      {cert}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                  <Input
                    placeholder="Add certification and press Enter"
                    className="w-full"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const input = e.target as HTMLInputElement;
                        const value = input.value.trim();
                        if (value) {
                          setNewStaff({
                            ...newStaff,
                            certifications: [...newStaff.certifications, value]
                          });
                          input.value = '';
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>
            <DialogFooter className="pr-3">
              <Button variant="outline" onClick={() => {
                setIsAddStaffDialogOpen(false)
                resetStaffForm()
              }}>
                Cancel
              </Button>
              <Button onClick={handleAddOrUpdateStaff}>
                {selectedStaff ? "Update Staff Member" : "Add Staff Member"}
              </Button>
            </DialogFooter>
            </ScrollArea>
          </DialogContent>
      </Dialog>

      {/* Staff PIN Dialog - Shows after staff member is created successfully */}
      <Dialog 
        open={!!newStaffPin} 
        onOpenChange={(open) => !open && setNewStaffPin(null)}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Staff Portal Access</DialogTitle>
            <DialogDescription>
              Share these login details with the staff member to access their portal
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4">
              <div className="mb-2">
                <span className="text-sm font-medium">Portal URL</span>
                <div className="flex items-center gap-2 mt-1">
                  <code className="rounded bg-background px-2 py-1 text-sm flex-1">
                    {`${process.env.NEXT_PUBLIC_SITE_URL || 'https://groombook.me'}/staff/portal/${currentSpace?.id}`}
                  </code>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      if (currentSpace?.id) {
                        navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_SITE_URL || 'https://groombook.me'}/staff/portal/${currentSpace.id}`);
                        toast.success("URL copied to clipboard");
                      }
                    }}
                  >
                    Copy
                  </Button>
                </div>
              </div>
              <div>
                <span className="text-sm font-medium">PIN</span>
                <div className="flex items-center gap-2 mt-1">
                  <code className="rounded bg-background px-2 py-1 text-sm flex-1">
                    {newStaffPin}
                  </code>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      if (newStaffPin) {
                        navigator.clipboard.writeText(newStaffPin);
                        toast.success("PIN copied to clipboard");
                      }
                    }}
                  >
                    Copy
                  </Button>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Share the portal URL with the staff member. They will use their PIN to log in securely.
              </p>
              <p className="text-sm text-muted-foreground">
                The staff member will be required to change this PIN on their first login.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setNewStaffPin(null)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isAddServiceDialogOpen}
        onOpenChange={(open) => {
          setIsAddServiceDialogOpen(open)
          if (!open) resetServiceForm()
        }}
      >
        <DialogContent className="max-h-[90vh]">
          <ScrollArea className="max-h-[70vh]">
            <DialogHeader>
              <DialogTitle>{isEditMode ? "Edit Service" : "Add New Service"}</DialogTitle>
              <DialogDescription>
                {isEditMode
                  ? "Update service details."
                  : "Add a new service to your space's offerings."}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Service Name</Label>
              <Input
                id="name"
                value={newService.name}
                onChange={(e) => setNewService({ ...newService, name: e.target.value })}
                placeholder="Enter service name"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newService.description}
                onChange={(e) => setNewService({ ...newService, description: e.target.value })}
                placeholder="Enter service description"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={newService.category}
                onValueChange={(value) => setNewService({ ...newService, category: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="haircut">Haircut</SelectItem>
                  <SelectItem value="styling">Styling</SelectItem>
                  <SelectItem value="coloring">Coloring</SelectItem>
                  <SelectItem value="treatment">Treatment</SelectItem>
                  <SelectItem value="nails">Nails</SelectItem>
                  <SelectItem value="makeup">Makeup</SelectItem>
                  <SelectItem value="spa">Spa</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="price">Price (KES)</Label>
                <Input
                  id="price"
                  type="number"
                  value={newService.price}
                  onChange={(e) => setNewService({ ...newService, price: e.target.value })}
                  placeholder="Enter price"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="duration">Duration (minutes)</Label>
                <Input
                  id="duration"
                  type="number"
                  value={newService.duration}
                  onChange={(e) => setNewService({ ...newService, duration: e.target.value })}
                  placeholder="Enter duration"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddServiceDialogOpen(false)
              resetServiceForm()
            }}>
              Cancel
            </Button>
            <Button onClick={handleAddOrUpdateService}>
              {isEditMode ? "Update Service" : "Add Service"}
            </Button>
          </DialogFooter>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="staff">Staff & Specialties</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="services">
          <ServicesTab
            services={services}
            staff={staff}
            isLoading={isLoading}
            searchTerm={searchTerm}
            setIsAddServiceDialogOpen={setIsAddServiceDialogOpen}
            setIsEditMode={setIsEditMode}
            setCurrentServiceId={setCurrentServiceId}
            setNewService={setNewService}
          />
        </TabsContent>

        <TabsContent value="staff">
          <StaffTab
            staff={staff}
            services={services}
            isLoading={isStaffLoading || isLoading}
            searchTerm={searchTerm}
            setIsAddStaffDialogOpen={setIsAddStaffDialogOpen}
            setNewStaff={(staff: Partial<Staff>) => setNewStaff({
              displayName: staff.displayName || "",
              email: staff.email || "",
              phoneNumber: staff.phoneNumber || "",
              commission: staff.commission?.toString() || "",
              bio: staff.bio || "",
              photoURL: staff.photoURL || "",
              experience: {
                years: staff.experience?.years || 0,
                since: staff.experience?.since || new Date().toISOString().split('T')[0],
                previousWorkplaces: staff.experience?.previousWorkplaces || []
              },
              certifications: staff.certifications || []
            })}
            setSelectedServices={setSelectedServices}
          />
        </TabsContent>

        <TabsContent value="history">
          <HistoryTab
            completedServices={completedServices}
            setCompletedServices={setCompletedServices}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
