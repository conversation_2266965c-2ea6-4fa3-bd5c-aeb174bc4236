'use client'

import { useState } from "react"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Plus, Users, Eye, RotateCcw, ExternalLink, Copy, Calendar, Mail, Phone, Award, Briefcase } from "lucide-react"
import { ActionMenu } from "@/components/ui/action-menu"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Service, Staff } from "@/services/types/models"
import { toast } from "sonner"
import { deleteStaff, setSelectedStaff } from "@/store/slices/staffSlice"
import { staffPortalService } from "@/services/staffPortalService"
import { staffAuthService } from "@/services/staffAuthService"
import { format } from "date-fns"

interface StaffTabProps {
  staff: Staff[]
  services: Service[]
  isLoading: boolean
  searchTerm: string
  setIsAddStaffDialogOpen: (open: boolean) => void
  setNewStaff: (staff: Partial<Staff>) => void
  setSelectedServices: (services: string[]) => void
}

export function StaffTab({
  staff,
  services,
  isLoading,
  searchTerm,
  setIsAddStaffDialogOpen,
  setNewStaff,
  setSelectedServices
}: StaffTabProps) {
  const dispatch = useAppDispatch()
  const { currentSpace } = useAppSelector(state => state.space)
  
  // State for staff management dialogs
  const [selectedStaffForDetails, setSelectedStaffForDetails] = useState<Staff | null>(null)
  const [isStaffDetailsOpen, setIsStaffDetailsOpen] = useState(false)
  const [isPinResetDialogOpen, setIsPinResetDialogOpen] = useState(false)
  const [isResettingPin, setIsResettingPin] = useState(false)
  const [newPin, setNewPin] = useState("")

  // First filter by salon ID, then by search term
  const filteredStaff = staff
    .filter(member => !currentSpace || member.spaceId === currentSpace.id)
    .filter(member =>
      member.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.specialties.some(specialty =>
        specialty.toLowerCase().includes(searchTerm.toLowerCase())
      )
    )

  const handleEditStaff = (staffMember: Staff) => {
    // Set the selected staff in Redux
    dispatch(setSelectedStaff(staffMember))

    // Set form values
    setNewStaff({
      displayName: staffMember.displayName,
      email: staffMember.email,
      phoneNumber: staffMember.phoneNumber,
      commission: staffMember.commission,
    })
    setSelectedServices(staffMember.services)
    setIsAddStaffDialogOpen(true)
  }

  const handleDeleteStaff = async (id: string) => {
    try {
      // Delete staff using Redux
      await dispatch(deleteStaff(id))
      toast.success("Staff member deleted successfully")
    } catch (error) {
      console.error('Error deleting staff:', error)
      toast.error("Failed to delete staff member")
    }
  }

  const handleViewStaffDetails = (staffMember: Staff) => {
    setSelectedStaffForDetails(staffMember)
    setIsStaffDetailsOpen(true)
  }

  const handleResetPin = (staffMember: Staff) => {
    setSelectedStaffForDetails(staffMember)
    // Generate a new PIN
    const newRandomPin = staffAuthService.generateNewPin()
    setNewPin(newRandomPin)
    setIsPinResetDialogOpen(true)
  }

  const handleConfirmPinReset = async () => {
    if (!selectedStaffForDetails) return
    
    try {
      setIsResettingPin(true)
      const success = await staffPortalService.adminResetPin(selectedStaffForDetails.id, newPin)
      
      if (success) {
        toast.success(`PIN reset for ${selectedStaffForDetails.displayName}. New PIN: ${newPin}`)
        // Copy PIN to clipboard
        navigator.clipboard.writeText(newPin)
        toast.info("New PIN copied to clipboard")
      } else {
        toast.error('Failed to reset PIN')
      }
    } catch (error) {
      console.error('Error resetting PIN:', error)
      toast.error('An error occurred while resetting PIN')
    } finally {
      setIsResettingPin(false)
      setIsPinResetDialogOpen(false)
      setSelectedStaffForDetails(null)
    }
  }

  const getPortalUrl = () => {
    if (!currentSpace) return ""
    return `${process.env.NEXT_PUBLIC_SITE_URL || 'https://groombook.me'}${staffAuthService.getPortalUrl(currentSpace.id)}`
  }

  const copyPortalUrl = async () => {
    const url = getPortalUrl()
    try {
      await navigator.clipboard.writeText(url)
      toast.success("Portal link copied! Ready to share via WhatsApp, SMS, or any messaging app.")
    } catch (error) {
      // Fallback for older browsers or when clipboard API fails
      console.error('Failed to copy to clipboard:', error)
      toast.error("Failed to copy link. Please copy manually from the URL field.")
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Staff & Specialties</CardTitle>
        <CardDescription>
          View staff members and their service specialties
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="relative w-full overflow-auto">
          {isLoading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : filteredStaff.length === 0 ? (
            <div className="flex h-[400px] flex-col items-center justify-center space-y-3 text-center">
              <div className="bg-[#FFF8E0] p-4 rounded-full">
                <Users className="h-8 w-8 text-[#F5B800]" />
              </div>
              <div className="space-y-1">
                <h3 className="font-medium text-lg">No staff members found</h3>
                <p className="text-muted-foreground text-sm">
                  {searchTerm ? "Try a different search term." : "Add staff members to get started."}
                </p>
              </div>
              {!searchTerm && (
                <Button className="mt-4" onClick={() => setIsAddStaffDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Staff Member
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Staff Member</TableHead>
                  <TableHead>Specialties</TableHead>
                  <TableHead>Services</TableHead>
                  <TableHead>Commission</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[250px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStaff.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={member.photoURL || ''} />
                          <AvatarFallback>
                            {member.displayName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.displayName}</div>
                          <div className="text-sm text-muted-foreground">{member.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {member.specialties.map((specialty, index) => (
                          <Badge key={index} variant="outline">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {services
                          .filter(service => member.services.includes(service.id))
                          .map(service => (
                            <Badge key={service.id} variant="secondary">
                              {service.name}
                            </Badge>
                          ))}
                      </div>
                    </TableCell>
                    <TableCell>{member.commission}%</TableCell>
                    <TableCell>
                      <Badge variant="default">Active</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 flex-wrap">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewStaffDetails(member)}
                          className="min-w-[70px]"
                        >
                          <Eye className="h-4 w-4 sm:mr-1" />
                          <span className="hidden sm:inline">View</span>
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResetPin(member)}
                          className="min-w-[80px]"
                        >
                          <RotateCcw className="h-4 w-4 sm:mr-1" />
                          <span className="hidden sm:inline">Reset PIN</span>
                        </Button>
                        <ActionMenu
                          onEdit={() => handleEditStaff(member)}
                          onDelete={() => handleDeleteStaff(member.id)}
                          deleteDialogDescription={`This will permanently delete the staff member "${member.displayName}" and remove them from your salon.`}
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </CardContent>

      {/* Staff Details Sheet */}
      <Sheet open={isStaffDetailsOpen} onOpenChange={setIsStaffDetailsOpen}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={selectedStaffForDetails?.photoURL || ''} />
                <AvatarFallback>
                  {selectedStaffForDetails?.displayName.split(' ').map(n => n[0]).join('') || 'ST'}
                </AvatarFallback>
              </Avatar>
              {selectedStaffForDetails?.displayName}
            </SheetTitle>
            <SheetDescription>
              Staff member details and portal access information
            </SheetDescription>
          </SheetHeader>
          
          {selectedStaffForDetails && (
            <div className="mt-6 space-y-6">
              {/* Portal Access Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Portal Access</h3>
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">Portal URL</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="flex-1 rounded bg-muted px-2 py-1 text-sm break-all overflow-hidden">
                        {getPortalUrl()}
                      </code>
                      <Button variant="outline" size="sm" onClick={copyPortalUrl} className="shrink-0">
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      variant="outline"
                      onClick={copyPortalUrl}
                      className="text-sm"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy Link
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <a href={getPortalUrl()} target="_blank" rel="noopener noreferrer" className="text-sm">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Open Portal
                      </a>
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      className="flex-1"
                      onClick={() => {
                        setIsStaffDetailsOpen(false)
                        handleResetPin(selectedStaffForDetails)
                      }}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Reset PIN
                    </Button>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Personal Information</h3>
                <div className="grid gap-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm text-muted-foreground">Email</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{selectedStaffForDetails.email}</span>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm text-muted-foreground">Phone</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{selectedStaffForDetails.phoneNumber || 'Not provided'}</span>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm text-muted-foreground">Commission</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-sm font-medium">{selectedStaffForDetails.commission}%</span>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm text-muted-foreground">Joined</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {selectedStaffForDetails.createdAt 
                            ? format(new Date(selectedStaffForDetails.createdAt), 'MMM d, yyyy')
                            : 'Unknown'
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Services & Specialties */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Services & Specialties</h3>
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm text-muted-foreground">Assigned Services</Label>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {services
                        .filter(service => selectedStaffForDetails.services.includes(service.id))
                        .map(service => (
                          <Badge key={service.id} variant="secondary">
                            {service.name}
                          </Badge>
                        ))}
                      {selectedStaffForDetails.services.length === 0 && (
                        <span className="text-sm text-muted-foreground">No services assigned</span>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm text-muted-foreground">Specialties</Label>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedStaffForDetails.specialties.map((specialty, index) => (
                        <Badge key={index} variant="outline">
                          {specialty}
                        </Badge>
                      ))}
                      {selectedStaffForDetails.specialties.length === 0 && (
                        <span className="text-sm text-muted-foreground">No specialties listed</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Experience & Certifications */}
              {(selectedStaffForDetails.experience || (selectedStaffForDetails.certifications && selectedStaffForDetails.certifications.length > 0)) && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Experience & Certifications</h3>
                    <div className="space-y-3">
                      {selectedStaffForDetails.experience && (
                        <div>
                          <Label className="text-sm text-muted-foreground">Experience</Label>
                          <div className="flex items-center gap-2 mt-1">
                            <Briefcase className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {selectedStaffForDetails.experience.years} years
                              {selectedStaffForDetails.experience.since && 
                                ` (since ${format(new Date(selectedStaffForDetails.experience.since), 'yyyy')})`
                              }
                            </span>
                          </div>
                        </div>
                      )}
                      {selectedStaffForDetails.certifications && selectedStaffForDetails.certifications.length > 0 && (
                        <div>
                          <Label className="text-sm text-muted-foreground">Certifications</Label>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {selectedStaffForDetails.certifications.map((cert, index) => (
                              <Badge key={index} variant="outline" className="gap-1">
                                <Award className="h-3 w-3" />
                                {cert}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Bio */}
              {selectedStaffForDetails.bio && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Bio</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {selectedStaffForDetails.bio}
                    </p>
                  </div>
                </>
              )}
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* PIN Reset Dialog */}
      <Dialog open={isPinResetDialogOpen} onOpenChange={setIsPinResetDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Staff PIN</DialogTitle>
            <DialogDescription>
              {selectedStaffForDetails && 
                `Generate a new PIN for ${selectedStaffForDetails.displayName}'s portal access.`
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-pin">New PIN</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="new-pin"
                  value={newPin}
                  onChange={(e) => setNewPin(e.target.value)}
                  placeholder="4-digit PIN"
                  maxLength={4}
                  className="text-center text-lg tracking-widest"
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    const newRandomPin = staffAuthService.generateNewPin()
                    setNewPin(newRandomPin)
                  }}
                >
                  Generate
                </Button>
              </div>
            </div>
            <div className="rounded-md bg-muted p-3">
              <p className="text-sm text-muted-foreground">
                The staff member will be required to change this PIN on their next login for security.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPinResetDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleConfirmPinReset}
              disabled={isResettingPin || newPin.length !== 4}
            >
              {isResettingPin ? "Resetting..." : "Reset PIN"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
