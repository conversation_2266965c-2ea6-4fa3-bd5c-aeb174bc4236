'use client'

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Clock, RefreshCw } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ActiveService } from "@/services/types/models"
import { toast } from "sonner"
import { activeServiceService } from "@/services/firestore"
import { useAppSelector } from "@/store/hooks"
import { formatPrice } from "@/utils/price-formatter"

interface HistoryTabProps {
  completedServices: ActiveService[]
  setCompletedServices: (services: ActiveService[]) => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
}

export function HistoryTab({
  completedServices,
  setCompletedServices,
  isLoading,
  setIsLoading
}: HistoryTabProps) {
  const { currentSpace } = useAppSelector((state) => state.space)
  const [historySearchTerm, setHistorySearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  const filteredCompletedServices = completedServices.filter(service => {
    // Filter by search term
    const matchesSearch =
      service.customerName.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
      service.staffName.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
      service.services.some(s => s.name.toLowerCase().includes(historySearchTerm.toLowerCase()));

    // Filter by status
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "completed" && service.status === "completed") ||
      (statusFilter === "awaiting_payment" && service.status === "awaiting_payment") ||
      (statusFilter === "paid" && service.status === "paid");

    return matchesSearch && matchesStatus;
  })

  // Function to refresh completed services
  const refreshCompletedServices = async () => {
    try {
      setIsLoading(true);
      console.log("Refreshing completed services");
      
      // Get all active services that are completed, awaiting payment, or paid
      const completedServicesData = await activeServiceService.getCompleted();
      
      console.log(`Found ${completedServicesData.length} completed/awaiting_payment/paid services`);
      setCompletedServices(completedServicesData);
      toast.success("Service history refreshed");
    } catch (error) {
      console.error("Error refreshing completed services:", error);
      toast.error("Failed to refresh service history");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>Service History</CardTitle>
            <CardDescription>
              View completed services and their details
            </CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshCompletedServices}
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="awaiting_payment">Awaiting Payment</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
              </SelectContent>
            </Select>
            <Input
              placeholder="Search history..."
              value={historySearchTerm}
              onChange={(e) => setHistorySearchTerm(e.target.value)}
              className="w-full sm:w-[250px]"
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative w-full overflow-auto">
          {isLoading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : filteredCompletedServices.length === 0 ? (
            <div className="flex h-[400px] flex-col items-center justify-center space-y-3 text-center">
              <div className="bg-[#F0F8FF] p-4 rounded-full">
                <Clock className="h-8 w-8 text-[#3B82F6]" />
              </div>
              <div className="space-y-1">
                <h3 className="font-medium text-lg">No service history yet</h3>
                <p className="text-muted-foreground text-sm">
                  {historySearchTerm || statusFilter !== "all"
                    ? "Try adjusting your filters."
                    : "Complete your first service to start building history."}
                </p>
              </div>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Staff</TableHead>
                      <TableHead>Services</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCompletedServices.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell>
                          {service.endTime ? new Date(service.endTime).toLocaleDateString() : "-"}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{service.customerName}</div>
                            <div className="text-sm text-muted-foreground">{service.customerPhone}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={service.staffAvatar || ''} />
                              <AvatarFallback>
                                {service.staffName.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <span>{service.staffName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {service.services.map((s, index) => (
                              <Badge key={index} variant="outline">
                                {s.name}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatPrice(service.totalAmount, currentSpace)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              service.status === "paid"
                                ? "default"
                                : service.status === "completed"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {service.status === "completed"
                              ? "Completed"
                              : service.status === "awaiting_payment"
                              ? "Awaiting Payment"
                              : service.status === "paid"
                              ? "Paid"
                              : service.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Cards View */}
              <div className="md:hidden space-y-4">
                {filteredCompletedServices.map((service) => (
                  <Card key={service.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={service.staffAvatar || ''} />
                            <AvatarFallback>
                              {service.staffName.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium text-sm">{service.customerName}</div>
                            <div className="text-xs text-muted-foreground">{service.customerPhone}</div>
                          </div>
                        </div>
                        <Badge
                          variant={
                            service.status === "paid"
                              ? "default"
                              : service.status === "completed"
                              ? "secondary"
                              : "outline"
                          }
                          className="text-xs"
                        >
                          {service.status === "completed"
                            ? "Completed"
                            : service.status === "awaiting_payment"
                            ? "Awaiting Payment"
                            : service.status === "paid"
                            ? "Paid"
                            : service.status}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Staff:</span>
                          <span>{service.staffName}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Date:</span>
                          <span>{service.endTime ? new Date(service.endTime).toLocaleDateString() : "-"}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Amount:</span>
                          <span className="font-medium">{formatPrice(service.totalAmount, currentSpace)}</span>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-xs text-muted-foreground">Services:</div>
                        <div className="flex flex-wrap gap-1">
                          {service.services.map((s, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {s.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
