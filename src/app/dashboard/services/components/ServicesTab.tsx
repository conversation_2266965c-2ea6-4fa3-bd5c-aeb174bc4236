'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus, Scissors } from "lucide-react"
import { ActionMenu } from "@/components/ui/action-menu"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Service, Staff } from "@/services/types/models"
import { toast } from "sonner"
import { serviceService } from "@/services/firestore"
import { formatPrice } from "@/utils/price-formatter"
import { useAppSelector } from "@/store/hooks"

interface ServicesTabProps {
  services: Service[]
  staff: Staff[]
  isLoading: boolean
  searchTerm: string
  setIsAddServiceDialogOpen: (open: boolean) => void
  setIsEditMode: (editMode: boolean) => void
  setCurrentServiceId: (id: string | null) => void
  setNewService: (service: any) => void
}

export function ServicesTab({
  services,
  staff,
  isLoading,
  searchTerm,
  setIsAddServiceDialogOpen,
  setIsEditMode,
  setCurrentServiceId,
  setNewService
}: ServicesTabProps) {
  const { currentSpace } = useAppSelector((state) => state.space);
  
  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Guard against null or undefined services
  const hasServices = Array.isArray(services) && services.length > 0
  const hasFilteredServices = Array.isArray(filteredServices) && filteredServices.length > 0

  const handleEditService = (service: Service) => {
    setIsEditMode(true)
    setCurrentServiceId(service.id)
    setNewService({
      name: service.name,
      description: service.description,
      category: service.category,
      price: service.price.toString(),
      duration: service.duration.toString(),
      status: service.status
    })
    setIsAddServiceDialogOpen(true)
  }

  const handleDeleteService = async (id: string) => {
    try {
      await serviceService.delete(id)
      toast.success("Service deleted successfully")
      // The parent component should handle refreshing the services list
    } catch (error) {
      console.error('Error deleting service:', error)
      toast.error("Failed to delete service")
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>All Services</CardTitle>
        <CardDescription>
          View and manage your salon services
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="relative w-full overflow-auto">
          {isLoading && !hasServices ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : !hasFilteredServices ? (
            <div className="flex h-[400px] flex-col items-center justify-center space-y-3 text-center">
              <div className="bg-[#FFF8E0] p-4 rounded-full">
                <Scissors className="h-8 w-8 text-[#F5B800]" />
              </div>
              <div className="space-y-1">
                <h3 className="font-medium text-lg">No services found</h3>
                <p className="text-muted-foreground text-sm">
                  {searchTerm ? "Try a different search term." : "Start by adding your first service."}
                </p>
              </div>
              {!searchTerm && (
                <Button className="mt-4" onClick={() => setIsAddServiceDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Service
                </Button>
              )}
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Service Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Staff</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="w-[70px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredServices.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell className="font-medium">{service.name}</TableCell>
                        <TableCell>{service.category}</TableCell>
                        <TableCell>{service.duration} min</TableCell>
                        <TableCell>
                          <div className="flex -space-x-2">
                            {staff
                              .filter(member => member.services.includes(service.id))
                              .map(member => (
                                <Avatar key={member.id} className="h-8 w-8 border-2 border-background">
                                  <AvatarImage src={member.photoURL || ''} />
                                  <AvatarFallback>
                                    {member.displayName.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                              ))}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatPrice(service.price, currentSpace)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={service.status === "Active" ? "default" : "secondary"}
                          >
                            {service.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <ActionMenu
                            onEdit={() => handleEditService(service)}
                            onDelete={() => handleDeleteService(service.id)}
                            deleteDialogDescription={`This will permanently delete the service "${service.name}" and remove it from any staff members who offer it.`}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Cards View */}
              <div className="md:hidden space-y-4">
                {filteredServices.map((service) => (
                  <Card key={service.id} className="p-4">
                    <div className="flex flex-col space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold">{service.name}</h3>
                        <ActionMenu
                          onEdit={() => handleEditService(service)}
                          onDelete={() => handleDeleteService(service.id)}
                          deleteDialogDescription={`This will permanently delete the service "${service.name}" and remove it from any staff members who offer it.`}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Category:</span>
                          <span className="ml-1">{service.category}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Duration:</span>
                          <span className="ml-1">{service.duration} min</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">Staff:</span>
                          <div className="flex -space-x-2">
                            {staff
                              .filter(member => member.services.includes(service.id))
                              .map(member => (
                                <Avatar key={member.id} className="h-6 w-6 border-2 border-background">
                                  <AvatarImage src={member.photoURL || ''} />
                                  <AvatarFallback className="text-xs">
                                    {member.displayName.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                              ))}
                          </div>
                        </div>
                        <Badge
                          variant={service.status === "Active" ? "default" : "secondary"}
                        >
                          {service.status}
                        </Badge>
                      </div>
                      
                      <div className="flex justify-end">
                        <span className="font-semibold text-lg">
                          {formatPrice(service.price, currentSpace)}
                        </span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
