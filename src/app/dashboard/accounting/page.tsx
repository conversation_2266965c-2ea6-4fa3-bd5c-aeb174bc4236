'use client';

import { useState, useEffect } from 'react';
import { useAppSelector } from '@/store/hooks';
import { selectCurrentSpace } from '@/store/slices/spaceSlice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DollarSign,
  TrendingUp,
  Users,
  Package,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { transactionService } from '@/services/transactionService';
import { commissionService } from '@/services/commissionService';
import { ServiceTransaction, CommissionRecord } from '@/services/types/models';
import { toast } from 'sonner';
import { format, startOfMonth, endOfMonth, subMonths } from 'date-fns';
import { formatPrice } from '@/utils/currency';

export default function AccountingPage() {
  const currentSpace = useAppSelector(selectCurrentSpace);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('current_month');
  const [accountingSummary, setAccountingSummary] = useState<any>(null);
  const [transactions, setTransactions] = useState<ServiceTransaction[]>([]);
  const [pendingCommissions, setPendingCommissions] = useState<CommissionRecord[]>([]);

  useEffect(() => {
    if (currentSpace) {
      fetchAccountingData();
    }
  }, [currentSpace, selectedPeriod]);

  const getPeriodDates = () => {
    const now = new Date();
    
    switch (selectedPeriod) {
      case 'current_month':
        return {
          start: startOfMonth(now).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
      case 'last_month':
        const lastMonth = subMonths(now, 1);
        return {
          start: startOfMonth(lastMonth).toISOString(),
          end: endOfMonth(lastMonth).toISOString(),
        };
      case 'last_3_months':
        const threeMonthsAgo = subMonths(now, 3);
        return {
          start: startOfMonth(threeMonthsAgo).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
      default:
        return {
          start: startOfMonth(now).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
    }
  };

  const fetchAccountingData = async () => {
    if (!currentSpace) return;

    try {
      setIsLoading(true);
      const { start, end } = getPeriodDates();

      // Fetch accounting summary
      const summary = await transactionService.getAccountingSummary(currentSpace.id, start, end);
      setAccountingSummary(summary);

      // Fetch transactions
      const transactionsData = await transactionService.getSpaceTransactions(currentSpace.id, start, end);
      setTransactions(transactionsData);

      // Fetch pending commissions
      const pendingCommissionsData = await commissionService.getPendingCommissions(currentSpace.id);
      setPendingCommissions(pendingCommissionsData);

    } catch (error) {
      console.error('Error fetching accounting data:', error);
      toast.error('Failed to load accounting data');
    } finally {
      setIsLoading(false);
    }
  };

  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case 'current_month':
        return format(new Date(), 'MMMM yyyy');
      case 'last_month':
        return format(subMonths(new Date(), 1), 'MMMM yyyy');
      case 'last_3_months':
        return 'Last 3 Months';
      default:
        return 'Current Month';
    }
  };

  // Helper function to format currency using space settings
  const formatCurrency = (amount: number): string => {
    return formatPrice(amount, currentSpace);
  };

  if (!currentSpace) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Accounting Dashboard</CardTitle>
            <CardDescription>
              Please select a space to view accounting information
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Accounting Dashboard</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Complete financial overview for {currentSpace.name}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="current_month">Current Month</SelectItem>
                <SelectItem value="last_month">Last Month</SelectItem>
                <SelectItem value="last_3_months">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={fetchAccountingData}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-lg sm:text-2xl font-bold text-green-600 truncate">
                    {formatCurrency(accountingSummary?.totalRevenue || 0)}
                  </p>
                </div>
                <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Product Costs</p>
                  <p className="text-lg sm:text-2xl font-bold text-red-600 truncate">
                    {formatCurrency(accountingSummary?.totalProductCosts || 0)}
                  </p>
                </div>
                <Package className="h-6 w-6 sm:h-8 sm:w-8 text-red-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Staff Commissions</p>
                  <p className="text-lg sm:text-2xl font-bold text-orange-600 truncate">
                    {formatCurrency(accountingSummary?.totalCommissions || 0)}
                  </p>
                </div>
                <Users className="h-6 w-6 sm:h-8 sm:w-8 text-orange-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Net Profit</p>
                  <p className="text-lg sm:text-2xl font-bold text-blue-600 truncate">
                    {formatCurrency(accountingSummary?.netProfit || 0)}
                  </p>
                </div>
                <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Total Transactions</p>
                  <p className="text-lg sm:text-2xl font-bold">{accountingSummary?.transactionCount || 0}</p>
                </div>
                <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-muted-foreground flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Average Transaction</p>
                  <p className="text-lg sm:text-2xl font-bold truncate">
                    {formatCurrency(accountingSummary?.averageTransactionValue || 0)}
                  </p>
                </div>
                <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-muted-foreground flex-shrink-0 ml-2" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <div className="overflow-x-auto">
            <TabsList className="grid w-full grid-cols-5 min-w-[600px] sm:min-w-0">
              <TabsTrigger value="overview" className="text-xs sm:text-sm">Overview</TabsTrigger>
              <TabsTrigger value="transactions" className="text-xs sm:text-sm">Transactions</TabsTrigger>
              <TabsTrigger value="commissions" className="text-xs sm:text-sm">Commissions</TabsTrigger>
              <TabsTrigger value="products" className="text-xs sm:text-sm">Products</TabsTrigger>
              <TabsTrigger value="staff" className="text-xs sm:text-sm">Staff</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {/* Top Services */}
              <Card>
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-lg sm:text-xl">Top Services - {getPeriodLabel()}</CardTitle>
                  <CardDescription className="text-sm">Best performing services by revenue</CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  {accountingSummary?.topServices?.length > 0 ? (
                    <div className="space-y-3 sm:space-y-4">
                      {accountingSummary.topServices.slice(0, 5).map((service: any, index: number) => (
                        <div key={index} className="flex items-center justify-between gap-2">
                          <div className="min-w-0 flex-1">
                            <p className="font-medium text-sm sm:text-base truncate">{service.serviceName}</p>
                            <p className="text-xs sm:text-sm text-muted-foreground">{service.count} services</p>
                          </div>
                          <p className="font-medium text-sm sm:text-base flex-shrink-0">{formatCurrency(service.revenue)}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-4 text-sm">No services data available</p>
                  )}
                </CardContent>
              </Card>

              {/* Pending Commissions */}
              <Card>
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="text-lg sm:text-xl">Pending Commission Payments</CardTitle>
                  <CardDescription className="text-sm">Staff commissions awaiting payment</CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  {pendingCommissions.length > 0 ? (
                    <div className="space-y-3 sm:space-y-4">
                      {pendingCommissions.slice(0, 5).map((commission) => (
                        <div key={commission.id} className="flex items-center justify-between gap-2">
                          <div className="min-w-0 flex-1">
                            <p className="font-medium text-sm sm:text-base truncate">{commission.staffName}</p>
                            <p className="text-xs sm:text-sm text-muted-foreground truncate">{commission.serviceName}</p>
                          </div>
                          <div className="text-right flex-shrink-0">
                            <p className="font-medium text-sm sm:text-base">{formatCurrency(commission.commissionAmount)}</p>
                            <Badge variant="secondary" className="text-xs">Pending</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-4 text-sm">No pending commissions</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Other tab contents would be added here */}
          <TabsContent value="transactions">
            <Card>
              <CardHeader className="pb-3 sm:pb-6">
                <CardTitle className="text-lg sm:text-xl">Recent Transactions</CardTitle>
                <CardDescription className="text-sm">Complete transaction history</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                {transactions.length > 0 ? (
                  <div className="space-y-3 sm:space-y-4">
                    {transactions.slice(0, 10).map((transaction) => (
                      <div key={transaction.id} className="border rounded-lg p-3 sm:p-4">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                          <div className="min-w-0 flex-1">
                            <p className="font-medium text-sm sm:text-base truncate">{transaction.customerName}</p>
                            <p className="text-xs sm:text-sm text-muted-foreground">
                              {format(new Date(transaction.paidAt || transaction.completedAt), 'MMM d, yyyy h:mm a')}
                            </p>
                          </div>
                          <div className="text-left sm:text-right flex-shrink-0">
                            <p className="font-medium text-sm sm:text-base">{formatCurrency(transaction.totalRevenue)}</p>
                            <p className="text-xs sm:text-sm text-muted-foreground">
                              Profit: {formatCurrency(transaction.netProfit)}
                            </p>
                          </div>
                        </div>
                        <div className="text-xs sm:text-sm text-muted-foreground">
                          <div className="flex flex-col sm:flex-row sm:gap-2">
                            <span>Staff: {transaction.staffName}</span>
                            <span className="hidden sm:inline">|</span>
                            <span className="truncate">Services: {transaction.services.map(s => s.name).join(', ')}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-4 text-sm">No transactions found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
