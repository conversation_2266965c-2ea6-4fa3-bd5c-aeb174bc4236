'use client';

import { useState, useEffect } from 'react';
import { useAppSelector } from '@/store/hooks';
import { selectCurrentSpace } from '@/store/slices/spaceSlice';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DollarSign,
  TrendingUp,
  Users,
  Package,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { transactionService } from '@/services/transactionService';
import { commissionService } from '@/services/commissionService';
import { ServiceTransaction, CommissionRecord } from '@/services/types/models';
import { toast } from 'sonner';
import { format, startOfMonth, endOfMonth, subMonths } from 'date-fns';
import { formatPrice } from '@/utils/currency';

export default function AccountingPage() {
  const currentSpace = useAppSelector(selectCurrentSpace);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('current_month');
  const [accountingSummary, setAccountingSummary] = useState<any>(null);
  const [transactions, setTransactions] = useState<ServiceTransaction[]>([]);
  const [pendingCommissions, setPendingCommissions] = useState<CommissionRecord[]>([]);

  useEffect(() => {
    if (currentSpace) {
      fetchAccountingData();
    }
  }, [currentSpace, selectedPeriod]);

  const getPeriodDates = () => {
    const now = new Date();
    
    switch (selectedPeriod) {
      case 'current_month':
        return {
          start: startOfMonth(now).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
      case 'last_month':
        const lastMonth = subMonths(now, 1);
        return {
          start: startOfMonth(lastMonth).toISOString(),
          end: endOfMonth(lastMonth).toISOString(),
        };
      case 'last_3_months':
        const threeMonthsAgo = subMonths(now, 3);
        return {
          start: startOfMonth(threeMonthsAgo).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
      default:
        return {
          start: startOfMonth(now).toISOString(),
          end: endOfMonth(now).toISOString(),
        };
    }
  };

  const fetchAccountingData = async () => {
    if (!currentSpace) return;

    try {
      setIsLoading(true);
      const { start, end } = getPeriodDates();

      // Fetch accounting summary
      const summary = await transactionService.getAccountingSummary(currentSpace.id, start, end);
      setAccountingSummary(summary);

      // Fetch transactions
      const transactionsData = await transactionService.getSpaceTransactions(currentSpace.id, start, end);
      setTransactions(transactionsData);

      // Fetch pending commissions
      const pendingCommissionsData = await commissionService.getPendingCommissions(currentSpace.id);
      setPendingCommissions(pendingCommissionsData);

    } catch (error) {
      console.error('Error fetching accounting data:', error);
      toast.error('Failed to load accounting data');
    } finally {
      setIsLoading(false);
    }
  };

  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case 'current_month':
        return format(new Date(), 'MMMM yyyy');
      case 'last_month':
        return format(subMonths(new Date(), 1), 'MMMM yyyy');
      case 'last_3_months':
        return 'Last 3 Months';
      default:
        return 'Current Month';
    }
  };

  // Helper function to format currency using space settings
  const formatCurrency = (amount: number): string => {
    return formatPrice(amount, currentSpace);
  };

  if (!currentSpace) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Accounting Dashboard</CardTitle>
            <CardDescription>
              Please select a space to view accounting information
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Accounting Dashboard</h1>
            <p className="text-muted-foreground">
              Complete financial overview for {currentSpace.name}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="current_month">Current Month</SelectItem>
                <SelectItem value="last_month">Last Month</SelectItem>
                <SelectItem value="last_3_months">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={fetchAccountingData}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(accountingSummary?.totalRevenue || 0)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Product Costs</p>
                  <p className="text-2xl font-bold text-red-600">
                    {formatCurrency(accountingSummary?.totalProductCosts || 0)}
                  </p>
                </div>
                <Package className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Staff Commissions</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {formatCurrency(accountingSummary?.totalCommissions || 0)}
                  </p>
                </div>
                <Users className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Net Profit</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(accountingSummary?.netProfit || 0)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Transactions</p>
                  <p className="text-2xl font-bold">{accountingSummary?.transactionCount || 0}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Average Transaction</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(accountingSummary?.averageTransactionValue || 0)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="commissions">Commissions</TabsTrigger>
            <TabsTrigger value="products">Product Usage</TabsTrigger>
            <TabsTrigger value="staff">Staff Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Services */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Services - {getPeriodLabel()}</CardTitle>
                  <CardDescription>Best performing services by revenue</CardDescription>
                </CardHeader>
                <CardContent>
                  {accountingSummary?.topServices?.length > 0 ? (
                    <div className="space-y-4">
                      {accountingSummary.topServices.slice(0, 5).map((service: any, index: number) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{service.serviceName}</p>
                            <p className="text-sm text-muted-foreground">{service.count} services</p>
                          </div>
                          <p className="font-medium">{formatCurrency(service.revenue)}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-4">No services data available</p>
                  )}
                </CardContent>
              </Card>

              {/* Pending Commissions */}
              <Card>
                <CardHeader>
                  <CardTitle>Pending Commission Payments</CardTitle>
                  <CardDescription>Staff commissions awaiting payment</CardDescription>
                </CardHeader>
                <CardContent>
                  {pendingCommissions.length > 0 ? (
                    <div className="space-y-4">
                      {pendingCommissions.slice(0, 5).map((commission) => (
                        <div key={commission.id} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{commission.staffName}</p>
                            <p className="text-sm text-muted-foreground">{commission.serviceName}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">{formatCurrency(commission.commissionAmount)}</p>
                            <Badge variant="secondary">Pending</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-4">No pending commissions</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Other tab contents would be added here */}
          <TabsContent value="transactions">
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Complete transaction history</CardDescription>
              </CardHeader>
              <CardContent>
                {transactions.length > 0 ? (
                  <div className="space-y-4">
                    {transactions.slice(0, 10).map((transaction) => (
                      <div key={transaction.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <p className="font-medium">{transaction.customerName}</p>
                            <p className="text-sm text-muted-foreground">
                              {format(new Date(transaction.paidAt || transaction.completedAt), 'MMM d, yyyy h:mm a')}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">{formatCurrency(transaction.totalRevenue)}</p>
                            <p className="text-sm text-muted-foreground">
                              Profit: {formatCurrency(transaction.netProfit)}
                            </p>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Staff: {transaction.staffName} | 
                          Services: {transaction.services.map(s => s.name).join(', ')}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-4">No transactions found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
