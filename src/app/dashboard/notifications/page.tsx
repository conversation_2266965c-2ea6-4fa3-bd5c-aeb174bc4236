'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Bell, MessageSquare, Mail } from 'lucide-react';
import { FeatureGate } from '@/components/FeatureGate';
import { RestrictedFeature } from '@/hooks/use-feature-access';
import { useAuth } from '@/utils/auth';

const SMS_BENEFITS = [
  "Send appointment reminders via SMS",
  "Automatically notify clients of changes",
  "Higher engagement rates than email",
  "Customizable templates and messaging",
  "Two-way messaging with customers"
];

function EmailSettingsTab() {
  const [emailNotifications, setEmailNotifications] = useState({
    appointments: true,
    marketing: true,
    systemUpdates: true
  });

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <Label htmlFor="appointments-email" className="text-base">Appointment Notifications</Label>
            <p className="text-sm text-muted-foreground">
              Receive notifications for new and updated appointments
            </p>
          </div>
          <Switch
            id="appointments-email"
            checked={emailNotifications.appointments}
            onCheckedChange={(checked) => setEmailNotifications(prev => ({ ...prev, appointments: checked }))}
          />
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <Label htmlFor="marketing-email" className="text-base">Marketing Updates</Label>
            <p className="text-sm text-muted-foreground">
              Receive marketing tips and promotional ideas
            </p>
          </div>
          <Switch
            id="marketing-email"
            checked={emailNotifications.marketing}
            onCheckedChange={(checked) => setEmailNotifications(prev => ({ ...prev, marketing: checked }))}
          />
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <Label htmlFor="system-email" className="text-base">System Updates</Label>
            <p className="text-sm text-muted-foreground">
              Receive notifications about system changes and maintenance
            </p>
          </div>
          <Switch
            id="system-email"
            checked={emailNotifications.systemUpdates}
            onCheckedChange={(checked) => setEmailNotifications(prev => ({ ...prev, systemUpdates: checked }))}
          />
        </div>
      </div>

      <Button className="w-full">Save Email Preferences</Button>
    </div>
  );
}

function SMSSettingsTab() {
  const [smsNotifications, setSmsNotifications] = useState({
    appointments: true,
    reminders: true,
    marketing: false
  });

  const [customTemplate, setCustomTemplate] = useState(
    "Hi {customer_name}, this is a reminder about your appointment on {appointment_date} at {appointment_time}. Reply YES to confirm or call us to reschedule."
  );

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <Label htmlFor="appointments-sms" className="text-base">Appointment Confirmations</Label>
            <p className="text-sm text-muted-foreground">
              Send SMS confirmations when appointments are booked
            </p>
          </div>
          <Switch
            id="appointments-sms"
            checked={smsNotifications.appointments}
            onCheckedChange={(checked) => setSmsNotifications(prev => ({ ...prev, appointments: checked }))}
          />
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <Label htmlFor="reminders-sms" className="text-base">Appointment Reminders</Label>
            <p className="text-sm text-muted-foreground">
              Send SMS reminders before scheduled appointments
            </p>
          </div>
          <Switch
            id="reminders-sms"
            checked={smsNotifications.reminders}
            onCheckedChange={(checked) => setSmsNotifications(prev => ({ ...prev, reminders: checked }))}
          />
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <Label htmlFor="marketing-sms" className="text-base">Marketing Messages</Label>
            <p className="text-sm text-muted-foreground">
              Send promotional messages and special offers
            </p>
          </div>
          <Switch
            id="marketing-sms"
            checked={smsNotifications.marketing}
            onCheckedChange={(checked) => setSmsNotifications(prev => ({ ...prev, marketing: checked }))}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="reminder-template">Reminder Template</Label>
        <Textarea
          id="reminder-template"
          value={customTemplate}
          onChange={(e) => setCustomTemplate(e.target.value)}
          placeholder="Enter your custom reminder message template"
          className="h-24"
        />
        <p className="text-xs text-muted-foreground">
          Available variables: {'{customer_name}'}, {'{appointment_date}'}, {'{appointment_time}'}, {'{service_name}'}
        </p>
      </div>

      <Button className="w-full">Save SMS Preferences</Button>
    </div>
  );
}

function NotificationsSettings() {
  const { user } = useAuth();

  useEffect(() => {
    // Load user notification settings here
  }, [user]);

  return (
    <div className="h-full flex-1 flex flex-col">
      <div className="flex justify-between items-center p-4 sm:p-8 pb-0">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground mb-3">
            Configure your notification preferences and communication settings
          </p>
        </div>
      </div>

      <Tabs defaultValue="email">
        <div className="px-4 sm:px-8">
          <TabsList className="grid w-full grid-cols-3 h-auto">
            <TabsTrigger value="email" className="flex items-center justify-center gap-2 text-xs sm:text-sm">
              <Mail className="h-4 w-4" />
              <span className="hidden sm:inline">Email</span>
            </TabsTrigger>
            <TabsTrigger value="sms" className="flex items-center justify-center gap-2 text-xs sm:text-sm">
              <MessageSquare className="h-4 w-4" />
              <span className="hidden sm:inline">SMS</span>
            </TabsTrigger>
            <TabsTrigger value="push" className="flex items-center justify-center gap-2 text-xs sm:text-sm">
              <Bell className="h-4 w-4" />
              <span className="hidden sm:inline">Push</span>
            </TabsTrigger>
          </TabsList>
        </div>



        <div className="flex-1 overflow-auto p-4 sm:p-8">
          <TabsContent value="email" className="mt-0 h-full">
            <Card>
              <CardHeader>
                <CardTitle>Email Notifications</CardTitle>
                <CardDescription>Configure your email notification preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <EmailSettingsTab />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sms" className="mt-0 h-full">
            <FeatureGate
              feature={RestrictedFeature.SMS_NOTIFICATIONS}
              title="SMS Notifications"
              description="Send SMS appointment reminders and notifications to your clients."
              benefits={SMS_BENEFITS}
            >
              <Card>
                <CardHeader>
                  <CardTitle>SMS Notifications</CardTitle>
                  <CardDescription>Configure your SMS notification settings and templates</CardDescription>
                </CardHeader>
                <CardContent>
                  <SMSSettingsTab />
                </CardContent>
              </Card>
            </FeatureGate>
          </TabsContent>

          <TabsContent value="push" className="mt-0 h-full">
            <Card>
              <CardHeader>
                <CardTitle>Push Notifications</CardTitle>
                <CardDescription>Configure your push notification preferences</CardDescription>
              </CardHeader>
              <CardContent className="text-center py-6 text-muted-foreground">
                Coming soon
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}

export default function NotificationsPage() {
  return <NotificationsSettings />;
}