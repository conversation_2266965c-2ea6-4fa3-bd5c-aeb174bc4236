'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { spaceService } from '@/services/firestore';
import { Space } from '@/services/types/models';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Scissors, MapPin, Phone, Calendar } from 'lucide-react';
import { useAuth } from '@/utils/auth';

export default function BookingLandingPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [spaces, setSpaces] = useState<Space[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  useEffect(() => {
    const fetchSpaces = async () => {
      try {
        setIsLoading(true);
        // Fetch spaces for the current user if logged in, otherwise use a fallback
        const userId = user ? user.uid : 'public';
        const spacesData = await spaceService.getAllForUser(userId);
        setSpaces(spacesData);
      } catch (error) {
        console.error('Error fetching spaces:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSpaces();
  }, [user]);
  
  const filteredSpaces = spaces.filter(spaces => 
    spaces.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    spaces.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    spaces.address?.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleBookNow = (spacesId: string) => {
    router.push(`/book/${spacesId}`);
  };
  
  return (
    <div className="container mx-auto py-12 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Book Your Appointment</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Find and book appointments at your favorite salons, fitness, center and barbershops
        </p>
      </div>
      
      <div className="max-w-md mx-auto mb-12">
        <div className="relative">
          <Input
            placeholder="Search by space name or location..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : filteredSpaces.length === 0 ? (
        <div className="text-center py-12">
          <Scissors className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">No Spaces Found</h2>
          <p className="text-muted-foreground">
            {searchTerm ? 'Try a different search term' : 'No spaces are available for booking at the moment'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSpaces.map((space) => (
            <Card key={space.id} className="overflow-hidden">
              <div className="h-48 bg-muted relative">
                {space.logo ? (
                  <Image
                  src={space.logo}
                  alt={space.name}
                  className="w-full h-full object-cover"
                  width={400}
                  height={300}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-100 to-indigo-100">
                  <Scissors className="h-16 w-16 text-primary" />
                  </div>
                )}
                <div className="absolute top-4 right-4 bg-primary text-white text-xs px-2 py-1 rounded-full">
                  {space.type === 'barbershop' ? 'Barbershop' : space.type === 'salon' ? 'Salon' : space.type === 'fitness' ? 'Fitness' : 'Space'}
                </div>
              </div>
              
              <CardHeader>
                <CardTitle>{space.name}</CardTitle>
                <CardDescription>{space.description || `Book your appointment at ${space.name}`}</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <span className="text-sm">
                    {space.address}, {space.city}, {space.state} {space.postalCode}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{space.phone}</span>
                </div>
              </CardContent>
              
              <CardFooter>
                <Button 
                  className="w-full" 
                  onClick={() => handleBookNow(space.id)}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Book Now
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
