'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { format, addDays, isBefore, startOfDay } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  Check as CheckIcon,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Clock,
  Tag,
  UserCheck,
  CreditCard
} from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { formatPrice } from "@/utils/price-formatter"
import { toast } from 'sonner';

// Import your existing components
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { PaymentMethodSelector } from '@/components/PaymentMethodSelector';
import { GroomBookBranding } from '@/components/GroomBookBranding';

// Types and services
import { Space, Service, Staff } from '@/services/types/models';
import { publicAppointmentService, BusinessHour, TimeSlot } from '@/services/publicAppointmentService';
import { isSpaceOwnerOnFreePlan } from '@/services/brandingService';
import { formatToE164 } from '@/utils/phoneUtils';
import { useAppSelector } from "@/store/hooks"

// Your existing form schema
const appointmentFormSchema = z.object({
  serviceId: z.string({
    required_error: 'Please select a service',
  }),
  staffId: z.string({
    required_error: 'Please select a staff member',
  }),
  date: z.date({
    required_error: 'Please select a date',
  }),
  time: z.string({
    required_error: 'Please select a time',
  }),
  customerName: z.string().min(2, {
    message: 'Name must be at least 2 characters',
  }),
  customerEmail: z.string().email({
    message: 'Please enter a valid email address',
  }).optional().or(z.literal('')),
  customerPhone: z.string()
    .refine(
      (value) => {
        if (!value || value.trim() === '') return true; // Allow empty
        const formatted = formatToE164(value);
        return formatted !== null;
      },
      {
        message: "Please enter a valid phone number (e.g., +254712345678 or 0712345678).",
      }
    ).optional().or(z.literal('')),
  notes: z.string().optional(),
  paymentMethod: z.enum(['cash', 'card', 'mpesa'], {
    required_error: 'Please select a payment method',
  }).optional(),
}).refine((data) => {
  // Ensure at least one contact method is provided
  const hasEmail = data.customerEmail && data.customerEmail.trim() !== '';
  const hasPhone = data.customerPhone && data.customerPhone.trim() !== '';
  return hasEmail || hasPhone;
}, {
  message: "Please provide either an email address or phone number",
  path: ["customerEmail"], // This will show the error on the email field
});

type AppointmentFormValues = z.infer<typeof appointmentFormSchema>;

// Step interface
interface Step {
  id: number;
  icon: JSX.Element;
  title: string;
}

// Animation variants
const pageVariants = {
  initial: { opacity: 0, x: 100 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -100 }
};

// Update steps array to include review
const steps: Step[] = [
  { 
    id: 1, 
    icon: <Tag className="w-5 h-5" />,
    title: 'Select Service' 
  },
  { 
    id: 2, 
    icon: <UserCheck className="w-5 h-5" />,
    title: 'Choose Staff' 
  },
  { 
    id: 3, 
    icon: <Calendar className="w-5 h-5" />,
    title: 'Pick Date' 
  },
  { 
    id: 4, 
    icon: <Clock className="w-5 h-5" />,
    title: 'Select Time' 
  },
  { 
    id: 5, 
    icon: <User className="w-5 h-5" />,
    title: 'Your Details' 
  },
  { 
    id: 6, 
    icon: <CreditCard className="w-5 h-5" />,
    title: 'Payment Method' 
  },
  { 
    id: 7, 
    icon: <CheckIcon className="w-5 h-5" />,
    title: 'Review' 
  },
];

interface BookAppointmentPageProps {
  params: {
    spaceId: string
  }
}

export default function BookAppointmentPage() {
  const { currentSpace } = useAppSelector((state) => state.space)
  const params = useParams();
  const spaceId = params.spaceId as string;

  // Your existing state
  const [space, setSpace] = useState<Space | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [businessHours, setBusinessHours] = useState<BusinessHour[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isBookingComplete, setIsBookingComplete] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'cash' | 'card' | 'mpesa' | null>(null);
  const [showBranding, setShowBranding] = useState(false);

  // Form
  const form = useForm<AppointmentFormValues>({
    resolver: zodResolver(appointmentFormSchema),
    defaultValues: {
      serviceId: '',
      staffId: '',
      customerName: '',
      customerEmail: '',
      customerPhone: '',
      notes: '',
      paymentMethod: undefined,
    },
  });

  // Fetch space details, services, and staff
  useEffect(() => {
    const fetchSpaceData = async () => {
      try {
        setIsLoading(true);
        const [spaceData, servicesData, staffData] = await Promise.all([
          publicAppointmentService.getSpaceDetails(spaceId),
          publicAppointmentService.getSpaceServices(spaceId),
          publicAppointmentService.getSpaceStaff(spaceId),
        ]);

        setSpace(spaceData);
        setServices(servicesData);
        setStaff(staffData);

        // Check if branding should be shown for free plan users
        const shouldShowBranding = await isSpaceOwnerOnFreePlan(spaceId);
        setShowBranding(shouldShowBranding);

        // Set default business hours if not available from space
        if (spaceData?.businessHours) {
          // Map the business hours to include the enabled property if missing
          const businessHoursArray = Array.isArray(spaceData.businessHours) 
            ? spaceData.businessHours 
            : [spaceData.businessHours];
            
          const formattedBusinessHours = businessHoursArray.map((hour: any) => ({
            ...hour,
            enabled: hour.enabled !== undefined ? hour.enabled : true
          }));
          setBusinessHours(formattedBusinessHours);
        } else {
          setBusinessHours([
            { day: 'Monday', open: '09:00', close: '18:00', enabled: true },
            { day: 'Tuesday', open: '09:00', close: '18:00', enabled: true },
            { day: 'Wednesday', open: '09:00', close: '18:00', enabled: true },
            { day: 'Thursday', open: '09:00', close: '18:00', enabled: true },
            { day: 'Friday', open: '09:00', close: '18:00', enabled: true },
            { day: 'Saturday', open: '09:00', close: '18:00', enabled: true },
            { day: 'Sunday', open: '09:00', close: '18:00', enabled: false },
          ]);
        }
      } catch (error) {
        toast.error('Failed to load space information. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSpaceData();
  }, [spaceId]);

  // Update time slots when service, staff, or date changes
  useEffect(() => {
    const updateTimeSlots = async () => {
      if (!selectedService || !selectedStaff || !selectedDate) {
        setTimeSlots([]);
        return;
      }

      try {
        setIsLoadingTimeSlots(true);

        // Add a small delay to ensure loading state is visible
        await new Promise(resolve => setTimeout(resolve, 500));

        const slots = await publicAppointmentService.generateAvailableTimeSlots(
          selectedDate,
          selectedService.id,
          selectedStaff.id,
          spaceId,
          businessHours
        );

        setTimeSlots(slots);

        // Reset the time selection when date changes
        form.setValue('time', '');
      } catch (error) {
        console.error('Error generating time slots:', error);
        toast.error('Failed to load available time slots. Please try again.');

        // Provide default time slots for a better user experience
        const defaultSlots = generateDefaultTimeSlots(selectedDate);
        setTimeSlots(defaultSlots);
      } finally {
        setIsLoadingTimeSlots(false);
      }
    };

    // Helper function to generate default time slots when there's an error
    const generateDefaultTimeSlots = (date: Date): TimeSlot[] => {
      const slots: TimeSlot[] = [];
      const dayOfWeek = format(date, 'EEEE');
      const daySettings = businessHours.find(day => day.day === dayOfWeek);

      if (!daySettings || !daySettings.enabled) {
        return []; // Space is closed on this day
      }

      // Generate some default slots
      const [openHour, openMinute] = daySettings.open.split(':').map(Number);
      const startTime = new Date(date);
      startTime.setHours(openHour, openMinute, 0, 0);

      // Create slots every 30 minutes for 4 hours
      for (let i = 0; i < 8; i++) {
        const slotTime = new Date(startTime);
        slotTime.setMinutes(slotTime.getMinutes() + (i * 30));

        slots.push({
          time: format(slotTime, 'h:mm a'),
          available: true,
          timestamp: new Date(slotTime)
        });
      }

      return slots;
    };

    updateTimeSlots();
  }, [selectedService, selectedStaff, selectedDate, spaceId, businessHours, form]);

  // Auto-select payment method if only one is available
  useEffect(() => {
    if (space && currentStep === 6 && !selectedPaymentMethod) {
      const paymentMethods = [
        { id: 'cash', enabled: space.paymentModes?.cash ?? true },
        { id: 'card', enabled: space.paymentModes?.card ?? false },
        { id: 'mpesa', enabled: space.paymentModes?.mpesa ?? false },
      ];
      const enabledMethods = paymentMethods.filter(method => method.enabled);

      if (enabledMethods.length === 1) {
        const singleMethod = enabledMethods[0].id as 'cash' | 'card' | 'mpesa';
        console.log('Auto-selecting payment method via useEffect:', singleMethod);
        setSelectedPaymentMethod(singleMethod);
        form.setValue('paymentMethod', singleMethod);
      }
    }
  }, [space, currentStep, selectedPaymentMethod, form]);

  // Filter staff by service
  const getStaffForService = () => {
    if (!selectedService) return staff;
    return staff.filter(s => s.services.includes(selectedService.id));
  };

  // Check if a date is disabled
  const isDateDisabled = (date: Date) => {
    // Disable dates in the past
    if (isBefore(date, startOfDay(new Date()))) {
      return true;
    }

    // Disable dates more than 30 days in the future
    if (isBefore(addDays(new Date(), 30), date)) {
      return true;
    }

    // Disable dates when the salon is closed
    const dayOfWeek = format(date, 'EEEE');
    const daySettings = businessHours.find(day => day.day === dayOfWeek);
    return !daySettings || !daySettings.enabled;
  };

  const handleNextStep = () => {
    console.log('handleNextStep called, current step:', currentStep, 'steps.length:', steps.length);
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
      console.log('Advanced to step:', currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Helper function to check if step 5 (customer details) is valid
  const isStep5Valid = () => {
    const name = form.getValues('customerName');
    const email = form.getValues('customerEmail');
    const phone = form.getValues('customerPhone');

    // Check if name is provided and valid
    if (!name || name.length < 2) {
      console.log('Step 5 validation failed: name missing or too short', name);
      return false;
    }

    // Check if at least one contact method is provided
    const hasEmail = email && email.trim() !== '';
    const hasPhone = phone && phone.trim() !== '';

    if (!hasEmail && !hasPhone) {
      console.log('Step 5 validation failed: no contact method provided');
      return false;
    }

    // If email is provided, validate format
    if (hasEmail) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        console.log('Step 5 validation failed: invalid email format', email);
        return false;
      }
    }

    // If phone is provided, validate format
    if (hasPhone) {
      const formattedPhone = formatToE164(phone);
      if (!formattedPhone) {
        console.log('Step 5 validation failed: invalid phone format', phone);
        return false;
      }
    }

    console.log('Step 5 validation passed!', { name: !!name, email: hasEmail, phone: hasPhone });
    return true;
  };

  const renderServicePrice = (price: number) => {
    return formatPrice(price, currentSpace);
  };

 

  // Your existing onSubmit function with success screen
  const onSubmit = async (data: AppointmentFormValues) => {
    console.log('🚨 onSubmit called! Current step:', currentStep, 'isSubmitting:', isSubmitting);

    // Only allow submission on the final step (step 7)
    if (currentStep !== steps.length) {
      console.log('❌ Form submission blocked - not on final step. Current:', currentStep, 'Final:', steps.length);
      return;
    }

    if (isSubmitting) return;

    console.log('Starting appointment booking...', data);

    try {
      setIsSubmitting(true);

      const selectedTimeSlot = timeSlots.find(slot => slot.time === data.time);
      if (!selectedTimeSlot) {
        throw new Error('Selected time slot not found');
      }

      console.log('Creating appointment with data:', {
        customerName: data.customerName,
        customerEmail: data.customerEmail,
        customerPhone: data.customerPhone,
        serviceId: selectedService!.id,
        staffId: selectedStaff!.id,
        spaceId: spaceId,
        startTime: selectedTimeSlot.timestamp.toISOString(),
        notes: data.notes,
      });

      const appointmentResult = await publicAppointmentService.createPublicAppointment({
        customerName: data.customerName,
        customerEmail: data.customerEmail && data.customerEmail.trim() !== '' ? data.customerEmail : undefined,
        customerPhone: data.customerPhone && data.customerPhone.trim() !== '' ? data.customerPhone : undefined,
        serviceId: selectedService!.id,
        staffId: selectedStaff!.id,
        spaceId: spaceId,
        startTime: selectedTimeSlot.timestamp.toISOString(),
        notes: data.notes,
      });

      console.log('Appointment created successfully!', appointmentResult);

      // Show success notification
      toast.success('Appointment booked successfully! You will receive a confirmation shortly.');

      setIsBookingComplete(true);
      setCurrentStep(7); // Move to success screen (step 7 since we have 6 steps)
    } catch (error) {
      console.error('Error booking appointment:', error);
      toast.error('Failed to book appointment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading space information...</p>
        </div>
      </div>
    );
  }

  if (!space) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Space Not Found</h1>
          <p className="text-muted-foreground">The space you are looking for does not exist or is not available.</p>
        </div>
      </div>
    );
  }

  // Show success screen after booking is complete
  if (isBookingComplete) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckIcon className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Booking Confirmed!</h2>
            <p className="text-gray-600 mb-6">
              Your appointment has been successfully booked at {space.name}.
            </p>
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="space-y-2">
                <div>
                  <span className="font-medium">Service:</span> {selectedService?.name}
                </div>
                <div>
                  <span className="font-medium">Date:</span> {selectedDate && format(selectedDate, 'PPP')}
                </div>
                <div>
                  <span className="font-medium">Time:</span> {form.getValues('time')}
                </div>
                <div>
                  <span className="font-medium">Staff:</span> {selectedStaff?.displayName}
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <Button
                onClick={() => window.location.href = '/'}
                className="w-full bg-[#F5B800] hover:bg-[#E5AB00] text-black"
              >
                Return to Home
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = `/book/${spaceId}`}
                className="w-full"
              >
                Book Another Appointment
              </Button>
            </div>
          </div>
        </div>
        {showBranding && <GroomBookBranding />}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-4 md:p-8">
        <div className="bg-white rounded-lg shadow-md p-6 md:p-8">
          <h1 className="text-2xl font-bold mb-6">{space.name}</h1>

          {/* Progress Steps */}
          <div className="flex justify-between mb-8">
            {steps.map((step) => (
              <div 
                key={step.id} 
                className={`flex flex-col items-center ${
                  currentStep >= step.id ? 'text-[#F5B800]' : 'text-gray-400'
                }`}
              >
                <div 
                  className={`w-10 h-10 rounded-full flex items-center justify-center mb-2
                    ${currentStep === step.id 
                      ? 'bg-[#F5B800]/10' 
                      : currentStep > step.id 
                        ? 'bg-[#F5B800] text-white' 
                        : 'bg-gray-100'
                    }`}
                >
                  {currentStep > step.id ? <CheckIcon className="h-5 w-5" /> : step.icon}
                </div>
                <span className="text-xs font-medium hidden sm:block">{step.title}</span>
              </div>
            ))}
          </div>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-6"
              onKeyDown={(e) => {
                // Prevent Enter key from submitting form unless on final step
                if (e.key === 'Enter' && currentStep !== steps.length) {
                  console.log('🚫 Enter key blocked - not on final step');
                  e.preventDefault();
                }
              }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  variants={pageVariants}
                >
                  {/* Step content */}
                  {currentStep === 1 && (
                    <div className="grid md:grid-cols-3 gap-4">
                      {services.map((service) => (
                        <div
                          key={service.id}
                          onClick={() => {
                            setSelectedService(service);
                            form.setValue('serviceId', service.id);
                            handleNextStep();
                          }}
                          className={`p-4 rounded-lg border cursor-pointer transition-all ${
                            selectedService?.id === service.id
                              ? 'border-[#F5B800] bg-[#F5B800]/10'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <h3 className="font-medium">{service.name}</h3>
                              <p className="text-sm text-gray-500">{service.duration} min</p>
                            </div>
                            <Badge variant={selectedService?.id === service.id ? "default" : "outline"} 
                              className={selectedService?.id === service.id ? "bg-[#F5B800] text-black" : ""}>
                              {renderServicePrice(service.price)}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {currentStep === 2 && (
                    <div className="grid md:grid-cols-2 gap-4">
                      {getStaffForService().map((staffMember) => (
                        <div
                          key={staffMember.id}
                          onClick={() => {
                            setSelectedStaff(staffMember);
                            form.setValue('staffId', staffMember.id);
                            handleNextStep();
                          }}
                          className={`p-4 rounded-lg border cursor-pointer flex items-center transition-all ${
                            selectedStaff?.id === staffMember.id
                              ? 'border-[#F5B800] bg-[#F5B800]/10'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3">
                            {staffMember.photoURL ? (
                              <img src={staffMember.photoURL} alt={staffMember.displayName} 
                                className="w-10 h-10 rounded-full object-cover" />
                            ) : (
                              <User className="h-5 w-5 text-gray-500" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium">{staffMember.displayName}</h3>
                            <p className="text-sm text-gray-500">{staffMember.role || 'Staff'}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {currentStep === 3 && (
                    <div className="flex justify-center">
                      <CalendarComponent
                        mode="single"
                        selected={selectedDate}
                        onSelect={(date) => {
                          setSelectedDate(date);
                          if (date) {
                            form.setValue('date', date);
                            handleNextStep();
                          }
                        }}
                        disabled={isDateDisabled}
                        className="rounded-md border"
                      />
                    </div>
                  )}

                  {currentStep === 4 && (
                    <div className="grid md:grid-cols-3 gap-4">
                      {isLoadingTimeSlots ? (
                        <div className="col-span-3 flex items-center justify-center py-4">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F5B800]"></div>
                        </div>
                      ) : timeSlots.length === 0 ? (
                        <div className="col-span-3 text-center py-4 text-gray-500">
                          No available slots for this date
                        </div>
                      ) : (
                        timeSlots
                          .filter(slot => slot.available)
                          .map((slot) => (
                            <div
                              key={slot.time}
                              onClick={() => {
                                form.setValue('time', slot.time);
                                handleNextStep();
                              }}
                              className={`p-4 text-center rounded-lg border cursor-pointer transition-all ${
                                form.getValues('time') === slot.time
                                  ? 'border-[#F5B800] bg-[#F5B800]/10'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              {slot.time}
                            </div>
                          ))
                      )}
                    </div>
                  )}

                  {currentStep === 5 && (
                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="customerName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Your Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter your full name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="space-y-4">
                        <p className="text-sm text-muted-foreground">
                          Please provide at least one contact method (email or phone number)
                        </p>

                        <FormField
                          control={form.control}
                          name="customerEmail"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email (Optional)</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter your email (optional)" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="customerPhone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Phone Number (Optional)</FormLabel>
                              <FormControl>
                                <Input placeholder="e.g., +254712345678 (optional)" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Notes (Optional)</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Any special requests or information"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  {currentStep === 6 && space && (
                    <motion.div 
                      key="payment"
                      initial={{ opacity: 0, x: 100 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -100 }}
                      className="space-y-6"
                    >
                      <PaymentMethodSelector
                        space={space}
                        selectedMethod={selectedPaymentMethod}
                        onMethodSelect={(method) => {
                          setSelectedPaymentMethod(method);
                          form.setValue('paymentMethod', method);
                        }}
                        className="max-w-md mx-auto"
                      />
                      
                      {/* Auto-select if only one payment method is available */}
                      {(() => {
                        const paymentMethods = [
                          { id: 'cash', enabled: space.paymentModes?.cash ?? true },
                          { id: 'card', enabled: space.paymentModes?.card ?? false },
                          { id: 'mpesa', enabled: space.paymentModes?.mpesa ?? false },
                        ];
                        const enabledMethods = paymentMethods.filter(method => method.enabled);

                        console.log('Payment methods check:', {
                          enabledMethods: enabledMethods.map(m => m.id),
                          selectedPaymentMethod,
                          paymentModes: space.paymentModes
                        });

                        // Auto-select if only one method is available and none is selected
                        if (enabledMethods.length === 1 && !selectedPaymentMethod) {
                          const singleMethod = enabledMethods[0].id as 'cash' | 'card' | 'mpesa';
                          console.log('Auto-selecting payment method:', singleMethod);
                          setTimeout(() => {
                            setSelectedPaymentMethod(singleMethod);
                            form.setValue('paymentMethod', singleMethod);
                          }, 100);
                        }

                        return null;
                      })()}
                    </motion.div>
                  )}

                  {currentStep === 7 && (
                    <motion.div 
                      key="review"
                      initial={{ opacity: 0, x: 100 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -100 }}
                      className="space-y-6"
                    >
                      <div className="rounded-lg border bg-gray-50 p-6">
                        <h3 className="text-lg font-semibold mb-4">Review Your Booking</h3>
                        
                        <div className="space-y-4">
                          {/* Service Details */}
                          <div className="bg-white rounded-lg p-4 border">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-medium">Service</h4>
                                <p className="text-gray-600">{selectedService?.name}</p>
                                <p className="text-sm text-gray-500">{selectedService?.duration} minutes</p>
                              </div>
                              <Badge variant="outline" className="bg-[#F5B800] text-black border-0">
                                {renderServicePrice(selectedService?.price || 0)}
                              </Badge>
                            </div>
                          </div>

                          {/* Staff Details */}
                          <div className="bg-white rounded-lg p-4 border">
                            <h4 className="font-medium">Staff Member</h4>
                            <div className="flex items-center mt-2">
                              <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-gray-100">
                                {selectedStaff?.photoURL ? (
                                  <img 
                                    src={selectedStaff.photoURL} 
                                    alt={selectedStaff.displayName}
                                    className="w-10 h-10 rounded-full object-cover"
                                  />
                                ) : (
                                  <User className="h-5 w-5 text-gray-500" />
                                )}
                              </div>
                              <div>
                                <p className="text-gray-600">{selectedStaff?.displayName}</p>
                                <p className="text-sm text-gray-500">{selectedStaff?.role || 'Staff'}</p>
                              </div>
                            </div>
                          </div>

                          {/* Date & Time */}
                          <div className="bg-white rounded-lg p-4 border">
                            <h4 className="font-medium">Date & Time</h4>
                            <div className="mt-2 space-y-1">
                              <p className="text-gray-600">{selectedDate && format(selectedDate, 'PPP')}</p>
                              <p className="text-gray-600">{form.getValues('time')}</p>
                            </div>
                          </div>

                          {/* Customer Details */}
                          <div className="bg-white rounded-lg p-4 border">
                            <h4 className="font-medium">Your Details</h4>
                            <div className="mt-2 space-y-1">
                              <p className="text-gray-600">{form.getValues('customerName')}</p>
                              <p className="text-gray-600">{form.getValues('customerEmail')}</p>
                              <p className="text-gray-600">{form.getValues('customerPhone')}</p>
                              {form.getValues('notes') && (
                                <>
                                  <p className="font-medium mt-2">Additional Notes:</p>
                                  <p className="text-gray-600">{form.getValues('notes')}</p>
                                </>
                              )}
                            </div>
                          </div>

                          {/* Payment Method */}
                          <div className="bg-white rounded-lg p-4 border">
                            <h4 className="font-medium">Payment Method</h4>
                            <div className="mt-2">
                              <p className="text-gray-600 capitalize">
                                {selectedPaymentMethod === 'mpesa' ? 'M-PESA' : selectedPaymentMethod}
                              </p>
                            </div>
                          </div>

                          {/* Terms */}
                          <div className="bg-white rounded-lg p-4 border">
                            <p className="text-sm text-gray-500">
                              By confirming this booking, you agree to our{' '}
                              <a href="/terms" className="text-[#F5B800] hover:underline" target="_blank">
                                Terms & Conditions
                              </a>
                              {' '}and{' '}
                              <a href="/privacy" className="text-[#F5B800] hover:underline" target="_blank">
                                Privacy Policy
                              </a>
                            </p>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              </AnimatePresence>

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-8">
                {currentStep > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePrevStep}
                    className="flex items-center"
                  >
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                )}
                
                {currentStep < steps.length ? (
                  <Button
                    type="button"
                    onClick={handleNextStep}
                    className="ml-auto bg-[#F5B800] hover:bg-[#E5AB00] text-black"
                    disabled={
                      (currentStep === 5 && !isStep5Valid()) ||
                      (currentStep === 6 && !selectedPaymentMethod) ||
                      (currentStep === 1 && !selectedService) ||
                      (currentStep === 2 && !selectedStaff) ||
                      (currentStep === 3 && !selectedDate) ||
                      (currentStep === 4 && !form.getValues('time'))
                    }
                  >
                    {currentStep === 5 ? "Select Payment" : currentStep === 6 ? "Review Booking" : "Next"}
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="ml-auto bg-[#F5B800] hover:bg-[#E5AB00] text-black"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2" />
                        Booking...
                      </>
                    ) : (
                      <>
                        Confirm Booking
                        <CheckIcon className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
        {showBranding && <GroomBookBranding />}
      </div>
    </div>
  );
}