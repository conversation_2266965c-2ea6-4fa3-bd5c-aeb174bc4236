'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAppDispatch } from '@/store/hooks';
import { registerUser } from '@/store/slices/authSlice';
import { Check } from 'lucide-react';
import { pricingPlans } from '@/lib/billing';
import posthog from 'posthog-js';
import RegisterLoading from './loading';
import RegisterForm from './RegisterForm';

import { Badge } from '@/components/ui/badge';

const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

type FormData = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get plan and billing cycle from URL parameters
  const plan = searchParams.get('plan') || '';
  const billingCycle = searchParams.get('billing') || 'monthly';

  // Format the plan name for display
  const formattedPlan = plan.charAt(0).toUpperCase() + plan.slice(1);

  // Track plan selection on page load
  useEffect(() => {
    posthog.capture('registration_started', {
      plan: plan,
      billingCycle: billingCycle,
      price: getPlanPrice()
    });
  }, []);

  // Format pricing for display
  const getPlanPrice = () => {
    switch (plan.toLowerCase()) {
      case 'free':
        return 'Free';
      case 'basic':
        return billingCycle === 'monthly' ? '$5.00/mo' : '$50.00/yr';
      case 'pro':
        return billingCycle === 'monthly' ? '$19.90/mo' : '$190.00/yr';
      case 'enterprise':
        return billingCycle === 'monthly' ? '$45.00/mo' : '$450.00/yr';
      default:
        return 'Free'; // Default to free if no plan specified
    }
  };

  const form = useForm<FormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });


  return (
    <div className="min-h-screen flex">
      {/* Left side - Form */}
      <Suspense fallback={<RegisterLoading />}>
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="w-full max-w-md space-y-8">
            <div>
              <div className="mb-12">
                <h1 className="text-2xl font-bold">GroomBook</h1>
              </div>
              <h2 className="text-4xl font-bold mb-2">Create Account</h2>
              <p className="text-gray-500">Join us to start managing your salon efficiently</p>
              <div className="mt-2 bg-blue-50 inline-block px-3 py-1 rounded-full">
                {plan.toLowerCase() === 'free' ? (
                  <p className="text-blue-700 text-sm font-medium">Always Free • No Credit Card Required</p>
                ) : (
                  <p className="text-blue-700 text-sm font-medium">{pricingPlans.basic.trialDays}-Day Free Trial • No Credit Card Required</p>
                )}
              </div>
            </div>

            {/* Selected Plan Summary */}
            {plan && (
              <div className="mt-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-100 rounded-lg p-4">
                  <h3 className="font-semibold text-lg mb-2 flex items-center">
                    Selected Plan
                    <Badge className="ml-2" variant={plan.toLowerCase() === 'premium' ? 'default' : 'outline'}>
                      {formattedPlan}
                    </Badge>
                  </h3>
                  <div className="flex flex-col gap-1">
                    <div className="flex justify-between">
                      <span className="text-sm">Billing Cycle:</span>
                      <span className="text-sm font-medium capitalize">{billingCycle}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Price:</span>
                      <span className="text-sm font-medium">{getPlanPrice()}</span>
                    </div>
                    {billingCycle === 'yearly' && (
                      <div className="mt-1 flex items-center justify-end gap-1">
                        <Check className="h-3 w-3 text-green-500" />
                        <span className="text-xs text-green-700">2 months free included</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <RegisterForm plan={plan} billingCycle={billingCycle} getPlanPrice={getPlanPrice} />

            <p className="text-center text-sm text-gray-600">
              Already have an account?{' '}
              <Link href="/login" className="text-[#F5B800] hover:text-[#E5AB00] font-medium">
                Sign In
              </Link>
            </p>
          </div>
        </div>
      </Suspense>


      {/* Right side - Image */}
      <div className="hidden lg:block flex-1 relative">
        <Image
          src="/space-4.jpg"
          alt="Space We are open"
          fill
          className="object-cover"
          priority
        />
      </div>
    </div>
  );
}

