import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAppDispatch } from '@/store/hooks';
import { registerUser } from '@/store/slices/authSlice';
import { User, Mail, Lock, Eye, EyeOff } from 'lucide-react';
import posthog from 'posthog-js';
import { pricingPlans } from '@/lib/billing';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { GoogleSignInButton } from '@/components/auth/GoogleSignInButton';
import { sendWelcomeEmail } from '@/utils/email-client';
import { useRouter } from 'next/navigation';
import { trackRegistrationConversion } from '@/utils/googleAdsTracking';

const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

type FormData = z.infer<typeof registerSchema>;

type RegisterFormProps = {
  plan: string;
  billingCycle: string;
  getPlanPrice: () => string;
};

const RegisterForm: React.FC<RegisterFormProps> = ({ plan, billingCycle, getPlanPrice }) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  async function onSubmit(data: FormData) {
    try {
      setError(null);
      setIsLoading(true);
      const resultAction = await dispatch(
        registerUser({
          email: data.email,
          password: data.password,
          name: data.name,
          planDetails: {
            plan: plan.toLowerCase(),
            billingCycle: billingCycle,
            price: getPlanPrice(),
          },
        })
      );

      if (registerUser.fulfilled.match(resultAction)) {
        // Track successful registration with plan details
        posthog.capture('registration_completed', {
          plan: plan.toLowerCase(),
          billingCycle: billingCycle,
          price: getPlanPrice(),
        });
        
        // Track Google Ads conversion
        trackRegistrationConversion();
        
        // Send welcome email to the new user
        try {
          await sendWelcomeEmail({
            email: data.email,
            name: data.name,
            dashboardUrl: `${window.location.origin}/dashboard`
          });
          console.log('Welcome email sent successfully');
        } catch (emailError) {
          console.error('Failed to send welcome email:', emailError);
          // Don't block the registration flow if email fails
        }

        // Redirect to onboarding for new users
        router.push('/onboarding');
      } else if (registerUser.rejected.match(resultAction)) {
        setError(resultAction.payload as string || 'Registration failed. Please try again.');
        posthog.capture('registration_failed', {
          plan: plan.toLowerCase(),
          billingCycle: billingCycle,
          error: resultAction.payload,
        });
      }
    } catch (error) {
      console.error('Registration error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm flex items-center">
            <span className="mr-2">⚠️</span>
            {error}
          </div>
        )}
        
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md text-sm flex items-center">
          <span className="mr-2">🎉</span>
          <div>
            {plan.toLowerCase() === 'free' ? (
              <p className="font-medium">Start with your free account today</p>
            ) : (
              <p className="font-medium">Start with a {pricingPlans.basic.trialDays}-day free trial</p>
            )}
          </div>
        </div>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input placeholder="Full Name" className="pl-10" {...field} />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input type="email" placeholder="Email Address" className="pl-10" {...field} />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    className="pl-10 pr-12"
                    {...field}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Confirm Password"
                    className="pl-10 pr-12"
                    {...field}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full bg-[#F5B800] hover:bg-[#E5AB00] text-black font-medium"
        >
          {isLoading ? 'Creating Account...' : 
            plan.toLowerCase() === 'free' ? 'Create Free Account' : 
            `Start Your Free ${pricingPlans.basic.trialDays}-Day Trial`
          }
        </Button>
      </form>

      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">or continue with</span>
        </div>
      </div>

      <GoogleSignInButton 
        mode="sign-up" 
        planDetails={
          plan ? {
            plan: plan.toLowerCase(),
            billingCycle: billingCycle,
            price: getPlanPrice()
          } : undefined
        } 
      />
    </Form>
  );
};

export default RegisterForm;
