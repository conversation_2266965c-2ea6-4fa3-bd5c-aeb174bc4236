export interface Salon {
  id: number;
  name: string;
  location: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  openingTime: string;
  closingTime: string;
  isOpen24Hours: boolean;
  description?: string;
  businessHours?: {
    day: string;
    open: string;
    close: string;
    enabled: boolean;
  }[];
}

// ... existing types ...