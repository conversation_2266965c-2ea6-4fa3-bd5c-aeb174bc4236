export interface User {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  role?: 'admin' | 'staff' | 'owner';
  salonId?: string;
  planDetails?: {
    plan: string;
    billingCycle: string;
    price: string;
  };
  hasCompletedOnboarding?: boolean;
  businessType?: string | null;
  referralSource?: string | null;
  subscriptionTier?: string;
  subscriptionStatus?: string;
  subscriptionId?: string;
}

export interface AuthState {
  user: User | null;
  loading: boolean;
  authLoading: boolean;
  error: string | null;
}
