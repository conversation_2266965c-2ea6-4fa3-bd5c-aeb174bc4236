export type Space = {
  id: string;
  name: string;
  description?: string;
  type: 'salon' | 'barbershop' | 'wellness' | 'fitness' | 'space';
  logo?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  businessHours?: {
    day: string;
    open: string;
    close: string;
    enabled: boolean;
  }[];
  // Currency settings
  currency?: {
    code: string;
    symbol: string;
    name: string;
    position: 'prefix' | 'suffix';
  };
  // M-PESA Integration
  mpesaBusinessName?: string;
  mpesaAccountType?: 'till' | 'paybill';
  mpesaShortcode?: string;
  mpesaAccountNumber?: string;
  mpesaPhoneNumber?: string;
  mpesaAutoConfirm?: boolean;
  mpesaEnabled?: boolean;
  // Payment modes
  paymentModes?: {
    cash: boolean;
    card: boolean;
    mpesa: boolean;
  };
  // Notification preferences
  notificationPreferences?: {
    sms?: boolean;
    email?: boolean;
    push?: boolean;
  };
}
