import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  Timestamp,
} from 'firebase/firestore';
import { db } from '@/utils/firebase';
import { 
  ServiceTransaction, 
  ActiveService, 
  CommissionRecord, 
  ServiceProductUsage 
} from './types/models';
import { commissionService } from './commissionService';
import { serviceProductService, ProductUsageInput } from './serviceProductService';

export interface PaymentRecordInput {
  serviceId: string;
  paymentMethod: 'cash' | 'mpesa' | 'bank_transfer' | 'card';
  productUsages?: ProductUsageInput[];
  notes?: string;
  recordedBy: string; // staff ID who recorded the payment
}

export const transactionService = {
  /**
   * Record payment and create complete transaction record
   */
  async recordPayment(input: PaymentRecordInput): Promise<ServiceTransaction> {
    try {
      console.log('Recording payment for service:', input.serviceId);

      // Get the active service details
      const serviceRef = doc(db, 'active-services', input.serviceId);
      const serviceDoc = await getDoc(serviceRef);

      if (!serviceDoc.exists()) {
        throw new Error('Service not found');
      }

      const service = { id: serviceDoc.id, ...serviceDoc.data() } as ActiveService;

      if (service.status !== 'awaiting_payment') {
        throw new Error('Service is not awaiting payment');
      }

      // Record product usage if provided
      let productCosts: ServiceProductUsage[] = [];
      let totalProductCost = 0;

      if (input.productUsages && input.productUsages.length > 0) {
        productCosts = await serviceProductService.recordProductUsage(
          input.serviceId,
          input.productUsages,
          input.recordedBy
        );
        totalProductCost = productCosts.reduce((sum, cost) => sum + cost.totalCost, 0);
      }

      // Calculate and record commissions
      const commissions = await commissionService.calculateAndRecordCommission(
        input.serviceId,
        input.paymentMethod
      );
      const totalCommissions = commissions.reduce((sum, comm) => sum + comm.commissionAmount, 0);

      // Calculate net profit
      const netProfit = service.totalAmount - totalProductCost - totalCommissions;

      // Create complete transaction record
      const transaction: Omit<ServiceTransaction, 'id'> = {
        serviceId: input.serviceId,
        customerId: service.customerId,
        customerName: service.customerName,
        staffId: service.staffId,
        staffName: service.staffName,
        services: service.services,
        totalRevenue: service.totalAmount,
        productCosts,
        totalProductCost,
        commissions,
        totalCommissions,
        netProfit,
        completedAt: service.endTime?.toISOString() || new Date().toISOString(),
        paidAt: new Date().toISOString(),
        paymentMethod: input.paymentMethod,
        status: 'paid',
        spaceId: service.spaceId,
        notes: input.notes,
      };

      // Save transaction record
      const transactionRef = await addDoc(collection(db, 'service-transactions'), transaction);

      // Update active service status to 'paid'
      await updateDoc(serviceRef, {
        status: 'paid',
        paidAt: new Date().toISOString(),
        paymentMethod: input.paymentMethod,
      });

      console.log('Payment recorded successfully:', transactionRef.id);

      return {
        ...transaction,
        id: transactionRef.id,
      };
    } catch (error) {
      console.error('Error recording payment:', error);
      throw error;
    }
  },

  /**
   * Get transaction by service ID
   */
  async getTransactionByServiceId(serviceId: string): Promise<ServiceTransaction | null> {
    try {
      const q = query(
        collection(db, 'service-transactions'),
        where('serviceId', '==', serviceId)
      );

      const snapshot = await getDocs(q);
      if (snapshot.empty) return null;

      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as ServiceTransaction;
    } catch (error) {
      console.error('Error fetching transaction:', error);
      return null;
    }
  },

  /**
   * Get transactions for a space
   */
  async getSpaceTransactions(
    spaceId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ServiceTransaction[]> {
    try {
      let q = query(
        collection(db, 'service-transactions'),
        where('spaceId', '==', spaceId),
        orderBy('paidAt', 'desc')
      );

      const snapshot = await getDocs(q);
      let transactions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ServiceTransaction[];

      // Filter by date range if provided
      if (startDate || endDate) {
        transactions = transactions.filter(transaction => {
          const transactionDate = new Date(transaction.paidAt || transaction.completedAt);
          if (startDate && transactionDate < new Date(startDate)) return false;
          if (endDate && transactionDate > new Date(endDate)) return false;
          return true;
        });
      }

      return transactions;
    } catch (error) {
      console.error('Error fetching space transactions:', error);
      throw error;
    }
  },

  /**
   * Get accounting summary for a space
   */
  async getAccountingSummary(
    spaceId: string,
    startDate?: string,
    endDate?: string
  ): Promise<{
    totalRevenue: number;
    totalProductCosts: number;
    totalCommissions: number;
    netProfit: number;
    transactionCount: number;
    averageTransactionValue: number;
    topServices: Array<{
      serviceName: string;
      count: number;
      revenue: number;
    }>;
    staffPerformance: Array<{
      staffId: string;
      staffName: string;
      serviceCount: number;
      revenue: number;
      commissions: number;
    }>;
  }> {
    try {
      const transactions = await this.getSpaceTransactions(spaceId, startDate, endDate);

      const totalRevenue = transactions.reduce((sum, t) => sum + t.totalRevenue, 0);
      const totalProductCosts = transactions.reduce((sum, t) => sum + t.totalProductCost, 0);
      const totalCommissions = transactions.reduce((sum, t) => sum + t.totalCommissions, 0);
      const netProfit = transactions.reduce((sum, t) => sum + t.netProfit, 0);
      const transactionCount = transactions.length;
      const averageTransactionValue = transactionCount > 0 ? totalRevenue / transactionCount : 0;

      // Calculate top services
      const serviceMap = new Map();
      transactions.forEach(transaction => {
        transaction.services.forEach(service => {
          if (serviceMap.has(service.name)) {
            const existing = serviceMap.get(service.name);
            serviceMap.set(service.name, {
              serviceName: service.name,
              count: existing.count + 1,
              revenue: existing.revenue + service.price,
            });
          } else {
            serviceMap.set(service.name, {
              serviceName: service.name,
              count: 1,
              revenue: service.price,
            });
          }
        });
      });
      const topServices = Array.from(serviceMap.values())
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 10);

      // Calculate staff performance
      const staffMap = new Map();
      transactions.forEach(transaction => {
        if (staffMap.has(transaction.staffId)) {
          const existing = staffMap.get(transaction.staffId);
          staffMap.set(transaction.staffId, {
            staffId: transaction.staffId,
            staffName: transaction.staffName,
            serviceCount: existing.serviceCount + 1,
            revenue: existing.revenue + transaction.totalRevenue,
            commissions: existing.commissions + transaction.totalCommissions,
          });
        } else {
          staffMap.set(transaction.staffId, {
            staffId: transaction.staffId,
            staffName: transaction.staffName,
            serviceCount: 1,
            revenue: transaction.totalRevenue,
            commissions: transaction.totalCommissions,
          });
        }
      });
      const staffPerformance = Array.from(staffMap.values())
        .sort((a, b) => b.revenue - a.revenue);

      return {
        totalRevenue,
        totalProductCosts,
        totalCommissions,
        netProfit,
        transactionCount,
        averageTransactionValue,
        topServices,
        staffPerformance,
      };
    } catch (error) {
      console.error('Error getting accounting summary:', error);
      throw error;
    }
  },

  /**
   * Get transactions for a staff member
   */
  async getStaffTransactions(
    staffId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ServiceTransaction[]> {
    try {
      let q = query(
        collection(db, 'service-transactions'),
        where('staffId', '==', staffId),
        orderBy('paidAt', 'desc')
      );

      const snapshot = await getDocs(q);
      let transactions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ServiceTransaction[];

      // Filter by date range if provided
      if (startDate || endDate) {
        transactions = transactions.filter(transaction => {
          const transactionDate = new Date(transaction.paidAt || transaction.completedAt);
          if (startDate && transactionDate < new Date(startDate)) return false;
          if (endDate && transactionDate > new Date(endDate)) return false;
          return true;
        });
      }

      return transactions;
    } catch (error) {
      console.error('Error fetching staff transactions:', error);
      throw error;
    }
  },
};
