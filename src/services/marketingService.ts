import { db } from '@/utils/firebase';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs,
  doc,
  Timestamp,
  updateDoc,
  increment,
  getDoc,
  setDoc 
} from 'firebase/firestore';

// Type definitions
export interface MarketingSource {
  id?: string;
  source: string;
  count: number;
  spaceId: string;
  lastUpdated: Date | Timestamp;
}

// Marketing service
export const marketingService = {
  /**
   * Record a new marketing source for a space
   */
  async recordSource(spaceId: string, source: string): Promise<void> {
    try {
      // Check if this source already exists for this space
      const sourcesRef = collection(db, 'marketing-sources');
      const q = query(
        sourcesRef,
        where('spaceId', '==', spaceId),
        where('source', '==', source)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        // Create a new source entry
        await addDoc(sourcesRef, {
          source,
          count: 1,
          spaceId,
          lastUpdated: Timestamp.now()
        });
      } else {
        // Update the existing source entry
        const docRef = doc(db, 'marketing-sources', querySnapshot.docs[0].id);
        await updateDoc(docRef, {
          count: increment(1),
          lastUpdated: Timestamp.now()
        });
      }
      
      // Also update summary stats
      await this.updateSummaryStats(spaceId);
      
    } catch (error) {
      console.error('Error recording marketing source:', error);
      throw error;
    }
  },
  
  /**
   * Get all marketing sources for a space
   */
  async getSourcesBySpace(spaceId: string): Promise<MarketingSource[]> {
    try {
      const sourcesRef = collection(db, 'marketing-sources');
      const q = query(sourcesRef, where('spaceId', '==', spaceId));
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastUpdated: doc.data().lastUpdated.toDate()
      })) as MarketingSource[];
      
    } catch (error) {
      console.error('Error getting marketing sources:', error);
      throw error;
    }
  },
  
  /**
   * Update summary statistics for a space
   */
  async updateSummaryStats(spaceId: string): Promise<void> {
    try {
      // Get all sources for this space
      const sources = await this.getSourcesBySpace(spaceId);
      const totalCount = sources.reduce((sum, source) => sum + source.count, 0);
      
      // Calculate percentages for each source
      const sourceStats = sources.map(source => ({
        source: source.source,
        count: source.count,
        percentage: Math.round((source.count / totalCount) * 100)
      }));
      
      // Sort by count (highest first)
      sourceStats.sort((a, b) => b.count - a.count);
      
      // Create or update the summary doc
      const summaryRef = doc(db, 'marketing-summaries', spaceId);
      const summaryDoc = await getDoc(summaryRef);
      
      if (summaryDoc.exists()) {
        await updateDoc(summaryRef, {
          sources: sourceStats,
          totalCount,
          lastUpdated: Timestamp.now()
        });
      } else {
        await setDoc(summaryRef, {
          spaceId,
          sources: sourceStats,
          totalCount,
          lastUpdated: Timestamp.now()
        });
      }
      
    } catch (error) {
      console.error('Error updating marketing stats:', error);
      throw error;
    }
  },
  
  /**
   * Get marketing summary for a space
   */
  async getSummary(spaceId: string): Promise<any> {
    try {
      const summaryRef = doc(db, 'marketing-summaries', spaceId);
      const summaryDoc = await getDoc(summaryRef);
      
      if (summaryDoc.exists()) {
        return {
          ...summaryDoc.data(),
          lastUpdated: summaryDoc.data().lastUpdated.toDate()
        };
      }
      
      return null;
      
    } catch (error) {
      console.error('Error getting marketing summary:', error);
      throw error;
    }
  },

  /**
   * Export marketing data as CSV
   */
  async exportMarketingDataCSV(spaceId: string): Promise<string> {
    try {
      const sources = await this.getSourcesBySpace(spaceId);
      if (!sources || sources.length === 0) {
        return 'No data to export';
      }

      // Create CSV header
      let csv = 'Source,Count,Last Updated\n';
      
      // Add data rows
      sources.forEach(source => {
        const date = source.lastUpdated instanceof Date 
          ? source.lastUpdated.toLocaleString() 
          : (source.lastUpdated as Timestamp).toDate().toLocaleString();
        
        csv += `"${source.source}",${source.count},"${date}"\n`;
      });
      
      return csv;
    } catch (error) {
      console.error('Error exporting marketing data:', error);
      throw error;
    }
  }
};
