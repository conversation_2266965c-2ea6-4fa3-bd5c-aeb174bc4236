import { 
  appointmentService, 
  customerService, 
  serviceService, 
  staffService 
} from './firestore';
import { 
  Appointment, 
  Customer, 
  Service, 
  Staff 
} from './types/models';
import { format, parseISO } from 'date-fns';

// Extended appointment type with UI-specific properties
export interface AppointmentWithDetails extends Appointment {
  customerName: string
  customerEmail: string
  customerPhone: string
  service: string
  serviceName: string
  price: number
  duration: string
  time: string
  date: string // Changed from Date to string for Redux serialization
  services: Array<{
    id: string
    name: string
    price: number
    duration: string
  }>
}

export const enhancedAppointmentService = {
  /**
   * Fetch appointments with detailed information about customers, services, and staff
   */
  async getAppointmentsWithDetails(spaceId: string): Promise<AppointmentWithDetails[]> {
    try {
      // Fetch all appointments for the space
      const appointments = await appointmentService.getBySpace(spaceId);
      
      // Get unique IDs for batch fetching
      const customerIds = [...new Set(appointments.map(a => a.customerId))];
      const serviceIds = [...new Set(appointments.map(a => a.serviceId))];
      const staffIds = [...new Set(appointments.map(a => a.staffId))];
      
      // Fetch all related data in parallel
      const [customers, services, staffMembers] = await Promise.all([
        this.fetchCustomersByIds(customerIds),
        this.fetchServicesByIds(serviceIds),
        this.fetchStaffByIds(staffIds)
      ]);
      
      // Map appointments to enhanced appointments with details
      return appointments.map(appointment => {
        const customer = customers.find(c => c.id === appointment.customerId);
        const service = services.find(s => s.id === appointment.serviceId);
        const staff = staffMembers.find(s => s.id === appointment.staffId);

        const startTime = parseISO(appointment.startTime);
        const endTime = parseISO(appointment.endTime);
        const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);

        // Extract customer info from notes for guest customers
        let customerName = customer?.displayName || 'Unknown Customer';
        let customerEmail = customer?.email || 'No email';
        let customerPhone = customer?.phoneNumber || 'No phone';

        if (appointment.customerId.startsWith('guest-') && appointment.notes) {
          // Parse customer info from notes: "Public booking by [Name] | Email: [Email] | Phone: [Phone]"
          const nameMatch = appointment.notes.match(/Public booking by ([^|]+)/);
          const emailMatch = appointment.notes.match(/Email: ([^|]+)/);
          const phoneMatch = appointment.notes.match(/Phone: ([^|]+)/);

          if (nameMatch) customerName = nameMatch[1].trim();
          if (emailMatch) customerEmail = emailMatch[1].trim();
          if (phoneMatch) customerPhone = phoneMatch[1].trim();
        }

        return {
          ...appointment,
          customerName,
          customerEmail,
          customerPhone,
          service: service?.name || 'Unknown Service',
          serviceName: service?.name || 'Unknown Service',
          price: service?.price || 0,
          duration: this.formatDuration(durationMinutes),
          time: format(startTime, 'h:mm a'),
          date: startTime.toISOString(), // Convert Date to string for Redux serialization
          services: [{
            id: service?.id || '',
            name: service?.name || 'Unknown Service',
            price: service?.price || 0,
            duration: this.formatDuration(service?.duration || 0)
          }]
        };
      });
    } catch (error) {
      console.error('Error fetching appointments with details:', error);
      throw error;
    }
  },
  
  /**
   * Format duration in minutes to a human-readable string
   */
  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return hours === 1 ? '1 hour' : `${hours} hours`;
    }
    
    return `${hours} hr ${remainingMinutes} min`;
  },
  
  /**
   * Fetch customers by IDs
   */
  async fetchCustomersByIds(ids: string[]): Promise<Customer[]> {
    if (ids.length === 0) return [];

    const customers: Customer[] = [];

    // Separate guest customers from real customers
    const realCustomerIds = ids.filter(id => !id.startsWith('guest-'));
    const guestCustomerIds = ids.filter(id => id.startsWith('guest-'));

    // Fetch real customers in parallel
    if (realCustomerIds.length > 0) {
      try {
        const promises = realCustomerIds.map(id => customerService.get(id));
        const results = await Promise.all(promises);

        // Filter out null results and add valid customers
        results.forEach(customer => {
          if (customer) {
            customers.push(customer);
          }
        });
      } catch (error) {
        console.error('Error fetching real customers:', error);
        // Create placeholder customers for failed fetches
        realCustomerIds.forEach(id => {
          customers.push({
            id,
            displayName: 'Unknown Customer',
            email: '<EMAIL>',
            phoneNumber: 'N/A',
            role: 'customer',
            visits: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          } as Customer);
        });
      }
    }

    // Create placeholder customer objects for guest customers
    guestCustomerIds.forEach(guestId => {
      customers.push({
        id: guestId,
        displayName: 'Guest Customer',
        email: '<EMAIL>',
        phoneNumber: 'N/A',
        role: 'customer',
        visits: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } as Customer);
    });

    return customers;
  },
  
  /**
   * Fetch services by IDs
   */
  async fetchServicesByIds(ids: string[]): Promise<Service[]> {
    if (ids.length === 0) return [];
    
    const services: Service[] = [];
    
    // Fetch services in parallel
    const promises = ids.map(id => serviceService.getById(id));
    const results = await Promise.all(promises);
    
    // Filter out null results and add valid services
    results.forEach(service => {
      if (service) {
        services.push(service);
      }
    });
    
    return services;
  },
  
  /**
   * Fetch staff by IDs
   */
  async fetchStaffByIds(ids: string[]): Promise<Staff[]> {
    if (ids.length === 0) return [];
    
    const staffMembers: Staff[] = [];
    
    // Fetch staff in parallel
    const promises = ids.map(id => staffService.getById(id));
    const results = await Promise.all(promises);
    
    // Filter out null results and add valid staff
    results.forEach(staff => {
      if (staff) {
        staffMembers.push(staff);
      }
    });
    
    return staffMembers;
  }
};
