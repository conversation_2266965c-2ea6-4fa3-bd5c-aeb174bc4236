import { addDoc, collection } from 'firebase/firestore';
import { db } from '../utils/firebase';
import { ContactFormSubmission } from './types/contactForm';

/**
 * Service for handling contact form submissions
 * This service only supports creating new submissions (no read, update, or delete)
 */
export const contactService = {
  /**
   * Submit a new contact form
   * @param data Contact form data
   * @returns Promise with the ID of the created document
   */
  async submit(data: Omit<ContactFormSubmission, 'id' | 'status' | 'createdAt'>): Promise<string> {
    try {
      // Prepare the submission data
      const submissionData: Omit<ContactFormSubmission, 'id'> = {
        ...data,
        status: 'new',
        createdAt: new Date().toISOString(),
      };

      // Add the document to Firestore
      const docRef = await addDoc(collection(db, 'contact-submissions'), submissionData);
      
      // Return the document ID
      return docRef.id;
    } catch (error) {
      console.error('Error submitting contact form:', error);
      throw new Error('Failed to submit contact form. Please try again later.');
    }
  }
};
