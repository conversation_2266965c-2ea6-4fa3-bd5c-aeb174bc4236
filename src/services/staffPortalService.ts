import {
  collection,
  doc,
  getDoc,
  updateDoc,
  query,
  where,
  getDocs,
  Timestamp,
  orderBy,
  limit,
  addDoc,
} from 'firebase/firestore';
import { db } from '@/utils/firebase';
import { Staff, Appointment, Service } from '@/services/types/models';
import { format, parseISO, startOfMonth, endOfMonth, subMonths, isWithinInterval } from 'date-fns';


const PIN_LENGTH = 4;
const MAX_PIN_ATTEMPTS = 5;
const PIN_TIMEOUT_MINUTES = 30;

interface StaffPin {
  id: string;
  pin: string;
  staffId: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isFirstLogin: boolean;
  attempts: number;
  lastAttemptAt?: Timestamp;
  lockedUntil?: Timestamp;
}

export interface StaffMetrics {
  totalAppointments: number;
  completedAppointments: number;
  totalEarnings: number;
  averageRating: number;
  recentReviews: Array<{
    id: string;
    rating: number;
    comment: string;
    customerName: string;
    date: string;
  }>;
  serviceBreakdown: Array<{
    name: string;
    count: number;
    earnings: number;
  }>;
}

export const staffPortalService = {
  /**
   * Migrate existing staff PINs from staff documents to staff-pins collection
   */
  async migrateStaffPins(): Promise<void> {
    try {
      console.log('Starting staff PIN migration...');

      // Get all staff members
      const staffRef = collection(db, 'staff');
      const staffSnapshot = await getDocs(staffRef);

      // Get existing PIN documents
      const pinsRef = collection(db, 'staff-pins');
      const pinSnapshot = await getDocs(pinsRef);
      const existingPinStaffIds = new Set(pinSnapshot.docs.map(doc => doc.data().staffId));

      let migratedCount = 0;

      for (const staffDoc of staffSnapshot.docs) {
        const staffData = staffDoc.data();
        const staffId = staffDoc.id;

        // Skip if PIN document already exists
        if (existingPinStaffIds.has(staffId)) {
          continue;
        }

        // Check if staff has a PIN in their document
        if (staffData.pin?.value) {
          console.log(`Migrating PIN for staff ${staffId}`);

          await addDoc(collection(db, 'staff-pins'), {
            pin: staffData.pin.value,
            staffId: staffId,
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
            isFirstLogin: !staffData.pin.hasChangedDefault,
            attempts: 0
          });

          migratedCount++;
        }
      }

      console.log(`Migration completed. Migrated ${migratedCount} staff PINs.`);
    } catch (error) {
      console.error('Error during PIN migration:', error);
      throw error;
    }
  },

  /**
   * Verify a staff member's PIN
   */
  async verifyPin(pin: string): Promise<{ isValid: boolean; isFirstLogin: boolean; staffId?: string }> {
    try {
      console.log('Starting PIN verification for PIN:', pin);
      
      const pinsRef = collection(db, 'staff-pins');
      const q = query(pinsRef, where('pin', '==', pin));
      
      console.log('Running query for PIN');
      
      const snapshot = await getDocs(q);
      
      console.log('Query completed. Documents found:', !snapshot.empty);

      if (snapshot.empty) {
        console.log('No matching PIN found in staff-pins collection, checking legacy staff documents...');

        // Fallback: Check for PIN in staff documents (legacy system)
        const staffRef = collection(db, 'staff');
        const staffSnapshot = await getDocs(staffRef);

        for (const staffDoc of staffSnapshot.docs) {
          const staffData = staffDoc.data();
          if (staffData.pin?.value === pin) {
            console.log('Found PIN in legacy staff document, migrating...');

            // Migrate this PIN to the new system
            await addDoc(collection(db, 'staff-pins'), {
              pin: pin,
              staffId: staffDoc.id,
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now(),
              isFirstLogin: !staffData.pin.hasChangedDefault,
              attempts: 0
            });

            return {
              isValid: true,
              isFirstLogin: !staffData.pin.hasChangedDefault,
              staffId: staffDoc.id
            };
          }
        }

        console.log('No matching PIN found in either system');
        return { isValid: false, isFirstLogin: false };
      }

      const pinDoc = snapshot.docs[0];
      const pinData = pinDoc.data() as StaffPin;
      
      console.log('Found PIN document:', { 
        isFirstLogin: pinData.isFirstLogin,
        staffId: pinData.staffId,
        attempts: pinData.attempts,
        hasLockUntil: !!pinData.lockedUntil
      });

      // Check if account is locked
      if (pinData.lockedUntil && pinData.lockedUntil.toDate() > new Date()) {
        console.log('Account is locked until:', pinData.lockedUntil.toDate());
        return { isValid: false, isFirstLogin: false };
      }

      // Reset attempts if last attempt was more than timeout period ago
      if (pinData.lastAttemptAt && 
          (new Date().getTime() - pinData.lastAttemptAt.toDate().getTime()) > (PIN_TIMEOUT_MINUTES * 60 * 1000)) {
        await updateDoc(doc(db, 'staff-pins', pinDoc.id), {
          attempts: 0,
          lastAttemptAt: null,
          lockedUntil: null
        });
        pinData.attempts = 0;
      }

      const isValid = true; // PIN matches since we queried by it
      
      if (isValid) {
        // Reset attempts on successful login
        console.log('PIN is valid, resetting login attempts');
        
        await updateDoc(doc(db, 'staff-pins', pinDoc.id), {
          attempts: 0,
          lastAttemptAt: null,
          lockedUntil: null
        });

        const result = {
          isValid: true,
          isFirstLogin: pinData.isFirstLogin,
          staffId: pinData.staffId
        };
        
        console.log('Returning successful verification result:', result);
        return result;
      } else {
        // Increment attempts on failed login
        const newAttempts = (pinData.attempts || 0) + 1;
        const updates: any = {
          attempts: newAttempts,
          lastAttemptAt: Timestamp.now()
        };

        // Lock account if max attempts exceeded
        if (newAttempts >= MAX_PIN_ATTEMPTS) {
          const lockUntil = new Date();
          lockUntil.setMinutes(lockUntil.getMinutes() + PIN_TIMEOUT_MINUTES);
          updates.lockedUntil = Timestamp.fromDate(lockUntil);
        }

        await updateDoc(doc(db, 'staff-pins', pinDoc.id), updates);
        return { isValid: false, isFirstLogin: false };
      }
    } catch (error) {
      console.error('Error verifying PIN:', error);
      throw error;
    }
  },

  /**
   * Update a staff member's PIN
   */
  async updatePin(staffId: string, currentPin: string, newPin: string): Promise<boolean> {
    try {
      const pinsRef = collection(db, 'staff-pins');
      const q = query(pinsRef, where('staffId', '==', staffId));
      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        return false;
      }

      const pinDoc = snapshot.docs[0];
      const pinData = pinDoc.data() as StaffPin;

      // Verify current PIN
      if (pinData.pin !== currentPin) {
        return false;
      }

      // Update to new PIN
      await updateDoc(doc(db, 'staff-pins', pinDoc.id), {
        pin: newPin,
        updatedAt: Timestamp.now(),
        isFirstLogin: false
      });

      return true;
    } catch (error) {
      console.error('Error updating PIN:', error);
      throw error;
    }
  },

  /**
   * Get staff member data
   */
  async getStaffData(staffId: string): Promise<Staff | null> {
    try {
      const docRef = doc(db, 'staff', staffId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      return docSnap.data() as Staff;
    } catch (error) {
      console.error('Error getting staff data:', error);
      throw error;
    }
  },

  /**
   * Update staff member profile
   */
  async updateStaffProfile(staffId: string, data: Partial<Staff>): Promise<boolean> {
    try {
      const docRef = doc(db, 'staff', staffId);
      await updateDoc(docRef, {
        ...data,
        updatedAt: Timestamp.now()
      });
      return true;
    } catch (error) {
      console.error('Error updating staff profile:', error);
      throw error;
    }
  },

  /**
   * Request PIN reset for a staff member
   * This sends a notification to the space admins
   */
  async requestPinReset(email: string): Promise<boolean> {
    try {
      console.log('Processing PIN reset request for email:', email);
      
      // Find staff member by email
      const staffRef = collection(db, 'staff');
      const q = query(staffRef, where('email', '==', email));
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.log('No staff found with this email');
        // We return true even if no staff is found to prevent email enumeration attacks
        return true;
      }

      const staffDoc = snapshot.docs[0];
      const staffData = staffDoc.data() as Staff;
      
      // Get space info to notify the admins
      const spaceRef = doc(db, 'spaces', staffData.spaceId);
      const spaceSnap = await getDoc(spaceRef);
      
      if (!spaceSnap.exists()) {
        console.error('Space not found for staff member');
        return true;
      }
      
      // Create a PIN reset request notification
      const notificationRef = collection(db, 'pin-reset-requests');
      await addDoc(notificationRef, {
        staffId: staffDoc.id,
        staffName: staffData.displayName,
        staffEmail: email,
        spaceId: staffData.spaceId,
        status: 'pending',
        createdAt: Timestamp.now(),
      });
      
      console.log('PIN reset request created successfully');
      
      return true;
    } catch (error) {
      console.error('Error requesting PIN reset:', error);
      // Return true even on error to prevent information disclosure
      return true;
    }
  },
  
  /**
   * Get PIN reset requests for a space
   */
  async getPinResetRequests(spaceId: string) {
    try {
      const requestsRef = collection(db, 'pin-reset-requests');
      const q = query(
        requestsRef, 
        where('spaceId', '==', spaceId),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
      }));
    } catch (error) {
      console.error('Error getting PIN reset requests:', error);
      return [];
    }
  },
  
  /**
   * Update PIN reset request status
   */
  async updatePinResetRequestStatus(requestId: string, status: 'completed' | 'rejected') {
    try {
      const requestRef = doc(db, 'pin-reset-requests', requestId);
      await updateDoc(requestRef, {
        status,
        updatedAt: Timestamp.now()
      });
      return true;
    } catch (error) {
      console.error('Error updating PIN reset request:', error);
      throw error;
    }
  },

  /**
   * Admin reset PIN for staff member
   */
  async adminResetPin(staffId: string, newPin: string) {
    try {
      // Find the PIN document for this staff member
      const pinsRef = collection(db, 'staff-pins');
      const q = query(pinsRef, where('staffId', '==', staffId));
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        console.error('No PIN document found for staff');
        return false;
      }
      
      const pinDoc = snapshot.docs[0];
      
      // Update the PIN
      await updateDoc(doc(db, 'staff-pins', pinDoc.id), {
        pin: newPin,
        updatedAt: Timestamp.now(),
        isFirstLogin: true, // Force the staff member to reset their PIN on next login
        attempts: 0,
        lastAttemptAt: null,
        lockedUntil: null
      });
      
      return true;
    } catch (error) {
      console.error('Error resetting staff PIN:', error);
      throw error;
    }
  },
  
  /**
   * Get all staff with their PIN information
   */
  async getStaffWithPins(spaceId: string) {
    try {
      // Get all staff for this space
      const staffRef = collection(db, 'staff');
      const q = query(staffRef, where('spaceId', '==', spaceId));
      let staffSnapshot = await getDocs(q);
      
      // For backward compatibility, if no results found, try with space id
      if (staffSnapshot.empty) {
        const qLegacy = query(staffRef, where('spaceId', '==', spaceId));
        staffSnapshot = await getDocs(qLegacy);
      }
      
      // Get all PIN documents
      const pinsRef = collection(db, 'staff-pins');
      const pinSnapshot = await getDocs(pinsRef);
      
      // Map PIN documents to staff IDs
      const pinMap = new Map();
      pinSnapshot.docs.forEach(doc => {
        const data = doc.data();
        pinMap.set(data.staffId, {
          ...data,
          id: doc.id
        });
      });
      
      // Combine staff and PIN information
      return staffSnapshot.docs.map(doc => {
        const staff = doc.data();
        const pinInfo = pinMap.get(doc.id);
        
        return {
          id: doc.id,
          name: staff.displayName,
          email: staff.email || '',
          createdAt: staff.createdAt.toDate(),
          lastLogin: pinInfo?.lastLoginAt ? pinInfo.lastLoginAt.toDate() : null,
          pin: pinInfo?.pin || 'Not set'
        };
      });
    } catch (error) {
      console.error('Error getting staff with PINs:', error);
      return [];
    }
  },

  /**
   * Get staff performance metrics
   */
  async getStaffMetrics(staffId: string, timeRange: string): Promise<StaffMetrics> {
    try {
      // Calculate date range based on the selected time range
      const now = new Date();
      let startDate = new Date();
      let endDate = new Date();

      switch (timeRange) {
        case 'today':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          endDate = new Date(now.setHours(23, 59, 59, 999));
          break;
        case 'this-week':
          const day = now.getDay();
          startDate = new Date(now.setDate(now.getDate() - day));
          startDate.setHours(0, 0, 0, 0);
          endDate = new Date(now.setDate(now.getDate() + 6));
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'this-month':
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
          break;
        case 'last-month':
          const lastMonth = subMonths(now, 1);
          startDate = startOfMonth(lastMonth);
          endDate = endOfMonth(lastMonth);
          break;
        case 'last-3-months':
          startDate = startOfMonth(subMonths(now, 3));
          endDate = endOfMonth(now);
          break;
        case 'all-time':
          startDate = new Date(0); // Beginning of time
          endDate = new Date();
          break;
        default:
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
      }

      // Fetch appointments for the staff member
      const appointmentsRef = collection(db, 'appointments');
      const appointmentsQuery = query(
        appointmentsRef,
        where('staffId', '==', staffId),
        where('startTime', '>=', startDate.toISOString()),
        where('startTime', '<=', endDate.toISOString())
      );
      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      const appointments = appointmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Appointment[];

      // Fetch services to calculate prices
      const servicesRef = collection(db, 'services');
      const servicesSnapshot = await getDocs(servicesRef);
      const services = servicesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Service[];

      // Fetch recent reviews
      const reviewsRef = collection(db, 'reviews');
      const reviewsQuery = query(
        reviewsRef,
        where('staffId', '==', staffId),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      const reviewsSnapshot = await getDocs(reviewsQuery);
      const reviews = reviewsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          rating: data.rating || 0,
          comment: data.comment || '',
          customerName: data.customerName || 'Anonymous',
          date: data.createdAt ? format(parseISO(data.createdAt), 'MMM d, yyyy') : 'Unknown date'
        };
      });

      // Calculate key metrics
      const completedAppointments = appointments.filter(appointment => 
        appointment.status === 'completed'
      );
      
      // Calculate service breakdown
      const serviceMap = new Map();
      completedAppointments.forEach(appointment => {
        const service = services.find(s => s.id === appointment.serviceId);
        if (service) {
          if (serviceMap.has(service.id)) {
            const existing = serviceMap.get(service.id);
            serviceMap.set(service.id, {
              name: service.name,
              count: existing.count + 1,
              earnings: existing.earnings + service.price
            });
          } else {
            serviceMap.set(service.id, {
              name: service.name,
              count: 1,
              earnings: service.price
            });
          }
        }
      });
      
      const serviceBreakdown = Array.from(serviceMap.values());
      
      // Calculate total earnings
      const totalEarnings = serviceBreakdown.reduce((sum, service) => sum + service.earnings, 0);
      
      // Calculate average rating
      const totalRatings = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = reviews.length > 0 ? totalRatings / reviews.length : 0;

      return {
        totalAppointments: appointments.length,
        completedAppointments: completedAppointments.length,
        totalEarnings,
        averageRating,
        recentReviews: reviews,
        serviceBreakdown
      };
    } catch (error) {
      console.error('Error fetching staff metrics:', error);
      throw error;
    }
  }
};
