import { SubscriptionTier } from './subscriptionService';
import { db } from '@/lib/firebase';
import { collection, doc, getDoc, where, query, getDocs, DocumentData } from 'firebase/firestore';
import { pricingPlans } from '@/lib/billing';

// Declare Paddle types for TypeScript
declare global {
  interface Window {
    Paddle?: {
      Environment: {
        sandbox: {
          setup: (config: { token: string }) => any;
        };
        production: {
          setup: (config: { token: string }) => any;
        };
      };
    };
  }
}

interface PaddleConfig {
  apiKey: string;
  environment: 'sandbox' | 'production';
}

// Initialize Paddle client
let isInitialized = false;
let paddleInstance: any = null;

export const initializePaddle = async (config?: PaddleConfig): Promise<boolean> => {
  if (typeof window === 'undefined') {
    return false; // Don't initialize on the server
  }

  if (isInitialized && paddleInstance) {
    console.log('Paddle already initialized');
    return true;
  }

  const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT || 'sandbox';
  const clientToken = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN;

  if (!clientToken) {
    console.warn('Paddle client token not found in environment variables. Using demo mode.');
    // For development/demo purposes, we'll still try to initialize
    // In production, you should set NEXT_PUBLIC_PADDLE_CLIENT_TOKEN
  }

  // Wait for Paddle SDK to load if it's not available yet
  let attempts = 0;
  const maxAttempts = 50; // 5 seconds max wait

  while (!window.Paddle && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
  }

  if (!window.Paddle) {
    console.error('Paddle SDK failed to load after waiting');
    return false;
  }

  try {
    if (paddleEnv !== 'sandbox' && paddleEnv !== 'production') {
      console.error('Invalid Paddle environment:', paddleEnv);
      return false;
    }

    paddleInstance = window.Paddle.Environment[paddleEnv].setup({
      token: clientToken || 'demo-token' // Fallback for development
    });

    isInitialized = true;
    console.log(`Paddle initialized in ${paddleEnv} environment`);
    return true;
  } catch (error) {
    console.error('Failed to initialize Paddle:', error);
    return false;
  }
};

// Get the user's subscription status from Firestore
export const getSubscriptionStatus = async (userId: string) => {
  try {
    // Get user document
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.error('User not found:', userId);
      return {
        tier: SubscriptionTier.FREE,
        isActive: false,
        expiresAt: null,
        features: []
      };
    }
    
    const userData = userDoc.data();
    const subscriptionId = userData.subscriptionId;
    
    // If no subscription, return free tier
    if (!subscriptionId) {
      return {
        tier: SubscriptionTier.FREE,
        isActive: false,
        expiresAt: null,
        features: pricingPlans.basic.features
      };
    }
    
    // Get subscription details
    const subscriptionRef = doc(db, 'subscriptions', subscriptionId);
    const subscriptionDoc = await getDoc(subscriptionRef);
    
    if (!subscriptionDoc.exists()) {
      return {
        tier: SubscriptionTier.FREE,
        isActive: false,
        expiresAt: null,
        features: pricingPlans.basic.features
      };
    }
    
    const subscription = subscriptionDoc.data();
    
    // Map subscription to tier and features
    let features: string[] = pricingPlans.basic.features;
    let tier = subscription.tier || SubscriptionTier.FREE;
    
    if (tier === SubscriptionTier.PRO) {
      features = pricingPlans.pro.features;
    } else if (tier === SubscriptionTier.ENTERPRISE) {
      features = pricingPlans.enterprise.features;
    }
    
    return {
      tier,
      isActive: subscription.status === 'active',
      expiresAt: subscription.currentPeriodEnd?.toDate() || null,
      features,
      subscription
    };
    
  } catch (error) {
    console.error('Error fetching subscription status:', error);
    return {
      tier: SubscriptionTier.FREE,
      isActive: false,
      expiresAt: null,
      features: pricingPlans.basic.features
    };
  }
};

// Create a checkout session with Paddle
export const createCheckoutSession = async (planId: string, customerId: string) => {
  if (typeof window === 'undefined') {
    throw new Error('Paddle checkout can only be initiated in the browser');
  }
  
  // Initialize Paddle if not already done
  const initialized = await initializePaddle();

  if (!initialized || !paddleInstance) {
    throw new Error('Failed to initialize Paddle');
  }
  
  try {
    // Determine if this is a monthly or yearly plan
    // and get the corresponding priceId from our pricingPlans
    let priceId = planId; // Default to using planId directly if it's a Paddle price ID
    let interval = 'month';
    
    // Extract billing period and plan name from the legacy plan ID format
    if (planId === 'basic' || planId === 'pri_01jw0x8s4xxz8v38ptt3ffqwtp') {
      priceId = pricingPlans.basic.monthly.priceId;
    } else if (planId === 'pro') {
      priceId = pricingPlans.pro.monthly.priceId;
    } else if (planId === 'enterprise') {
      priceId = pricingPlans.enterprise.monthly?.priceId || '';
    }
    
    // Prevent checkout for custom enterprise plans that don't have a direct checkout flow
    if (!priceId && planId === 'enterprise') {
      throw new Error('Please contact sales for Enterprise plan');
    }
    
    // Create custom data to pass with the checkout
    const customData = {
      userId: customerId,
      planId: planId
    };
    
    // Get origin for success and cancel URLs
    const origin = typeof window !== 'undefined' ? window.location.origin : '';
    const successUrl = `${origin}/dashboard/billing/success?session_id={checkout.id}&userId=${customerId}`;
    const cancelUrl = `${origin}/dashboard/billing/canceled`;
    
    // Redirect to Paddle checkout with success and cancel URLs
    window.location.href = `https://checkout.paddle.com/checkout/${priceId}?passthrough=${encodeURIComponent(JSON.stringify(customData))}&success_url=${encodeURIComponent(successUrl)}&cancel_url=${encodeURIComponent(cancelUrl)}`;
    
    return true;
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
};

// Open customer portal for subscription management
export const manageSubscription = async (customerId: string) => {
  if (typeof window === 'undefined') {
    throw new Error('Paddle customer portal can only be opened in the browser');
  }
  
  try {
    // Find the customer's subscription in Firestore
    const subscriptionsRef = collection(db, 'subscriptions');
    const q = query(subscriptionsRef, where('userId', '==', customerId));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      throw new Error('No active subscription found');
    }
    
    // Find an active subscription
    let paddleCustomerId: string | undefined;
    
    querySnapshot.docs.forEach((docSnapshot) => {
      const data = docSnapshot.data();
      if (data.status === 'active' && data.customerId) {
        paddleCustomerId = data.customerId;
      }
    });
    
    if (!paddleCustomerId) {
      throw new Error('No active subscription found');
    }
    
    // Redirect to Paddle customer portal using the customer ID
    window.location.href = `https://checkout.paddle.com/customer/${paddleCustomerId}`;
    
    return true;
  } catch (error) {
    console.error('Error opening customer portal:', error);
    throw error;
  }
};

// Type declaration for Paddle global object
declare global {
  interface Window {
    Paddle: {
      Environment: {
        sandbox: {
          setup: (config: { token: string }) => any;
        };
        production: {
          setup: (config: { token: string }) => any;
        };
      };
    };
  }
}