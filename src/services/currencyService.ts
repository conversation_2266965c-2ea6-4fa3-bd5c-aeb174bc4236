import { db } from '@/utils/firebase';
import { doc, getDoc, setDoc, collection, query, where, getDocs, writeBatch } from 'firebase/firestore';

// Exchange rates (you can integrate with a real API like exchangerate-api.com)
const EXCHANGE_RATES: Record<string, Record<string, number>> = {
  USD: {
    USD: 1,
    KES: 130, // 1 USD = 130 KES (approximate)
    EUR: 0.85, // 1 USD = 0.85 EUR
    GBP: 0.73, // 1 USD = 0.73 GBP
    NGN: 800, // 1 USD = 800 NGN
    ZAR: 18.5, // 1 USD = 18.5 ZAR
  },
  KES: {
    USD: 0.0077, // 1 KES = 0.0077 USD
    KES: 1,
    EUR: 0.0065,
    GBP: 0.0056,
    NGN: 6.15,
    ZAR: 0.14,
  },
  EUR: {
    USD: 1.18,
    KES: 153,
    EUR: 1,
    GBP: 0.86,
    NGN: 940,
    ZAR: 21.8,
  },
  GBP: {
    USD: 1.37,
    KES: 178,
    EUR: 1.16,
    GBP: 1,
    NGN: 1095,
    ZAR: 25.3,
  },
  NGN: {
    USD: 0.00125,
    KES: 0.16,
    EUR: 0.00106,
    GBP: 0.00091,
    NGN: 1,
    ZAR: 0.023,
  },
  ZAR: {
    USD: 0.054,
    KES: 7.02,
    EUR: 0.046,
    GBP: 0.040,
    NGN: 43.2,
    ZAR: 1,
  }
};

export interface CurrencyConversion {
  fromCurrency: string;
  toCurrency: string;
  originalAmount: number;
  convertedAmount: number;
  exchangeRate: number;
  convertedAt: string;
}

export interface ConversionRecord {
  id: string;
  spaceId: string;
  fromCurrency: string;
  toCurrency: string;
  exchangeRate: number;
  convertedAt: string;
  itemsConverted: {
    services: number;
    products: number;
    revenueTargets: number;
    invoices: number;
  };
}

export const currencyService = {
  /**
   * Get current exchange rate between two currencies
   */
  getExchangeRate(fromCurrency: string, toCurrency: string): number {
    if (fromCurrency === toCurrency) return 1;
    
    const rate = EXCHANGE_RATES[fromCurrency]?.[toCurrency];
    if (!rate) {
      console.warn(`Exchange rate not found for ${fromCurrency} to ${toCurrency}, using 1:1`);
      return 1;
    }
    
    return rate;
  },

  /**
   * Convert amount from one currency to another
   */
  convertAmount(amount: number, fromCurrency: string, toCurrency: string): number {
    const rate = this.getExchangeRate(fromCurrency, toCurrency);
    const converted = amount * rate;
    
    // Round to 2 decimal places for most currencies, but handle special cases
    if (toCurrency === 'KES' || toCurrency === 'NGN') {
      // For currencies with lower values per unit, round to nearest whole number
      return Math.round(converted);
    }
    
    return Math.round(converted * 100) / 100;
  },

  /**
   * Convert all prices in a space when currency changes
   */
  async convertSpaceCurrency(spaceId: string, fromCurrency: string, toCurrency: string): Promise<ConversionRecord> {
    if (fromCurrency === toCurrency) {
      throw new Error('Source and target currencies are the same');
    }

    const exchangeRate = this.getExchangeRate(fromCurrency, toCurrency);
    const batch = writeBatch(db);
    let itemsConverted = {
      services: 0,
      products: 0,
      revenueTargets: 0,
      invoices: 0
    };

    try {
      // 1. Convert Services
      const servicesQuery = query(
        collection(db, 'services'),
        where('spaceId', '==', spaceId)
      );
      const servicesSnapshot = await getDocs(servicesQuery);
      
      servicesSnapshot.docs.forEach(doc => {
        const service = doc.data();
        const convertedPrice = this.convertAmount(service.price, fromCurrency, toCurrency);
        
        batch.update(doc.ref, {
          price: convertedPrice,
          originalPrice: service.price,
          originalCurrency: fromCurrency,
          convertedAt: new Date().toISOString(),
          exchangeRate: exchangeRate
        });
        
        itemsConverted.services++;
      });

      // 2. Convert Inventory/Products (if you have them)
      const inventoryQuery = query(
        collection(db, 'inventory'),
        where('spaceId', '==', spaceId)
      );
      const inventorySnapshot = await getDocs(inventoryQuery);
      
      inventorySnapshot.docs.forEach(doc => {
        const item = doc.data();
        if (item.cost) {
          const convertedCost = this.convertAmount(item.cost, fromCurrency, toCurrency);
          
          batch.update(doc.ref, {
            cost: convertedCost,
            originalCost: item.cost,
            originalCurrency: fromCurrency,
            convertedAt: new Date().toISOString(),
            exchangeRate: exchangeRate
          });
          
          itemsConverted.products++;
        }
      });

      // 3. Convert Revenue Targets
      const revenueTargetsQuery = query(
        collection(db, 'revenueTargets'),
        where('spaceId', '==', spaceId)
      );
      const revenueTargetsSnapshot = await getDocs(revenueTargetsQuery);
      
      revenueTargetsSnapshot.docs.forEach(doc => {
        const target = doc.data();
        const convertedAmount = this.convertAmount(target.amount, fromCurrency, toCurrency);
        
        batch.update(doc.ref, {
          amount: convertedAmount,
          originalAmount: target.amount,
          originalCurrency: fromCurrency,
          convertedAt: new Date().toISOString(),
          exchangeRate: exchangeRate
        });
        
        itemsConverted.revenueTargets++;
      });

      // 4. Convert Recent Invoices (last 30 days to avoid massive updates)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const invoicesQuery = query(
        collection(db, 'invoices'),
        where('spaceId', '==', spaceId)
      );
      const invoicesSnapshot = await getDocs(invoicesQuery);
      
      invoicesSnapshot.docs.forEach(doc => {
        const invoice = doc.data();
        const invoiceDate = new Date(invoice.createdAt);
        
        // Only convert recent invoices
        if (invoiceDate >= thirtyDaysAgo) {
          const convertedTotal = this.convertAmount(invoice.total, fromCurrency, toCurrency);
          const convertedSubtotal = this.convertAmount(invoice.subtotal, fromCurrency, toCurrency);
          const convertedTax = this.convertAmount(invoice.tax || 0, fromCurrency, toCurrency);
          const convertedTip = this.convertAmount(invoice.tip || 0, fromCurrency, toCurrency);
          
          batch.update(doc.ref, {
            total: convertedTotal,
            subtotal: convertedSubtotal,
            tax: convertedTax,
            tip: convertedTip,
            originalTotal: invoice.total,
            originalCurrency: fromCurrency,
            convertedAt: new Date().toISOString(),
            exchangeRate: exchangeRate
          });
          
          itemsConverted.invoices++;
        }
      });

      // Commit all changes
      await batch.commit();

      // Create conversion record
      const conversionRecord: ConversionRecord = {
        id: `${spaceId}_${Date.now()}`,
        spaceId,
        fromCurrency,
        toCurrency,
        exchangeRate,
        convertedAt: new Date().toISOString(),
        itemsConverted
      };

      // Save conversion record
      await setDoc(
        doc(db, 'currency-conversions', conversionRecord.id),
        conversionRecord
      );

      return conversionRecord;

    } catch (error) {
      console.error('Error converting space currency:', error);
      throw error;
    }
  },

  /**
   * Get conversion history for a space
   */
  async getConversionHistory(spaceId: string): Promise<ConversionRecord[]> {
    try {
      const conversionsQuery = query(
        collection(db, 'currency-conversions'),
        where('spaceId', '==', spaceId)
      );
      const snapshot = await getDocs(conversionsQuery);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ConversionRecord[];
    } catch (error) {
      console.error('Error fetching conversion history:', error);
      return [];
    }
  },

  /**
   * Check if space has been converted recently (within 24 hours)
   */
  async canConvertCurrency(spaceId: string): Promise<{ canConvert: boolean; lastConversion?: ConversionRecord }> {
    const history = await this.getConversionHistory(spaceId);
    
    if (history.length === 0) {
      return { canConvert: true };
    }

    const lastConversion = history.sort((a, b) => 
      new Date(b.convertedAt).getTime() - new Date(a.convertedAt).getTime()
    )[0];

    const lastConversionTime = new Date(lastConversion.convertedAt);
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    const canConvert = lastConversionTime < twentyFourHoursAgo;

    return {
      canConvert,
      lastConversion: canConvert ? undefined : lastConversion
    };
  },

  /**
   * Format currency with proper symbols and formatting
   */
  formatCurrency(amount: number, currency: string): string {
    const formatters: Record<string, Intl.NumberFormat> = {
      USD: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),
      KES: new Intl.NumberFormat('en-KE', { style: 'currency', currency: 'KES' }),
      EUR: new Intl.NumberFormat('en-EU', { style: 'currency', currency: 'EUR' }),
      GBP: new Intl.NumberFormat('en-GB', { style: 'currency', currency: 'GBP' }),
      NGN: new Intl.NumberFormat('en-NG', { style: 'currency', currency: 'NGN' }),
      ZAR: new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR' }),
    };

    const formatter = formatters[currency];
    if (!formatter) {
      return `${currency} ${amount.toFixed(2)}`;
    }

    return formatter.format(amount);
  }
};
