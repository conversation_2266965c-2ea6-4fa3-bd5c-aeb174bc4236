import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { getSubscriptionStatus } from './subscriptionService';

export interface UsageStats {
  userId: string;
  currentMonth: string; // YYYY-MM format
  appointmentsCount: number;
  customersCount: number;
  spacesCount: number;
  lastUpdated: Timestamp;
}

// Get current month in YYYY-MM format
const getCurrentMonth = (): string => {
  const now = new Date();
  return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
};

// Get user's current usage stats
export const getUserUsage = async (userId: string): Promise<UsageStats> => {
  try {
    const currentMonth = getCurrentMonth();
    const usageRef = doc(db, 'usage', `${userId}_${currentMonth}`);
    const usageDoc = await getDoc(usageRef);
    
    if (usageDoc.exists()) {
      return usageDoc.data() as UsageStats;
    }
    
    // Initialize usage stats for new month
    const initialStats: UsageStats = {
      userId,
      currentMonth,
      appointmentsCount: 0,
      customersCount: 0,
      spacesCount: 0,
      lastUpdated: Timestamp.now()
    };
    
    await setDoc(usageRef, initialStats);
    return initialStats;
  } catch (error) {
    console.error('Error getting user usage:', error);
    // Return safe defaults
    return {
      userId,
      currentMonth: getCurrentMonth(),
      appointmentsCount: 0,
      customersCount: 0,
      spacesCount: 0,
      lastUpdated: Timestamp.now()
    };
  }
};

// Check if user can perform an action based on their plan limits
export const canPerformAction = async (
  userId: string,
  action: 'create_appointment' | 'create_customer' | 'create_space'
): Promise<{ allowed: boolean; reason?: string; limit: number; current: number }> => {
  try {
    const [subscription, usage] = await Promise.all([
      getSubscriptionStatus(userId),
      getUserUsage(userId)
    ]);
    
    const limits = subscription.limits;
    
    switch (action) {
      case 'create_appointment':
        const appointmentLimit = limits.appointmentsPerMonth;
        if (appointmentLimit === -1) {
          return { allowed: true, limit: -1, current: usage.appointmentsCount };
        }
        const canCreateAppointment = usage.appointmentsCount < appointmentLimit;
        return {
          allowed: canCreateAppointment,
          reason: canCreateAppointment ? undefined : `Monthly appointment limit of ${appointmentLimit} reached`,
          limit: appointmentLimit,
          current: usage.appointmentsCount
        };
      
      case 'create_customer':
        const customerLimit = limits.customers;
        if (customerLimit === -1) {
          return { allowed: true, limit: -1, current: usage.customersCount };
        }
        const canCreateCustomer = usage.customersCount < customerLimit;
        return {
          allowed: canCreateCustomer,
          reason: canCreateCustomer ? undefined : `Customer limit of ${customerLimit} reached`,
          limit: customerLimit,
          current: usage.customersCount
        };
      
      case 'create_space':
        const spaceLimit = limits.spaces;
        if (spaceLimit === -1) {
          return { allowed: true, limit: -1, current: usage.spacesCount };
        }
        const canCreateSpace = usage.spacesCount < spaceLimit;
        return {
          allowed: canCreateSpace,
          reason: canCreateSpace ? undefined : `Space limit of ${spaceLimit} reached`,
          limit: spaceLimit,
          current: usage.spacesCount
        };
      
      default:
        return { allowed: false, reason: 'Unknown action', limit: 0, current: 0 };
    }
  } catch (error) {
    console.error('Error checking action permissions:', error);
    return { allowed: false, reason: 'Error checking permissions', limit: 0, current: 0 };
  }
};

// Increment usage counter when action is performed
export const incrementUsage = async (
  userId: string, 
  type: 'appointments' | 'customers' | 'spaces',
  count: number = 1
): Promise<void> => {
  try {
    const currentMonth = getCurrentMonth();
    const usageRef = doc(db, 'usage', `${userId}_${currentMonth}`);
    
    // Get current usage or create if doesn't exist
    const usage = await getUserUsage(userId);
    
    // Update the appropriate counter
    const updates: Partial<UsageStats> = {
      lastUpdated: Timestamp.now()
    };
    
    switch (type) {
      case 'appointments':
        updates.appointmentsCount = usage.appointmentsCount + count;
        break;
      case 'customers':
        updates.customersCount = usage.customersCount + count;
        break;
      case 'spaces':
        updates.spacesCount = usage.spacesCount + count;
        break;
    }
    
    await updateDoc(usageRef, updates);
  } catch (error) {
    console.error('Error incrementing usage:', error);
    throw error;
  }
};

// Decrement usage counter when action is undone (e.g., appointment cancelled)
export const decrementUsage = async (
  userId: string, 
  type: 'appointments' | 'customers' | 'spaces',
  count: number = 1
): Promise<void> => {
  try {
    const currentMonth = getCurrentMonth();
    const usageRef = doc(db, 'usage', `${userId}_${currentMonth}`);
    
    const usage = await getUserUsage(userId);
    
    const updates: Partial<UsageStats> = {
      lastUpdated: Timestamp.now()
    };
    
    switch (type) {
      case 'appointments':
        updates.appointmentsCount = Math.max(0, usage.appointmentsCount - count);
        break;
      case 'customers':
        updates.customersCount = Math.max(0, usage.customersCount - count);
        break;
      case 'spaces':
        updates.spacesCount = Math.max(0, usage.spacesCount - count);
        break;
    }
    
    await updateDoc(usageRef, updates);
  } catch (error) {
    console.error('Error decrementing usage:', error);
    throw error;
  }
};

// Get usage stats for display in UI
export const getUsageForDisplay = async (userId: string) => {
  try {
    const [subscription, usage] = await Promise.all([
      getSubscriptionStatus(userId),
      getUserUsage(userId)
    ]);
    
    const limits = subscription.limits;
    
    return {
      appointments: {
        current: usage.appointmentsCount,
        limit: limits.appointmentsPerMonth,
        unlimited: limits.appointmentsPerMonth === -1,
        percentage: limits.appointmentsPerMonth === -1 ? 0 : (usage.appointmentsCount / limits.appointmentsPerMonth) * 100
      },
      customers: {
        current: usage.customersCount,
        limit: limits.customers,
        unlimited: limits.customers === -1,
        percentage: limits.customers === -1 ? 0 : (usage.customersCount / limits.customers) * 100
      },
      spaces: {
        current: usage.spacesCount,
        limit: limits.spaces,
        unlimited: limits.spaces === -1,
        percentage: limits.spaces === -1 ? 0 : (usage.spacesCount / limits.spaces) * 100
      },
      planName: subscription.tier,
      isFreePlan: subscription.tier === 'free'
    };
  } catch (error) {
    console.error('Error getting usage for display:', error);
    return null;
  }
};