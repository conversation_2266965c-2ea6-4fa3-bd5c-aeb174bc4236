import {
  appointmentService,
  serviceService,
  staffService,
  spaceService,
  customerService
} from './firestore';
import {
  Appointment,
  Service,
  Staff,
  Space,
} from './types/models';
import { format, parseISO, isWithinInterval, addMinutes, isBefore, isAfter } from 'date-fns';

/**
 * Format a phone number to E.164 format
 */
function formatToE164(phoneNumber: string): string | null {
  try {
    // Remove all non-digit characters
    const digitsOnly = phoneNumber.replace(/\D/g, '');

    // Check if it's already in E.164 format
    if (digitsOnly.startsWith('254')) {
      return `+${digitsOnly}`;
    }

    // If it starts with 0, replace with Kenya country code
    if (digitsOnly.startsWith('0')) {
      return `+254${digitsOnly.substring(1)}`;
    }

    // If it's a 9-digit number, assume it's missing the leading 0
    if (digitsOnly.length === 9) {
      return `+254${digitsOnly}`;
    }

    // If it's a 10-digit number starting with 7 or 1, assume it's a Kenyan number
    if (digitsOnly.length === 10 && (digitsOnly.startsWith('7') || digitsOnly.startsWith('1'))) {
      return `+254${digitsOnly}`;
    }

    // Return as is with + prefix if we can't determine the format
    return `+${digitsOnly}`;
  } catch (error) {
    console.error('Error formatting phone number:', error);
    return null;
  }
}

// Interface for business hours
export interface BusinessHour {
  day: string;
  open: string;
  close: string;
  enabled: boolean;
}

// Interface for available time slots
export interface TimeSlot {
  time: string;
  available: boolean;
  timestamp: Date;
}

// Interface for public appointment creation
export interface PublicAppointmentData {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  serviceId: string;
  staffId: string;
  spaceId: string;
  startTime: string;
  notes?: string;
}

export const publicAppointmentService = {
  /**
   * Get space details by ID
   */
  async getSpaceDetails(spaceId: string): Promise<Space | null> {
    try {
      return await spaceService.getById(spaceId);
    } catch (error) {
      console.error('Error fetching space details:', error);
      throw error;
    }
  },

  /**
   * Get all services for a space
   * @deprecated Use getSpaceServices instead
   */
  async getSalonServices(spaceId: string): Promise<Service[]> {
    return this.getSpaceServices(spaceId);
  },

  /**
   * Get all services for a space
   */
  async getSpaceServices(spaceId: string): Promise<Service[]> {
    try {
      const services = await serviceService.getAll();
      return services.filter(service =>
        service.status === 'Active' && (service.spaceId === spaceId || service.spaceId === spaceId)
      );
    } catch (error) {
      console.error('Error fetching space services:', error);
      throw error;
    }
  },

  /**
   * Get all staff for a space
   * @deprecated Use getSpaceStaff instead
   */
  async getSalonStaff(spaceId: string): Promise<Staff[]> {
    return this.getSpaceStaff(spaceId);
  },

  /**
   * Get all staff for a space
   */
  async getSpaceStaff(spaceId: string): Promise<Staff[]> {
    try {
      const allStaff = await staffService.getAll();
      return allStaff.filter(staff => staff.spaceId === spaceId || staff.spaceId === spaceId);
    } catch (error) {
      console.error('Error fetching space staff:', error);
      throw error;
    }
  },

  /**
   * Check if a space is open on a specific date and time
   * @deprecated Use isSpaceOpen instead
   */
  isSalonOpen(date: Date, businessHours: BusinessHour[]): boolean {
    return this.isSpaceOpen(date, businessHours);
  },

  /**
   * Check if a space is open on a specific date and time
   */
  isSpaceOpen(date: Date, businessHours: BusinessHour[]): boolean {
    const dayOfWeek = format(date, 'EEEE'); // Monday, Tuesday, etc.
    const daySettings = businessHours.find(day => day.day === dayOfWeek);

    if (!daySettings || !daySettings.enabled) {
      return false; // Space is closed on this day
    }

    const timeString = format(date, 'HH:mm');
    return timeString >= daySettings.open && timeString <= daySettings.close;
  },

  /**
   * Get staff appointments for a specific date
   */
  async getStaffAppointmentsForDate(staffId: string, date: Date): Promise<Appointment[]> {
    try {
      console.log(`Getting appointments for staff ${staffId} on ${date.toDateString()}`);
      const appointments = await appointmentService.getStaffAppointments(staffId, date);
      console.log(`Retrieved ${appointments.length} appointments`);
      return appointments;
    } catch (error) {
      console.error('Error fetching staff appointments:', error);
      // Return empty array instead of throwing to prevent the entire booking flow from breaking
      return [];
    }
  },

  /**
   * Check if a staff member is available for a specific time slot
   */
  isStaffAvailable(
    startTime: Date,
    duration: number,
    appointments: Appointment[]
  ): boolean {
    const endTime = addMinutes(startTime, duration);

    console.log('Checking staff availability for:', {
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration
    });
    console.log('Existing appointments:', appointments.map(a => ({
      id: a.id,
      start: a.startTime,
      end: a.endTime,
      status: a.status
    })));

    // Check if the time slot overlaps with any existing appointments
    const isUnavailable = appointments.some(appointment => {
      if (appointment.status === 'cancelled') {
        return false; // Cancelled appointments don't block the time slot
      }

      const appointmentStart = parseISO(appointment.startTime);
      const appointmentEnd = parseISO(appointment.endTime);

      // Check for overlap
      const hasOverlap = (
        (isWithinInterval(startTime, { start: appointmentStart, end: appointmentEnd }) ||
        isWithinInterval(endTime, { start: appointmentStart, end: appointmentEnd }) ||
        (isBefore(startTime, appointmentStart) && isAfter(endTime, appointmentEnd)))
      );

      if (hasOverlap) {
        console.log('Overlap found with appointment:', {
          id: appointment.id,
          start: appointment.startTime,
          end: appointment.endTime
        });
      }

      return hasOverlap;
    });

    return !isUnavailable;
  },

  /**
   * Generate available time slots for a specific date, service, and staff
   */
  async generateAvailableTimeSlots(
    date: Date,
    serviceId: string,
    staffId: string,
    spaceId: string,
    businessHours: BusinessHour[]
  ): Promise<TimeSlot[]> {
    try {
      console.log('Generating time slots with params:', { date, serviceId, staffId, spaceId });

      // Get service details to know the duration
      const service = await serviceService.getById(serviceId);
      if (!service) {
        console.error('Service not found:', serviceId);
        throw new Error('Service not found');
      }
      console.log('Service found:', service);

      // Get staff appointments for the date
      const appointments = await this.getStaffAppointmentsForDate(staffId, date);
      console.log('Staff appointments for date:', appointments);

      // Get business hours for the day
      const dayOfWeek = format(date, 'EEEE');
      console.log('Day of week:', dayOfWeek);
      console.log('Business hours:', businessHours);

      const daySettings = businessHours.find(day => day.day === dayOfWeek);
      console.log('Day settings:', daySettings);

      if (!daySettings || !daySettings.enabled) {
        console.log('Space is closed on this day');
        return []; // Space is closed on this day
      }

      // Generate time slots from opening to closing time
      const timeSlots: TimeSlot[] = [];
      const [openHour, openMinute] = daySettings.open.split(':').map(Number);
      const [closeHour, closeMinute] = daySettings.close.split(':').map(Number);

      console.log('Opening hours:', { openHour, openMinute, closeHour, closeMinute });

      // Set the start and end times for the day
      const startOfDay = new Date(date);
      startOfDay.setHours(openHour, openMinute, 0, 0);

      const endOfDay = new Date(date);
      endOfDay.setHours(closeHour, closeMinute, 0, 0);

      console.log('Start of day:', startOfDay);
      console.log('End of day:', endOfDay);

      // Generate slots in 30-minute intervals
      const slotInterval = 30; // minutes
      let currentSlot = new Date(startOfDay);

      while (currentSlot < endOfDay) {
        // Check if there's enough time for the service before closing
        const serviceEndTime = addMinutes(currentSlot, service.duration);
        if (serviceEndTime > endOfDay) {
          console.log('Not enough time for service before closing');
          break;
        }

        // Check if the staff is available for this time slot
        const isAvailable = this.isStaffAvailable(
          currentSlot,
          service.duration,
          appointments
        );

        timeSlots.push({
          time: format(currentSlot, 'h:mm a'),
          available: isAvailable,
          timestamp: new Date(currentSlot)
        });

        // Move to the next slot
        currentSlot = addMinutes(currentSlot, slotInterval);
      }

      console.log('Generated time slots:', timeSlots);
      return timeSlots;
    } catch (error) {
      console.error('Error generating available time slots:', error);
      throw error;
    }
  },

  /**
   * Create a new appointment for a public user
   */
  async createPublicAppointment(data: PublicAppointmentData): Promise<Appointment> {
    try {
      console.log('Creating public appointment with data:', data);

      // Get service details to calculate end time
      const service = await serviceService.getById(data.serviceId);
      if (!service) {
        throw new Error('Service not found');
      }

      // Parse the start time
      const startTime = new Date(data.startTime);

      // Calculate end time based on service duration
      const endTime = addMinutes(startTime, service.duration);

      // For public bookings, use a guest customer approach to avoid permission issues
      // The customer information will be stored in the appointment notes
      const customerId = `guest-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      console.log('Using guest customer ID for public booking:', customerId);
      // Create the appointment
      console.log('Creating appointment with customer ID:', customerId);
      const appointment = await appointmentService.create({
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        serviceId: data.serviceId,
        staffId: data.staffId,
        customerId: customerId,
      
        spaceId: data.spaceId,
        status: 'scheduled',
        notes: `Public booking by ${data.customerName} | Email: ${data.customerEmail} | Phone: ${data.customerPhone}${data.notes ? ` | Notes: ${data.notes}` : ''}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      

      console.log('Appointment created successfully:', appointment);
      return appointment;
    } catch (error) {
      console.error('Error creating public appointment:', error);
      throw error;
    }
  }
};
