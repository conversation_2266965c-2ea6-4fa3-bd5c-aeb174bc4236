import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { SubscriptionTier } from '@/services/subscriptionService';

/**
 * Check if a space owner is on the free plan
 * This is used to determine if GroomBook branding should be shown
 */
export async function isSpaceOwnerOnFreePlan(spaceId: string): Promise<boolean> {
  try {
    // Get space document to find the owner
    const spaceRef = doc(db, 'spaces', spaceId);
    const spaceDoc = await getDoc(spaceRef);
    
    if (!spaceDoc.exists()) {
      console.error('Space not found:', spaceId);
      return true; // Default to showing branding if space not found
    }
    
    const spaceData = spaceDoc.data();
    const ownerId = spaceData.ownerId || spaceData.userId;
    
    if (!ownerId) {
      console.error('No owner found for space:', spaceId);
      return true; // Default to showing branding if no owner
    }
    
    // Get user document to check subscription
    const userRef = doc(db, 'users', ownerId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.error('User not found:', ownerId);
      return true; // Default to showing branding if user not found
    }
    
    const userData = userDoc.data();
    const subscriptionId = userData.subscriptionId;
    
    // If no subscription, user is on free plan
    if (!subscriptionId) {
      return true;
    }
    
    // Get subscription details
    const subscriptionRef = doc(db, 'subscriptions', subscriptionId);
    const subscriptionDoc = await getDoc(subscriptionRef);
    
    if (!subscriptionDoc.exists()) {
      return true; // Default to showing branding if subscription not found
    }
    
    const subscription = subscriptionDoc.data();
    const tier = subscription.tier || SubscriptionTier.FREE;
    const isActive = subscription.status === 'active';
    
    // Show branding if user is on free tier or subscription is not active
    return tier === SubscriptionTier.FREE || !isActive;
    
  } catch (error) {
    console.error('Error checking space owner subscription:', error);
    return true; // Default to showing branding on error
  }
}
