import { collection, addDoc, query, where, getDocs, updateDoc, doc, Timestamp, orderBy } from 'firebase/firestore';
import { db } from '../utils/firebase';
// import { sendEmail } from '../utils/email'; // deprecated: use API route /api/send-email instead

export type NotificationType = 'service_completion' | 'inventory' | 'appointment' | 'payment' | 'referral';

export interface Notification {
  id?: string;
  title: string;
  message: string;
  type: NotificationType;
  spaceId?: string;  // New field
  createdAt: Date | Timestamp;
  read: boolean;
  data?: Record<string, any>; // Additional data specific to the notification type
}

export const notificationService = {
  /**
   * Create a new notification
   */
  async create(notification: Omit<Notification, 'id' | 'createdAt' | 'read'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'notifications'), {
        ...notification,
        createdAt: Timestamp.now(),
        read: false
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  },

  /**
   * Get all notifications for a space
   */
  async getForSpace(spaceId: string): Promise<Notification[]> {
    try {
      const q = query(
        collection(db, 'notifications'),
        where('spaceId', '==', spaceId),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate()
      })) as Notification[];
    } catch (error) {
      console.error('Error getting notifications:', error);
      throw error;
    }
  },



  /**
   * Get notifications by type for a space
   */
  async getByType(spaceId: string, type: NotificationType): Promise<Notification[]> {
    try {
      // For backward compatibility, check both spaceId and spaceId
      const qSpace = query(
        collection(db, 'notifications'),
        where('spaceId', '==', spaceId),
        where('type', '==', type),
        orderBy('createdAt', 'desc')
      );
      
      
      
      const [spaceSnapshot] = await Promise.all([
        getDocs(qSpace),
      ]);

      // Combine and deduplicate results
      const uniqueNotifications = new Map();
      
      // Process notifications from spaceId query
      spaceSnapshot.docs.forEach(doc => {
        uniqueNotifications.set(doc.id, {
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt.toDate()
        });
      });
      
      
      
      // Convert to array and return
      return Array.from(uniqueNotifications.values()) as Notification[];
    } catch (error) {
      console.error(`Error getting ${type} notifications:`, error);
      return [];
    }
  },

  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        read: true
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  },

  /**
   * Mark all notifications for a space as read
   */
  async markAllAsRead(spaceId: string): Promise<void> {
    try {
      const qSpace = query(
        collection(db, 'notifications'),
        where('spaceId', '==', spaceId),
        where('read', '==', false)
      );
      
      const spaceSnapshot = await getDocs(qSpace);

      // Process all notifications
      const updatePromises = spaceSnapshot.docs.map(docSnapshot => {
        const notificationRef = doc(db, 'notifications', docSnapshot.id);
        return updateDoc(notificationRef, { read: true });
      });

      if (updatePromises.length > 0) {
        await Promise.all(updatePromises);
      }
    } catch (error) {
      throw error;
    }
  },

  /**
   * Create a service completion notification
   */
  async createServiceCompletionNotification(
    spaceId: string,
    serviceId: string,
    customerName: string,
    serviceName: string,
    staffName: string,
    totalAmount: number
  ): Promise<string> {
    return this.create({
      title: 'Service Awaiting Payment',
      message: `${staffName} completed ${serviceName} for ${customerName} - Payment of KES ${totalAmount} pending`,
      type: 'service_completion',
      spaceId,
      data: {
        serviceId,
        customerName,
        serviceName,
        staffName,
        totalAmount
      }
    });
  },

  /**
   * Send a referral invitation email
   */
  async sendReferralInvitation({
    recipientEmail,
    senderName,
    referralCode,
    referralUrl,
    customMessage
  }: {
    recipientEmail: string;
    senderName: string;
    referralCode: string;
    referralUrl: string;
    customMessage?: string;
  }): Promise<boolean> {
    try {
      // Import React Email components and render function
      const { render } = await import('@react-email/render');
      const { ReferralEmail } = await import('@/emails/ReferralEmail');
      
      // Create email subject and content
      const subject = `${senderName} has invited you to join GroomBook!`;
      
      // Generate text content from custom message or default
      const textContent = customMessage || 
        `${senderName} has invited you to GroomBook for managing your service space! Use referral code ${referralCode} when signing up.`;
      
      // Render React Email component to HTML
      const htmlContent = await render(ReferralEmail({
        senderName,
        referralCode,
        referralUrl,
        customMessage
      }));
      
      // Always call the email API route
      try {
        const response = await fetch('/api/send-email', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            to: recipientEmail,
            subject,
            text: textContent,
            html: htmlContent
          }),
        });
        const data = await response.json();
        return data.success;
      } catch (err) {
        console.error('Error calling send-email API:', err);
        return false;
      }
    } catch (error) {
      console.error('Error sending referral invitation:', error);
      return false;
    }
  }
};
