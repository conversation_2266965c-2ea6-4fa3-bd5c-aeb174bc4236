import { collection, query, getDocs, where } from 'firebase/firestore';
import { format, startOfDay, endOfDay } from 'date-fns';
import { db } from '@/utils/firebase';
import { loyaltyService } from './loyaltyService';
import { Customer } from './types/models';

// Environment-aware logger
const logger = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[INFO] ${message}`, data);
    }
    // In production, you'd integrate with Sentry or another logging service here
  },
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.error(`[ERROR] ${message}`, error);
    }
    // In production, you'd integrate with Sentry or another logging service here
  }
};

export const birthdayService = {
  /**
   * Find all customers with birthdays today
   */
  async findTodaysBirthdayCelebrants(): Promise<Customer[]> {
    try {
      const today = new Date();
      const dayMonth = format(today, 'MM-dd'); // Format as month-day (e.g., "01-15" for January 15)
      
      // Get all customers with loyalty program enrollment
      const customersRef = collection(db, 'customers');
      const customersQuery = query(
        customersRef,
        where('loyaltyPoints', '!=', null)
      );
      
      const customersSnapshot = await getDocs(customersQuery);
      const customers: Customer[] = [];
      
      customersSnapshot.forEach(doc => {
        const customer = {
          id: doc.id,
          ...doc.data()
        } as Customer;
        
        // Check if customer has a birthdate in loyaltyPoints
        if (customer.loyaltyPoints?.birthdate) {
          const birthdate = new Date(customer.loyaltyPoints.birthdate);
          const customerBirthDayMonth = format(birthdate, 'MM-dd');
          
          // If it's this customer's birthday, add them to the list
          if (customerBirthDayMonth === dayMonth) {
            customers.push(customer);
          }
        }
      });
      
      return customers;
    } catch (error) {
      logger.error('Error finding birthday celebrants:', error);
      return [];
    }
  },
  
  /**
   * Process birthday rewards for all celebrating customers
   */
  async processBirthdayRewards(): Promise<{
    success: number;
    failure: number;
    customers: string[];
    failedCustomers: Array<{id: string, name: string, error: string}>;
  }> {
    try {
      const birthdayCelebrants = await this.findTodaysBirthdayCelebrants();
      logger.info(`Found ${birthdayCelebrants.length} birthday celebrants`);
      
      const results = {
        success: 0,
        failure: 0,
        customers: [] as string[],
        failedCustomers: [] as Array<{id: string, name: string, error: string}>
      };
      
      // Process each customer
      for (const customer of birthdayCelebrants) {
        try {
          // Check if birthday points have already been awarded today
          const transactionsToday = await loyaltyService.getCustomerTransactionHistory(customer.id);
          const alreadyAwarded = transactionsToday.some(t => 
            t.source === 'birthday' && 
            new Date(t.createdAt).toDateString() === new Date().toDateString()
          );
          
          if (alreadyAwarded) {
            logger.info(`Birthday points already awarded to ${customer.displayName} today`);
            continue;
          }
          
          // Award birthday points
          await loyaltyService.awardBirthdayPoints(customer.id);
          
          results.success++;
          results.customers.push(customer.displayName);
          
          logger.info(`Awarded birthday points to ${customer.displayName}`);
        } catch (error) {
          results.failure++;
          const errorMessage = error instanceof Error ? error.message : String(error);
          
          results.failedCustomers.push({
            id: customer.id,
            name: customer.displayName,
            error: errorMessage
          });
          
          logger.error(`Failed to award birthday points to ${customer.displayName}:`, error);
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Error processing birthday rewards:', error);
      return {
        success: 0,
        failure: 0,
        customers: [],
        failedCustomers: []
      };
    }
  },
  
  /**
   * Check if a specific customer has a birthday today
   */
  isBirthdayToday(birthdateString: string): boolean {
    if (!birthdateString) return false;
    
    try {
      const birthdate = new Date(birthdateString);
      const today = new Date();
      
      return format(birthdate, 'MM-dd') === format(today, 'MM-dd');
    } catch (error) {
      logger.error('Error checking birthday:', error);
      return false;
    }
  }
};