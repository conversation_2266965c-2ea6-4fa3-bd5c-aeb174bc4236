export interface User {
    id: string;
    email: string;
    displayName: string;
    role: 'admin' | 'staff' | 'customer';
    phoneNumber: string;
    photoURL?: string;
    createdAt: string;
    updatedAt: string;
  }

  export interface Staff extends User {
    role: 'staff';
    specialties: string[];
    bio?: string; // Staff member's bio/description
    experience?: {
      years: number;
      since: string; // ISO date string when they started
      previousWorkplaces?: string[];
    };
    certifications?: string[]; // Professional certifications
    availability: {
      [key: string]: {
        start: string;
        end: string;
      }[];
    };
    commission: number;
    services: string[]; // IDs of services this staff member can perform
    photoURL?: string;
    spaceId: string; // ID of the space this staff member belongs to
    rating?: number; // Average rating from reviews
    pin?: {
      value: string;
      hasChangedDefault: boolean;
      lastUpdated: string;
    };
    subscription?: {
      plan: 'free' | 'monthly' | 'yearly';
      startDate: string;
      endDate: string;
      isTrial: boolean;
      status: 'active' | 'expired' | 'cancelled';
    };
  }

  export interface Service {
    id: string;
    name: string;
    description: string;
    category: string;
    price: number;
    duration: number; // in minutes
    status: 'Active' | 'Inactive';
    spaceId: string; // ID of the space this service belongs to
    createdAt: string;
    updatedAt: string;
  }

  export interface Customer extends User {
    role: 'customer';
    visits: number;
    lastVisit?: string;
    notes?: string;
    preferences?: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      [key: string]: any;
    };
    loyaltyPoints?: {
      balance: number;
      tier?: 'bronze' | 'silver' | 'gold' | 'platinum';
      updatedAt: string;
      birthdate?: string;
      referralCode?: string;
    };
  }

  export interface PaymentIntegration {
    id: string;
    type: 'mpesa' | 'card';
    isActive: boolean;
    details: {
      paybillNumber?: string;
      tillNumber?: string;
      merchantId?: string;
      shortCode?: string;
      consumerKey?: string;
      consumerSecret?: string;
    };
    createdAt: Date;
    updatedAt: Date;
  }

  export interface PaymentTransaction {
    id: string;
    invoiceId: string;
    amount: number;
    paymentMethod: 'mpesa' | 'cash' | 'card';
    status: 'pending' | 'completed' | 'failed';
    mpesaDetails?: {
      transactionCode: string;
      phoneNumber: string;
      timestamp: Date;
    };
    cashDetails?: {
      amountGiven: number;
      change: number;
      receivedBy: string;
    };
    cardDetails?: {
      last4: string;
      brand: string;
      authCode: string;
    };
    createdAt: Date;
    updatedAt: Date;
  }

  export interface Invoice {
    id: string;
    customerId: string;
    staffId: string;
    spaceId?: string;  // New field for spaces
    services: {
      serviceId: string;
      name: string;
      price: number;
      quantity: number;
    }[];
    subtotal: number;
    tax: number;
    tip?: number;
    total: number;
    status: 'pending' | 'paid' | 'failed' | 'cancelled';
    paymentTransactions: PaymentTransaction[];
    createdAt: Date;
    updatedAt: Date;
  }

  export interface Space {
    id: string;
    name: string;
    type: 'salon' | 'barbershop' | 'spa' | 'fitness' | 'other';
    description?: string;
    logo?: string;
    phone: string;
    email: string;
    address: string;
    city: string;
    state: string;
    postalCode: string;
    website?: string;
    instagram?: string;
    facebook?: string;
    createdAt: string;
    updatedAt: string;
    ownerId: string;
    // Business hours
    businessHours?: BusinessHour[];
    // Currency settings
    currency?: {
      code: string;
      symbol: string;
      name: string;
      position: 'prefix' | 'suffix';
    };
    // M-PESA Integration
    mpesaEnabled?: boolean;
    mpesaBusinessName?: string;
    mpesaAccountType?: 'till' | 'paybill';
    mpesaShortcode?: string;
    mpesaAccountNumber?: string;
    mpesaPhoneNumber?: string;
    mpesaAutoConfirm?: boolean;
    // Payment modes
    paymentModes?: {
      cash: boolean;
      card: boolean;
      mpesa: boolean;
    };
    // Notification preferences
    notifications?: {
      email: boolean;
      sms: boolean;
      reminders: boolean;
    };
  }

  export interface BusinessHour {
    day: string;
    open: string;
    close: string;
    enabled: boolean;
  }


  export interface Shop {
    id: string;
    name: string;
    address: string;
    phoneNumber: string;
    email: string;
    businessHours: {
      [key: string]: {
        start: string;
        end: string;
      };
    };
    settings: {
      taxRate: number;
      currency: string;
      mpesaPaybillNumber?: string;
      mpesaTillNumber?: string;
    };
  }

  export interface Appointment {
    id: string
    startTime: string
    endTime: string
    serviceId: string
    staffId: string
    customerId: string
    spaceId: string
    status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no-show'
    createdAt: string
    updatedAt: string
    notes?: string
  }

  export interface ActiveService {
    id: string
    customerId: string
    customerName: string
    customerPhone: string
    staffId: string
    staffName: string
    services: {
      id: string
      name: string
      price: number
      duration: number
    }[]
    status: 'in_progress' | 'completed' | 'awaiting_payment' | 'paid' | 'cancelled'
    startTime: Date
    endTime?: Date
    totalAmount: number
    progress: number
    notes?: string
    staffAvatar: string
    spaceId: string
  }

  export interface RevenueTarget {
    id: string;
    spaceId: string;
    type: 'daily' | 'monthly' | 'annual';
    amount: number;
    startDate: string;
    endDate?: string;
    isRecurring: boolean;
    createdAt: string;
    updatedAt: string;
  }

  // Commission and Accounting Models
  export interface CommissionRecord {
    id: string;
    staffId: string;
    staffName: string;
    serviceId: string; // active-service ID
    serviceName: string;
    serviceAmount: number;
    commissionRate: number; // percentage (e.g., 15 for 15%)
    commissionAmount: number;
    status: 'pending' | 'paid' | 'cancelled';
    createdAt: string;
    paidAt?: string;
    spaceId: string;
    paymentMethod?: 'cash' | 'bank_transfer' | 'mobile_money';
    notes?: string;
  }

  export interface ServiceProductUsage {
    id: string;
    serviceId: string; // active-service ID
    productId: string; // inventory item ID
    productName: string;
    quantityUsed: number;
    costPerUnit: number;
    totalCost: number;
    usedAt: string;
    spaceId: string;
    recordedBy: string; // staff ID who recorded the usage
  }

  export interface ServiceTransaction {
    id: string;
    serviceId: string; // active-service ID
    customerId: string;
    customerName: string;
    staffId: string;
    staffName: string;
    services: {
      id: string;
      name: string;
      price: number;
    }[];
    totalRevenue: number;
    productCosts: ServiceProductUsage[];
    totalProductCost: number;
    commissions: CommissionRecord[];
    totalCommissions: number;
    netProfit: number;
    completedAt: string;
    paidAt?: string;
    paymentMethod?: 'cash' | 'mpesa' | 'bank_transfer' | 'card';
    status: 'completed' | 'paid';
    spaceId: string;
    notes?: string;
  }

  export interface StaffEarnings {
    staffId: string;
    staffName: string;
    period: {
      start: string;
      end: string;
    };
    totalServices: number;
    totalRevenue: number;
    totalCommissions: number;
    paidCommissions: number;
    pendingCommissions: number;
    commissionRecords: CommissionRecord[];
    serviceBreakdown: {
      serviceName: string;
      count: number;
      revenue: number;
      commissions: number;
    }[];
  }

  export interface Review {
    id: string;
    activeServiceId: string; // Links the review to a completed service
    staffId: string;
    customerId: string;
    spaceId: string;
    rating: number; // 1-5 star rating
    comment: string;
    serviceDate: string; // Date of service
    createdAt: string;
    customerName: string;
    serviceIds: string[]; // Which services were reviewed
    staffResponse?: {
      comment: string;
      createdAt: string;
    };
    status: 'published' | 'hidden' | 'reported';
  }