/**
 * Protected loyalty operations that check feature access before executing
 */

import { hasFeatureAccess } from './subscriptionService';
import { loyaltyService } from './loyaltyService';

export class ProtectedLoyaltyService {
  /**
   * Award signup points only if user has loyalty feature access
   */
  static async awardPointsForSignup(customerId: string, userId: string): Promise<void> {
    const hasAccess = await hasFeatureAccess(userId, 'advanced_loyalty_program');
    if (!hasAccess) {
      console.log(`Skipping loyalty points award for user ${userId} - no loyalty feature access`);
      return;
    }
    
    await loyaltyService.awardPointsForSignup(customerId);
  }

  /**
   * Award appointment points only if user has loyalty feature access
   */
  static async awardPointsForAppointment(
    customerId: string, 
    appointmentId: string,
    totalAmount: number,
    userId: string
  ): Promise<void> {
    const hasAccess = await hasFeatureAccess(userId, 'advanced_loyalty_program');
    if (!hasAccess) {
      console.log(`Skipping loyalty points award for user ${userId} - no loyalty feature access`);
      return;
    }
    
    await loyaltyService.awardPointsForAppointment(customerId, appointmentId, totalAmount);
  }

  /**
   * Award referral points only if user has referral feature access
   */
  static async awardPointsForReferral(
    referrerId: string, 
    refereeId: string, 
    userId: string
  ): Promise<void> {
    const hasAccess = await hasFeatureAccess(userId, 'advanced_referral_system');
    if (!hasAccess) {
      console.log(`Skipping referral points award for user ${userId} - no referral feature access`);
      return;
    }
    
    await loyaltyService.awardPointsForReferral(referrerId, refereeId);
  }

  /**
   * Award birthday points only if user has loyalty feature access
   */
  static async awardBirthdayPoints(customerId: string, userId: string): Promise<void> {
    const hasAccess = await hasFeatureAccess(userId, 'advanced_loyalty_program');
    if (!hasAccess) {
      console.log(`Skipping birthday points award for user ${userId} - no loyalty feature access`);
      return;
    }
    
    await loyaltyService.awardBirthdayPoints(customerId);
  }
}
