import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc, setDoc, collection, query, where, getDocs, serverTimestamp, Timestamp, addDoc } from 'firebase/firestore';
import { SubscriptionTier } from './subscriptionService';
import { pricingPlans } from '@/lib/billing';

// Map Paddle price IDs to subscription tiers
// These should match the IDs in src/lib/billing.ts
export const PRICE_ID_TO_TIER = {
  'pri_01jw0x8s4xxz8v38ptt3ffqwtp': SubscriptionTier.BASIC, // Basic monthly - $9
  'pri_01jw0xgr4mg7r0fj5fqwv1mddj': SubscriptionTier.BASIC, // Basic yearly - $90
  'pri_01jw0xnpq49mv9qfr6nzv2qkjn': SubscriptionTier.PRO,  // Pro monthly
  'pri_01jw0xqmnys4sqhfazgsc7k0f4': SubscriptionTier.PRO,  // Pro yearly
  'pri_01jw0xxvjfgxdb1kn001qay4ha': SubscriptionTier.ENTERPRISE, // Enterprise monthly
  'pri_01jw0y01tdcfskjxa642xk41k7': SubscriptionTier.ENTERPRISE  // Enterprise yearly
};

/**
 * Get features array for a subscription tier
 */
export const getFeaturesForTier = (tier: SubscriptionTier): string[] => {
  switch (tier) {
    case SubscriptionTier.BASIC:
      return pricingPlans.basic.features;
    case SubscriptionTier.PRO:
      return pricingPlans.pro.features;
    case SubscriptionTier.ENTERPRISE:
      return pricingPlans.enterprise.features;
    case SubscriptionTier.FREE:
    default:
      return pricingPlans.free.features;
  }
};

/**
 * Get tier from Paddle price ID
 */
export const getTierFromPriceId = (priceId: string): SubscriptionTier => {
  return PRICE_ID_TO_TIER[priceId as keyof typeof PRICE_ID_TO_TIER] || SubscriptionTier.FREE;
};

/**
 * Find a user by Paddle customer ID
 */
export const findUserByCustomerId = async (paddleCustomerId: string) => {
  try {
    const subscriptionsRef = collection(db, 'subscriptions');
    const q = query(subscriptionsRef, where('customerId', '==', paddleCustomerId));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      // Get the first matching subscription
      const subscription = querySnapshot.docs[0].data();
      if (subscription.userId) {
        return subscription.userId;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error finding user by customer ID:', error);
    return null;
  }
};

/**
 * Update user's subscription information
 */
export const updateSubscriptionInDatabase = async (subscriptionData: any) => {
  try {
    // If we have a subscription ID, use it to find the document
    if (subscriptionData.subscriptionId) {
      const subscriptionRef = doc(db, 'subscriptions', subscriptionData.subscriptionId);
      const subscriptionDoc = await getDoc(subscriptionRef);
      
      if (subscriptionDoc.exists()) {
        // Update existing subscription
        await updateDoc(subscriptionRef, {
          ...subscriptionData,
          updatedAt: serverTimestamp()
        });
        
        // Find and update associated user
        const userId = subscriptionDoc.data().userId;
        if (userId) {
          const userRef = doc(db, 'users', userId);
          await updateDoc(userRef, {
            subscriptionId: subscriptionData.subscriptionId,
            subscriptionTier: subscriptionData.tier || getTierFromPriceId(subscriptionData.priceId),
            subscriptionStatus: subscriptionData.status,
            subscriptionUpdatedAt: new Date()
          });
        }
        
        return true;
      }
    }
    
    // If we have a customer ID but no subscription ID
    if (subscriptionData.customerId && !subscriptionData.subscriptionId) {
      const userId = await findUserByCustomerId(subscriptionData.customerId);
      if (userId) {
        // Create new subscription document
        const subscriptionRef = doc(collection(db, 'subscriptions'));
        await setDoc(subscriptionRef, {
          ...subscriptionData,
          userId,
          subscriptionId: subscriptionRef.id,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        // Update user document
        const userRef = doc(db, 'users', userId);
        await updateDoc(userRef, {
          subscriptionId: subscriptionRef.id,
          subscriptionTier: subscriptionData.tier || getTierFromPriceId(subscriptionData.priceId),
          subscriptionStatus: subscriptionData.status,
          subscriptionUpdatedAt: new Date()
        });
        
        return true;
      }
    }
    
    // If we have userId and are creating a new subscription
    if (subscriptionData.userId) {
      const userRef = doc(db, 'users', subscriptionData.userId);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists()) {
        // Create new subscription with provided ID or auto-generated
        let subscriptionId = subscriptionData.subscriptionId;
        let subscriptionRef;
        
        if (subscriptionId) {
          subscriptionRef = doc(db, 'subscriptions', subscriptionId);
        } else {
          subscriptionRef = doc(collection(db, 'subscriptions'));
          subscriptionId = subscriptionRef.id;
        }
        
        await setDoc(subscriptionRef, {
          ...subscriptionData,
          subscriptionId: subscriptionId,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        // Update user with subscription info
        await updateDoc(userRef, {
          subscriptionId: subscriptionId,
          subscriptionTier: subscriptionData.tier || getTierFromPriceId(subscriptionData.priceId),
          subscriptionStatus: subscriptionData.status,
          subscriptionUpdatedAt: new Date()
        });
        
        return true;
      } else {
        console.error(`User with ID ${subscriptionData.userId} not found`);
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error updating subscription in database:', error);
    throw error;
  }
};

/**
 * Log a transaction in the database
 */
export const logTransactionInDatabase = async (transactionData: any) => {
  try {
    const transactionRef = doc(collection(db, 'transactions'));
    await setDoc(transactionRef, {
      ...transactionData,
      createdAt: new Date()
    });
    
    return transactionRef.id;
  } catch (error) {
    console.error('Error logging transaction in database:', error);
    throw error;
  }
};
