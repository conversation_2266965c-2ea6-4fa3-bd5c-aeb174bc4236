import { db } from '@/utils/firebase';
import { collection, query, where, getDocs, addDoc, deleteDoc, doc } from 'firebase/firestore';
import { RevenueTarget } from './types/models';

interface CreateRevenueTargetInput {
  spaceId: string;
  type: 'daily' | 'monthly' | 'annual';
  amount: number;
  startDate: string;
  isRecurring: boolean;
}

class RevenueTargetValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'RevenueTargetValidationError';
  }
}

export const revenueTargetService = {
  /**
   * Validates a revenue target before creation
   */
  validateTarget(input: CreateRevenueTargetInput): void {
    // Type validation
    if (!['daily', 'monthly', 'annual'].includes(input.type)) {
      throw new RevenueTargetValidationError('Invalid target type. Must be daily, monthly, or annual.');
    }

    // Amount validation
    if (typeof input.amount !== 'number' || input.amount <= 0) {
      throw new RevenueTargetValidationError('Target amount must be a positive number.');
    }

    // Start date validation
    const startDate = new Date(input.startDate);
    if (isNaN(startDate.getTime())) {
      throw new RevenueTargetValidationError('Invalid start date format.');
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);

    if (startDate < today) {
      throw new RevenueTargetValidationError('Start date cannot be in the past.');
    }
  },

  /**
   * Creates a new revenue target
   */
  async create(input: CreateRevenueTargetInput): Promise<string> {
    try {
      // Validate input
      this.validateTarget(input);

      // Check for existing active targets of the same type
      const existingTargets = await this.getBySpace(input.spaceId);
      const conflictingTarget = existingTargets.find(target => 
        target.type === input.type && 
        new Date(target.startDate) <= new Date(input.startDate) &&
        (!target.endDate || new Date(target.endDate) >= new Date(input.startDate))
      );

      if (conflictingTarget) {
        throw new RevenueTargetValidationError(
          `An active ${input.type} target already exists for this period.`
        );
      }

      // Calculate end date based on type (if not recurring)
      let endDate: string | undefined;
      if (!input.isRecurring) {
        const date = new Date(input.startDate);
        switch (input.type) {
          case 'daily':
            date.setDate(date.getDate() + 1);
            break;
          case 'monthly':
            date.setMonth(date.getMonth() + 1);
            break;
          case 'annual':
            date.setFullYear(date.getFullYear() + 1);
            break;
        }
        endDate = date.toISOString();
      }

      // Create the target
      const target: Omit<RevenueTarget, 'id'> = {
        ...input,
        endDate,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const docRef = await addDoc(collection(db, 'revenueTargets'), target);
      return docRef.id;
    } catch (error) {
      if (error instanceof RevenueTargetValidationError) {
        throw error;
      }
      console.error('Error creating revenue target:', error);
      throw new Error('Could not create revenue target. Please try again.');
    }
  },

  /**
   * Gets all revenue targets for a space
   */
  async getBySpace(spaceId: string): Promise<RevenueTarget[]> {
    try {
      const q = query(
        collection(db, 'revenueTargets'),
        where('spaceId', '==', spaceId)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as RevenueTarget[];
    } catch (error) {
      console.error('Error fetching space targets:', error);
      throw new Error('Could not fetch revenue targets. Please try again.');
    }
  },

  /**
   * Gets the active daily target for a space
   */
  async getActiveDailyTarget(spaceId: string): Promise<RevenueTarget | null> {
    try {
      const today = new Date().toISOString();
      const targets = await this.getBySpace(spaceId);
      
      return targets.find(target => {
        if (target.type !== 'daily') return false;
        const startDate = new Date(target.startDate);
        const endDate = target.endDate ? new Date(target.endDate) : null;
        const todayDate = new Date(today);

        return (
          todayDate >= startDate && 
          (!endDate || todayDate < endDate)
        );
      }) || null;
    } catch (error) {
      console.error('Error fetching active daily target:', error);
      throw new Error('Could not fetch active daily target. Please try again.');
    }
  },

  /**
   * Gets the active monthly target for a space
   */
  async getActiveMonthlyTarget(spaceId: string): Promise<RevenueTarget | null> {
    try {
      const today = new Date().toISOString();
      const targets = await this.getBySpace(spaceId);
      
      return targets.find(target => {
        if (target.type !== 'monthly') return false;
        const startDate = new Date(target.startDate);
        const endDate = target.endDate ? new Date(target.endDate) : null;
        const todayDate = new Date(today);

        return (
          todayDate >= startDate && 
          (!endDate || todayDate < endDate)
        );
      }) || null;
    } catch (error) {
      console.error('Error fetching active monthly target:', error);
      throw new Error('Could not fetch active monthly target. Please try again.');
    }
  },

  /**
   * Gets the active annual target for a space
   */
  async getActiveAnnualTarget(spaceId: string): Promise<RevenueTarget | null> {
    try {
      const today = new Date().toISOString();
      const targets = await this.getBySpace(spaceId);
      
      return targets.find(target => {
        if (target.type !== 'annual') return false;
        const startDate = new Date(target.startDate);
        const endDate = target.endDate ? new Date(target.endDate) : null;
        const todayDate = new Date(today);

        return (
          todayDate >= startDate && 
          (!endDate || todayDate < endDate)
        );
      }) || null;
    } catch (error) {
      console.error('Error fetching active annual target:', error);
      throw new Error('Could not fetch active annual target. Please try again.');
    }
  },

  /**
   * Deletes a revenue target
   */
  async delete(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'revenueTargets', id));
    } catch (error) {
      console.error('Error deleting revenue target:', error);
      throw new Error('Could not delete revenue target. Please try again.');
    }
  }
};
