import { Staff, Service } from './types/models'
import { BusinessHour } from './publicAppointmentService'
import { businessHoursService } from './businessHoursService'

interface AppointmentTime {
  startTime: Date
  endTime: Date
}

interface StaffAvailability {
  staffId: string
  appointments: AppointmentTime[]
}

export const appointmentValidationService = {
  /**
   * Check if the appointment time is valid (not in past, at least 2 days in advance)
   */
  isValidAppointmentTime(startTime: Date): boolean {
    const now = new Date()
    const twoDaysFromNow = new Date(now.setDate(now.getDate() + 2))

    // Reset hours to start of day for comparison
    twoDaysFromNow.setHours(0, 0, 0, 0)
    const appointmentDate = new Date(startTime)
    appointmentDate.setHours(0, 0, 0, 0)

    return appointmentDate >= twoDaysFromNow
  },

  /**
   * Check if staff is available for the given time slot
   */
  isStaffAvailable(
    startTime: Date,
    duration: number,
    staffAvailability: StaffAvailability
  ): boolean {
    const endTime = new Date(startTime.getTime() + duration * 60000) // Convert duration to milliseconds

    // Check if the time slot overlaps with any existing appointments
    return !staffAvailability.appointments.some(appointment => {
      const appointmentStart = new Date(appointment.startTime)
      const appointmentEnd = new Date(appointment.endTime)

      // Check for overlap
      return (
        (startTime >= appointmentStart && startTime < appointmentEnd) ||
        (endTime > appointmentStart && endTime <= appointmentEnd) ||
        (startTime <= appointmentStart && endTime >= appointmentEnd)
      )
    })
  },

  /**
   * Check if staff can perform the requested service
   */
  canStaffPerformService(staff: Staff, serviceId: string): boolean {
    return staff.services.includes(serviceId)
  },

  /**
   * Check if the appointment time is within business hours
   */
  isWithinBusinessHours(startTime: Date, duration: number, businessHours: BusinessHour[]): boolean {
    // Check if the salon is open at the start time
    if (!businessHoursService.isWithinBusinessHours(startTime, businessHours)) {
      return false;
    }

    // Check if the salon will still be open at the end time
    const endTime = new Date(startTime.getTime() + duration * 60000);
    return businessHoursService.isWithinBusinessHours(endTime, businessHours);
  },

  /**
   * Validate the entire appointment request
   */
  async validateAppointment(
    startTime: Date,
    serviceId: string,
    staffId: string,
    staff: Staff,
    staffAvailability: StaffAvailability,
    service: Service,
    businessHours?: BusinessHour[]
  ): Promise<{ valid: boolean; error?: string }> {
    // Check if appointment time is valid
    if (!this.isValidAppointmentTime(startTime)) {
      return {
        valid: false,
        error: 'Appointments must be scheduled at least 2 days in advance'
      }
    }

    // Check if staff can perform the service
    if (!this.canStaffPerformService(staff, serviceId)) {
      return {
        valid: false,
        error: 'Selected staff member cannot perform this service'
      }
    }

    // Check staff availability
    if (!this.isStaffAvailable(startTime, service.duration, staffAvailability)) {
      return {
        valid: false,
        error: 'Staff is not available at the selected time'
      }
    }

    // Check business hours if provided
    if (businessHours && !this.isWithinBusinessHours(startTime, service.duration, businessHours)) {
      return {
        valid: false,
        error: 'The appointment time is outside of business hours'
      }
    }

    return { valid: true }
  }
}