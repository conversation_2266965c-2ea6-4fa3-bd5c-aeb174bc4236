import { db } from '@/utils/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { Staff } from './types/models';

/**
 * Staff authentication service for managing staff portal access
 */
export const staffAuthService = {
  /**
   * Verify a staff PIN
   */
  async verifyPin(pin: string): Promise<Staff | null> {
    try {
      // Search for staff with matching PIN
      const staffRef = doc(db, 'staff', pin);
      const staffDoc = await getDoc(staffRef);
      
      if (!staffDoc.exists()) return null;
      
      const staff = { id: staffDoc.id, ...staffDoc.data() } as Staff;
      if (!staff.pin?.value || staff.pin.value !== pin) return null;
      
      return staff;
    } catch (error) {
      console.error('Error verifying PIN:', error);
      return null;
    }
  },

  /**
   * Update staff PIN
   */
  async updatePin(staffId: string, newPin: string): Promise<boolean> {
    try {
      const staffRef = doc(db, 'staff', staffId);
      await updateDoc(staffRef, {
        pin: {
          value: newPin,
          hasChangedDefault: true,
          lastUpdated: new Date().toISOString()
        }
      });
      return true;
    } catch (error) {
      console.error('Error updating PIN:', error);
      return false;
    }
  },

  /**
   * Check if a PIN is the default/needs to be changed
   */
  async shouldResetPin(pin: string): Promise<boolean> {
    const staff = await this.verifyPin(pin);
    if (!staff) return false;
    return !staff.pin?.hasChangedDefault;
  },

  /**
   * Generate a new random PIN
   */
  generateNewPin(): string {
    // Generate a 4-digit PIN
    return Math.floor(1000 + Math.random() * 9000).toString();
  },

  /**
   * Generate a secure URL for staff access using space ID instead of PIN
   */
  getPortalUrl(spaceId: string): string {
    return `/staff/portal/${spaceId}`;
  }
};
