import { db } from '@/utils/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { Staff } from './types/models';

/**
 * Staff authentication service for managing staff portal access
 */
export const staffAuthService = {
  /**
   * Verify a staff PIN (legacy method - use staffPortalService.verifyPin instead)
   */
  async verifyPin(pin: string): Promise<Staff | null> {
    try {
      // Use the new PIN verification system
      const result = await import('./staffPortalService').then(module =>
        module.staffPortalService.verifyPin(pin)
      );

      if (!result.isValid || !result.staffId) return null;

      // Get the staff data
      const staffRef = doc(db, 'staff', result.staffId);
      const staffDoc = await getDoc(staffRef);

      if (!staffDoc.exists()) return null;

      return { id: staffDoc.id, ...staffDoc.data() } as Staff;
    } catch (error) {
      console.error('Error verifying PIN:', error);
      return null;
    }
  },

  /**
   * Update staff PIN (legacy method - use staffPortalService.updatePin instead)
   */
  async updatePin(staffId: string, newPin: string): Promise<boolean> {
    try {
      // Update both the staff document (for backward compatibility) and the staff-pins collection
      const staffRef = doc(db, 'staff', staffId);
      await updateDoc(staffRef, {
        pin: {
          value: newPin,
          hasChangedDefault: true,
          lastUpdated: new Date().toISOString()
        }
      });

      // Also update the staff-pins collection
      const { collection, query, where, getDocs, updateDoc: updateFirestoreDoc, Timestamp } = await import('firebase/firestore');
      const pinsRef = collection(db, 'staff-pins');
      const q = query(pinsRef, where('staffId', '==', staffId));
      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const pinDoc = snapshot.docs[0];
        await updateFirestoreDoc(doc(db, 'staff-pins', pinDoc.id), {
          pin: newPin,
          updatedAt: Timestamp.now(),
          isFirstLogin: false
        });
      }

      return true;
    } catch (error) {
      console.error('Error updating PIN:', error);
      return false;
    }
  },

  /**
   * Check if a PIN is the default/needs to be changed
   */
  async shouldResetPin(pin: string): Promise<boolean> {
    const staff = await this.verifyPin(pin);
    if (!staff) return false;
    return !staff.pin?.hasChangedDefault;
  },

  /**
   * Generate a new random PIN
   */
  generateNewPin(): string {
    // Generate a 4-digit PIN
    return Math.floor(1000 + Math.random() * 9000).toString();
  },

  /**
   * Generate a secure URL for staff access using space ID instead of PIN
   */
  getPortalUrl(spaceId: string): string {
    return `/staff/portal/${spaceId}`;
  }
};
