import { format, parse, isWithinInterval } from 'date-fns';
import { BusinessHour } from './publicAppointmentService';

export const businessHoursService = {
  /**
   * Check if a given time is within business hours
   */
  isWithinBusinessHours(
    date: Date,
    businessHours: BusinessHour[]
  ): boolean {
    const dayOfWeek = format(date, 'EEEE'); // Monday, Tuesday, etc.
    const daySettings = businessHours.find(day => day.day === dayOfWeek);
    
    if (!daySettings || !daySettings.enabled) {
      return false; // Salon is closed on this day
    }
    
    // Parse the open and close times for the day
    const dateString = format(date, 'yyyy-MM-dd');
    const openTime = parse(`${dateString} ${daySettings.open}`, 'yyyy-MM-dd HH:mm', new Date());
    const closeTime = parse(`${dateString} ${daySettings.close}`, 'yyyy-MM-dd HH:mm', new Date());
    
    // Check if the time is within business hours
    return isWithinInterval(date, { start: openTime, end: closeTime });
  },
  
  /**
   * Get the default business hours for a new space
   */
  getDefaultBusinessHours(): BusinessHour[] {
    return [
      { day: 'Monday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Tuesday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Wednesday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Thursday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Friday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Saturday', open: '09:00', close: '18:00', enabled: true },
      { day: 'Sunday', open: '09:00', close: '18:00', enabled: false },
    ];
  },
  
  /**
   * Format business hours for display
   */
  formatBusinessHoursForDisplay(businessHours: BusinessHour[]): string[] {
    return businessHours.map(day => {
      if (!day.enabled) {
        return `${day.day}: Closed`;
      }
      
      // Parse times to format them nicely
      const dateString = format(new Date(), 'yyyy-MM-dd');
      const openTime = parse(`${dateString} ${day.open}`, 'yyyy-MM-dd HH:mm', new Date());
      const closeTime = parse(`${dateString} ${day.close}`, 'yyyy-MM-dd HH:mm', new Date());
      
      return `${day.day}: ${format(openTime, 'h:mm a')} - ${format(closeTime, 'h:mm a')}`;
    });
  }
};
