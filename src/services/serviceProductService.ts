import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  Timestamp,
} from 'firebase/firestore';
import { db } from '@/utils/firebase';
import { ServiceProductUsage } from './types/models';

export interface InventoryItem {
  id: string;
  name: string;
  type: string;
  quantity: number;
  costPerUnit: number;
  thresholdLow: number;
  thresholdCritical: number;
  status?: 'ok' | 'low' | 'critical';
  lastUpdated: string;
  spaceId: string;
}

export interface ProductUsageInput {
  productId: string;
  quantityUsed: number;
  notes?: string;
}

export const serviceProductService = {
  /**
   * Record product usage for a service and deduct from inventory
   */
  async recordProductUsage(
    serviceId: string,
    productUsages: ProductUsageInput[],
    recordedBy: string
  ): Promise<ServiceProductUsage[]> {
    try {
      console.log('Recording product usage for service:', serviceId);

      const usageRecords: ServiceProductUsage[] = [];

      for (const usage of productUsages) {
        // Get inventory item details
        const inventoryRef = doc(db, 'inventory', usage.productId);
        const inventoryDoc = await getDoc(inventoryRef);

        if (!inventoryDoc.exists()) {
          throw new Error(`Inventory item ${usage.productId} not found`);
        }

        const inventoryItem = { id: inventoryDoc.id, ...inventoryDoc.data() } as InventoryItem;

        // Check if enough quantity is available
        if (inventoryItem.quantity < usage.quantityUsed) {
          throw new Error(
            `Insufficient quantity for ${inventoryItem.name}. Available: ${inventoryItem.quantity}, Required: ${usage.quantityUsed}`
          );
        }

        // Calculate total cost
        const totalCost = usage.quantityUsed * inventoryItem.costPerUnit;

        // Create usage record
        const usageRecord: Omit<ServiceProductUsage, 'id'> = {
          serviceId,
          productId: usage.productId,
          productName: inventoryItem.name,
          quantityUsed: usage.quantityUsed,
          costPerUnit: inventoryItem.costPerUnit,
          totalCost,
          usedAt: new Date().toISOString(),
          spaceId: inventoryItem.spaceId,
          recordedBy,
        };

        // Save usage record
        const usageRef = await addDoc(collection(db, 'service-product-usage'), usageRecord);

        // Update inventory quantity
        const newQuantity = inventoryItem.quantity - usage.quantityUsed;
        const status = this.getInventoryStatus(newQuantity, inventoryItem.thresholdLow, inventoryItem.thresholdCritical);

        await updateDoc(inventoryRef, {
          quantity: newQuantity,
          status,
          lastUpdated: new Date().toISOString(),
        });

        usageRecords.push({
          ...usageRecord,
          id: usageRef.id,
        });

        console.log(`Product usage recorded: ${usage.quantityUsed} x ${inventoryItem.name} = ${totalCost}`);
      }

      return usageRecords;
    } catch (error) {
      console.error('Error recording product usage:', error);
      throw error;
    }
  },

  /**
   * Get product usage for a service
   */
  async getServiceProductUsage(serviceId: string): Promise<ServiceProductUsage[]> {
    try {
      const q = query(
        collection(db, 'service-product-usage'),
        where('serviceId', '==', serviceId),
        orderBy('usedAt', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ServiceProductUsage[];
    } catch (error) {
      console.error('Error fetching service product usage:', error);
      throw error;
    }
  },

  /**
   * Get product usage for a space within a date range
   */
  async getSpaceProductUsage(
    spaceId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ServiceProductUsage[]> {
    try {
      let q = query(
        collection(db, 'service-product-usage'),
        where('spaceId', '==', spaceId),
        orderBy('usedAt', 'desc')
      );

      const snapshot = await getDocs(q);
      let usageRecords = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ServiceProductUsage[];

      // Filter by date range if provided
      if (startDate || endDate) {
        usageRecords = usageRecords.filter(record => {
          const usageDate = new Date(record.usedAt);
          if (startDate && usageDate < new Date(startDate)) return false;
          if (endDate && usageDate > new Date(endDate)) return false;
          return true;
        });
      }

      return usageRecords;
    } catch (error) {
      console.error('Error fetching space product usage:', error);
      throw error;
    }
  },

  /**
   * Get product usage summary by product
   */
  async getProductUsageSummary(
    spaceId: string,
    startDate?: string,
    endDate?: string
  ): Promise<Array<{
    productId: string;
    productName: string;
    totalQuantityUsed: number;
    totalCost: number;
    usageCount: number;
  }>> {
    try {
      const usageRecords = await this.getSpaceProductUsage(spaceId, startDate, endDate);

      const summary = usageRecords.reduce((acc, record) => {
        const existing = acc.find(item => item.productId === record.productId);
        if (existing) {
          existing.totalQuantityUsed += record.quantityUsed;
          existing.totalCost += record.totalCost;
          existing.usageCount += 1;
        } else {
          acc.push({
            productId: record.productId,
            productName: record.productName,
            totalQuantityUsed: record.quantityUsed,
            totalCost: record.totalCost,
            usageCount: 1,
          });
        }
        return acc;
      }, [] as Array<{
        productId: string;
        productName: string;
        totalQuantityUsed: number;
        totalCost: number;
        usageCount: number;
      }>);

      return summary.sort((a, b) => b.totalCost - a.totalCost);
    } catch (error) {
      console.error('Error getting product usage summary:', error);
      throw error;
    }
  },

  /**
   * Get inventory items for a space (for product selection)
   */
  async getSpaceInventory(spaceId: string): Promise<InventoryItem[]> {
    try {
      const q = query(
        collection(db, 'inventory'),
        where('spaceId', '==', spaceId),
        orderBy('name', 'asc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as InventoryItem[];
    } catch (error) {
      console.error('Error fetching space inventory:', error);
      throw error;
    }
  },

  /**
   * Calculate total product cost for a service
   */
  async getServiceProductCost(serviceId: string): Promise<number> {
    try {
      const usageRecords = await this.getServiceProductUsage(serviceId);
      return usageRecords.reduce((total, record) => total + record.totalCost, 0);
    } catch (error) {
      console.error('Error calculating service product cost:', error);
      return 0;
    }
  },

  /**
   * Helper function to determine inventory status
   */
  getInventoryStatus(quantity: number, thresholdLow: number, thresholdCritical: number): 'ok' | 'low' | 'critical' {
    if (quantity <= thresholdCritical) return 'critical';
    if (quantity <= thresholdLow) return 'low';
    return 'ok';
  },

  /**
   * Reverse product usage (for cancellations)
   */
  async reverseProductUsage(serviceId: string): Promise<void> {
    try {
      const usageRecords = await this.getServiceProductUsage(serviceId);

      for (const record of usageRecords) {
        // Get current inventory
        const inventoryRef = doc(db, 'inventory', record.productId);
        const inventoryDoc = await getDoc(inventoryRef);

        if (inventoryDoc.exists()) {
          const inventoryItem = { id: inventoryDoc.id, ...inventoryDoc.data() } as InventoryItem;
          
          // Restore quantity
          const newQuantity = inventoryItem.quantity + record.quantityUsed;
          const status = this.getInventoryStatus(newQuantity, inventoryItem.thresholdLow, inventoryItem.thresholdCritical);

          await updateDoc(inventoryRef, {
            quantity: newQuantity,
            status,
            lastUpdated: new Date().toISOString(),
          });
        }

        // Mark usage record as reversed (you might want to add a status field)
        // For now, we'll leave the record for audit purposes
      }

      console.log('Product usage reversed for service:', serviceId);
    } catch (error) {
      console.error('Error reversing product usage:', error);
      throw error;
    }
  },
};
