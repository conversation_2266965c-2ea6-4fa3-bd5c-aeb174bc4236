import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
} from 'firebase/firestore';
import { db } from '../utils/firebase';
import { Customer, Service, Staff, Invoice, Shop, Appointment, ActiveService, Review, Space } from './types/models';
import { AppointmentWithDetails } from './appointmentService';

// Generic get document by ID
export async function getDocument<T>(collectionName: string, id: string): Promise<T | null> {
  const docRef = doc(db, collectionName, id);
  const docSnap = await getDoc(docRef);
  return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as T : null;
}

// Generic create document
export async function createDocument<T>(collectionName: string, data: Omit<T, 'id'>) {
  const docRef = await addDoc(collection(db, collectionName), {
    ...data,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  });
  return docRef.id;
}

// Generic update document
export async function updateDocument<T>(collectionName: string, id: string, data: Partial<T>) {
  const docRef = doc(db, collectionName, id);
  await updateDoc(docRef, {
    ...data,
    updatedAt: Timestamp.now(),
  });
}

// Generic delete document
export async function deleteDocument(collectionName: string, id: string) {
  await deleteDoc(doc(db, collectionName, id));
}

// Customer specific operations
export const customerService = {
  create: (data: Omit<Customer, 'id'>) => createDocument<Customer>('customers', data),
  update: (id: string, data: Partial<Customer>) => updateDocument<Customer>('customers', id, data),
  delete: (id: string) => deleteDocument('customers', id),
  get: (id: string) => getDocument<Customer>('customers', id),
  getAll: async () => {
    const querySnapshot = await getDocs(collection(db, 'customers'));
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Customer);
  },
  getBySpace: async (spaceId: string) => {
    // Since customers don't have a spaceId field yet, we need to filter by appointments
    // Get all appointments for this space
    const appointmentsQuery = query(
      collection(db, 'appointments'),
      where('spaceId', '==', spaceId)
    );
    const appointmentsSnapshot = await getDocs(appointmentsQuery);

    // Extract unique customer IDs
    const customerIds = new Set<string>();
    appointmentsSnapshot.docs.forEach(doc => {
      const appointment = doc.data();
      if (appointment.customerId) {
        customerIds.add(appointment.customerId);
      }
    });

    // If no customers found, return empty array
    if (customerIds.size === 0) {
      return [];
    }

    // Get all customers with these IDs
    // Convert Set to Array and use Promise.all for proper async handling
    const customerPromises = Array.from(customerIds).map(customerId =>
      getDocument<Customer>('customers', customerId)
    );

    const customerResults = await Promise.all(customerPromises);

    // Filter out null values and return valid customers
    return customerResults.filter((customer): customer is Customer =>
      customer !== null
    );
  },
  getByPhone: async (phoneNumber: string) => {
    const q = query(
      collection(db, 'customers'),
      where('phoneNumber', '==', phoneNumber),
      limit(1)
    );
    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) return null;
    const doc = querySnapshot.docs[0];
    return { id: doc.id, ...doc.data() } as Customer;
  },
  
  // Query customers based on custom conditions
  queryCustomers: async (conditions: Array<{
    field: string;
    operator: '==' | '!=' | '>' | '>=' | '<' | '<=';
    value: any;
  }>) => {
    try {
      // Build the query with all conditions
      let customersQuery = query(collection(db, 'customers'));
      
      conditions.forEach(condition => {
        customersQuery = query(
          customersQuery, 
          where(condition.field, condition.operator, condition.value)
        );
      });
      
      const querySnapshot = await getDocs(customersQuery);
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Customer);
    } catch (error) {
      console.error('Error querying customers:', error);
      return [];
    }
  },
  
  // Process a referral when a new customer signs up
  processReferral: async (newCustomerId: string, referralCode: string) => {
    try {
      // Find the customer with the given referral code
      const referrersQuery = query(
        collection(db, 'customers'),
        where('loyaltyPoints.referralCode', '==', referralCode)
      );
      
      const querySnapshot = await getDocs(referrersQuery);
      
      if (querySnapshot.empty) {
        throw new Error('Invalid referral code');
      }
      
      const referrer = querySnapshot.docs[0];
      const referrerId = referrer.id;
      
      // Award points to the referrer
      const { loyaltyService } = await import('./loyaltyService');
      await loyaltyService.awardPointsForReferral(
        referrerId,
        newCustomerId
      );
      
      // Update metadata on the new customer to track who referred them
      await updateDoc(doc(db, 'customers', newCustomerId), {
        'metadata.referredBy': referralCode,
        'metadata.referrerId': referrerId,
        updatedAt: new Date().toISOString()
      });
      
      return {
        success: true,
        referrerId
      };
    } catch (error) {
      console.error('Error processing referral:', error);
      throw error;
    }
  }
};

// Staff specific operations
export const staffService = {
  async create(data: Omit<Staff, 'id'>): Promise<Staff> {
    // Ensure spaceId is provided
    if (!data.spaceId) {
      throw new Error('Space ID is required when creating staff');
    }

    const docRef = await addDoc(collection(db, 'staff'), {
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // If the staff has a PIN, also create a document in the staff-pins collection
    if (data.pin?.value) {
      await addDoc(collection(db, 'staff-pins'), {
        pin: data.pin.value,
        staffId: docRef.id,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        isFirstLogin: !data.pin.hasChangedDefault,
        attempts: 0
      });
    }

    return {
      id: docRef.id,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  },
  async getById(id: string): Promise<Staff | null> {
    return getDocument<Staff>('staff', id);
  },
  async getAll(): Promise<Staff[]> {
    const querySnapshot = await getDocs(collection(db, 'staff'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Staff[];
  },
  async getBySpace(spaceId: string): Promise<Staff[]> {
    const q = query(collection(db, 'staff'), where('spaceId', '==', spaceId));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Staff[];
  },
  async update(id: string, data: Partial<Staff>): Promise<void> {
    return updateDocument<Staff>('staff', id, data);
  },
  async delete(id: string): Promise<void> {
    // Delete the staff document
    const docRef = doc(db, 'staff', id);
    await deleteDoc(docRef);

    // Also delete the corresponding PIN document if it exists
    const pinsRef = collection(db, 'staff-pins');
    const q = query(pinsRef, where('staffId', '==', id));
    const snapshot = await getDocs(q);

    if (!snapshot.empty) {
      const pinDoc = snapshot.docs[0];
      await deleteDoc(doc(db, 'staff-pins', pinDoc.id));
    }
  },
};

// Service specific operations
export const serviceService = {
  async create(data: Omit<Service, 'id'>): Promise<Service> {
    // Ensure spaceId is provided
    if (!data.spaceId) {
      throw new Error('Space ID is required when creating service');
    }

    const docRef = await addDoc(collection(db, 'services'), {
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    return {
      id: docRef.id,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  },
  async getById(id: string): Promise<Service | null> {
    return getDocument<Service>('services', id);
  },
  async getAll(): Promise<Service[]> {
    const querySnapshot = await getDocs(collection(db, 'services'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Service[];
  },
  async getBySpace(spaceId: string): Promise<Service[]> {
    const q = query(collection(db, 'services'), where('spaceId', '==', spaceId));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Service[];
  },
  async update(id: string, data: Partial<Service>): Promise<void> {
    return updateDocument<Service>('services', id, data);
  },
  async delete(id: string): Promise<void> {
    // First, find any active services that reference this service
    const q = query(
      collection(db, 'active-services'),
      where('status', 'in', ['in_progress', 'awaiting_payment'])
    );
    const querySnapshot = await getDocs(q);

    // Update any active services that contain this service to 'cancelled' status
    const batch = [];
    for (const docSnap of querySnapshot.docs) {
      const activeService = docSnap.data() as ActiveService;
      const serviceIds = activeService.services.map(s => s.id);

      // Check if this active service contains the service being deleted
      if (serviceIds.includes(id)) {
        batch.push(
          updateDoc(doc(db, 'active-services', docSnap.id), {
            status: 'cancelled',
            endTime: Timestamp.now()
          })
        );
      }
    }

    // Execute all updates in parallel
    if (batch.length > 0) {
      await Promise.all(batch);
    }

    // Now delete the service
    const docRef = doc(db, 'services', id);
    await deleteDoc(docRef);
  },
};

// Invoice specific operations
export const invoiceService = {
  create: (data: Omit<Invoice, 'id'>) => createDocument<Invoice>('invoices', data),
  update: (id: string, data: Partial<Invoice>) => updateDocument<Invoice>('invoices', id, data),
  get: (id: string) => getDocument<Invoice>('invoices', id),
  getAll: async () => {
    const querySnapshot = await getDocs(
      query(collection(db, 'invoices'), orderBy('createdAt', 'desc'))
    );
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Invoice);
  },
  getCustomerInvoices: async (customerId: string) => {
    const q = query(
      collection(db, 'invoices'),
      where('customerId', '==', customerId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Invoice);
  },
  getStaffInvoices: async (staffId: string) => {
    const q = query(
      collection(db, 'invoices'),
      where('staffId', '==', staffId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Invoice);
  },
};

// Shop specific operations
export const shopService = {
  get: async () => {
    const querySnapshot = await getDocs(collection(db, 'shops'));
    return querySnapshot.empty ? null : { id: querySnapshot.docs[0].id, ...querySnapshot.docs[0].data() } as Shop;
  },
  update: (id: string, data: Partial<Shop>) => updateDocument<Shop>('shops', id, data),
};


export const appointmentService = {
  async create(data: Omit<Appointment, 'id'>): Promise<Appointment> {
    const docRef = await addDoc(collection(db, 'appointments'), {
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    return {
      id: docRef.id,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  },

  async getById(id: string): Promise<Appointment | null> {
    return getDocument<Appointment>('appointments', id);
  },

  async getBySpace(spaceId: string): Promise<Appointment[]> {
    const q = query(collection(db, 'appointments'), where('spaceId', '==', spaceId));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Appointment[];
  },

  async getStaffAppointments(staffId: string, date: Date): Promise<Appointment[]> {
    try {
      // Get start and end of the day
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      console.log(`Fetching appointments for staff ${staffId} on ${date.toDateString()}`);

      // Use a simpler query that doesn't require a composite index
      // First, get all appointments for this staff member
      const staffQuery = query(
        collection(db, 'appointments'),
        where('staffId', '==', staffId)
      );

      const querySnapshot = await getDocs(staffQuery);
      console.log(`Found ${querySnapshot.size} total appointments for staff`);

      // Then filter by date in memory
      const filteredAppointments = querySnapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        }) as Appointment)
        .filter(appointment => {
          const appointmentStartTime = new Date(appointment.startTime);
          return appointmentStartTime >= startOfDay && appointmentStartTime <= endOfDay;
        });

      console.log(`After filtering by date: ${filteredAppointments.length} appointments`);
      return filteredAppointments;
    } catch (error) {
      console.error('Error fetching staff appointments:', error);
      // Return empty array on error to prevent the entire booking flow from breaking
      return [];
    }
  },

  async getByDateRange(spaceId: string, startDate: Date, endDate: Date): Promise<AppointmentWithDetails[]> {
    try {
      const appointmentsRef = collection(db, 'appointments');
      const q = query(
        appointmentsRef,
        where('spaceId', '==', spaceId),
        where('date', '>=', startDate),
        where('date', '<=', endDate),
        orderBy('date', 'asc')
      );

      const snapshot = await getDocs(q);
      const appointments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as AppointmentWithDetails[];

      return appointments;
    } catch (error) {
      console.error('Error getting appointments by date range:', error);
      return [];
    }
  },

  async update(id: string, data: Partial<Appointment>): Promise<void> {
    return updateDocument<Appointment>('appointments', id, data);
  },

  async delete(id: string): Promise<void> {
    const docRef = doc(db, 'appointments', id);
    await deleteDoc(docRef);
  }
};



export const activeServiceService = {
  async create(data: Omit<ActiveService, 'id'>): Promise<ActiveService> {
    // Ensure spaceId is provided
    if (!data.spaceId) {
      throw new Error('Space ID is required when creating active service');
    }

    const docRef = await addDoc(collection(db, 'active-services'), {
      ...data,
      startTime: Timestamp.fromDate(data.startTime),
      endTime: data.endTime ? Timestamp.fromDate(data.endTime) : null
    });
    return {
      id: docRef.id,
      ...data
    };
  },

  async exists(id: string): Promise<boolean> {
    try {
      console.log(`Checking if active service with ID ${id} exists`);
      const docRef = doc(db, 'active-services', id);
      const docSnap = await getDoc(docRef);
      const exists = docSnap.exists();
      console.log(`Active service with ID ${id} exists: ${exists}`);
      return exists;
    } catch (error) {
      console.error(`Error checking if active service with ID ${id} exists:`, error);
      return false;
    }
  },

  async findServiceByProperties(service: ActiveService): Promise<string | null> {
    try {
      console.log(`Attempting to find service by properties instead of ID`);

      // Query for services with matching customer and staff
      const q = query(
        collection(db, 'active-services'),
        where('customerId', '==', service.customerId),
        where('staffId', '==', service.staffId),
        where('status', '==', 'in_progress')
      );

      const querySnapshot = await getDocs(q);
      console.log(`Found ${querySnapshot.size} potential matches`);

      if (querySnapshot.empty) {
        return null;
      }

      // If we have multiple matches, try to find the best one
      if (querySnapshot.size > 1) {
        console.log(`Multiple matches found, looking for best match`);

        // Try to match by service details
        for (const docSnap of querySnapshot.docs) {
          const data = docSnap.data();

          // Check if services match
          if (data.services && data.services.length === service.services.length) {
            const serviceIds = service.services.map(s => s.id).sort();
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const dataServiceIds = data.services.map((s: any) => s.id).sort();

            // Check if service IDs match
            const servicesMatch = serviceIds.every((id, index) => id === dataServiceIds[index]);

            if (servicesMatch) {
              console.log(`Found matching service with ID: ${docSnap.id}`);
              return docSnap.id;
            }
          }
        }
      }

      // If we couldn't find an exact match or there's only one result, return the first one
      console.log(`Returning first match with ID: ${querySnapshot.docs[0].id}`);
      return querySnapshot.docs[0].id;
    } catch (error) {
      console.error(`Error finding service by properties:`, error);
      return null;
    }
  },

  async getAll(): Promise<ActiveService[]> {
    const querySnapshot = await getDocs(collection(db, 'active-services'));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      startTime: doc.data().startTime.toDate(),
      endTime: doc.data().endTime?.toDate()
    })) as ActiveService[];
  },

  async getInProgress(): Promise<ActiveService[]> {
    const q = query(
      collection(db, 'active-services'),
      where('status', 'in', ['in_progress', 'awaiting_payment'])
    );
    const querySnapshot = await getDocs(q);

    // Log all document IDs for debugging
    console.log("Active services in Firestore:",
      querySnapshot.docs.map(doc => ({ id: doc.id, status: doc.data().status }))
    );

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      startTime: doc.data().startTime.toDate(),
      endTime: doc.data().endTime?.toDate()
    })) as ActiveService[];
  },

  async getInProgressBySpace(spaceId: string): Promise<ActiveService[]> {
    const q = query(
      collection(db, 'active-services'),
      where('spaceId', '==', spaceId),
      where('status', 'in', ['in_progress', 'awaiting_payment'])
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      startTime: doc.data().startTime.toDate(),
      endTime: doc.data().endTime?.toDate()
    })) as ActiveService[];
  },

  async getCompleted(): Promise<ActiveService[]> {
    try {
      console.log("Fetching all active services from Firestore");

      // Simply get all documents from active-services collection
      const querySnapshot = await getDocs(collection(db, 'active-services'));

      console.log(`Found ${querySnapshot.size} total active services`);

      // Filter for completed, awaiting_payment, and paid services
      const completedServices = querySnapshot.docs
        .filter(doc => {
          const status = doc.data().status;
          return status === 'completed' || status === 'awaiting_payment' || status === 'paid';
        })
        .map(doc => {
          const data = doc.data();
          console.log(`Service ID: ${doc.id}, Status: ${data.status}`);

          return {
            id: doc.id,
            ...data,
            startTime: data.startTime?.toDate() || new Date(),
            endTime: data.endTime?.toDate() || new Date()
          };
        });

      console.log(`Found ${completedServices.length} completed/awaiting_payment/paid services`);
      return completedServices as ActiveService[];
    } catch (error) {
      console.error("Error fetching completed services:", error);
      return [];
    }
  },

  async getCompletedBySpace(spaceId: string): Promise<ActiveService[]> {
    try {
      const q = query(
        collection(db, 'active-services'),
        where('spaceId', '==', spaceId)
      );
      const querySnapshot = await getDocs(q);

      // Filter for completed, awaiting_payment, and paid services
      const completedServices = querySnapshot.docs
        .filter(doc => {
          const status = doc.data().status;
          return status === 'completed' || status === 'awaiting_payment' || status === 'paid';
        })
        .map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            startTime: data.startTime?.toDate() || new Date(),
            endTime: data.endTime?.toDate() || new Date()
          };
        });

      return completedServices as ActiveService[];
    } catch (error) {
      console.error("Error fetching completed services for space:", error);
      return [];
    }
  },

  async update(id: string, data: Partial<ActiveService>, retryCount = 0): Promise<void> {
    const maxRetries = 2;
    const docRef = doc(db, 'active-services', id);

    try {
      // Check if document exists first
      console.log(`Checking if active service with ID ${id} exists (attempt ${retryCount + 1})`);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        console.error(`Active service with ID ${id} does not exist in Firestore`);

        // If we have retries left, try again after a short delay
        if (retryCount < maxRetries) {
          console.log(`Retrying update for service ID ${id} (attempt ${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          return this.update(id, data, retryCount + 1);
        }

        throw new Error(`Active service with ID ${id} does not exist`);
      }

      console.log(`Found active service with ID ${id}, preparing update data`);
      const serviceData = docSnap.data();
      console.log(`Current service data:`, serviceData);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateData: Record<string, any> = {
        ...data,
        // Only convert to Timestamp if the date exists
        startTime: data.startTime ? Timestamp.fromDate(data.startTime) : undefined,
        endTime: data.endTime ? Timestamp.fromDate(data.endTime) : undefined
      };

      // Remove undefined fields to prevent Firestore errors
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      console.log(`Updating active service with ID ${id} with data:`, updateData);
      await updateDoc(docRef, updateData);
      console.log(`Successfully updated active service with ID ${id}`);
    } catch (error) {
      console.error(`Error updating active service with ID ${id}:`, error);

      // If we have retries left and it's not a "document doesn't exist" error, try again
      if (retryCount < maxRetries &&
        !(error instanceof Error && error.message.includes('does not exist'))) {
        console.log(`Retrying update for service ID ${id} (attempt ${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        return this.update(id, data, retryCount + 1);
      }

      throw error;
    }
  },

  async delete(id: string): Promise<void> {
    const docRef = doc(db, 'active-services', id);
    await deleteDoc(docRef);
  }
};

export const reviewService = {
  async create(data: Omit<Review, 'id'>): Promise<Review> {
    const docRef = await addDoc(collection(db, 'reviews'), {
      ...data,
      status: 'published',
      createdAt: new Date().toISOString()
    });

    // Update staff member's average rating
    const reviews = await this.getStaffReviews(data.staffId);
    const avgRating = reviews.reduce((acc, rev) => acc + rev.rating, 0) / reviews.length;
    await staffService.update(data.staffId, { rating: avgRating });

    return {
      id: docRef.id,
      ...data,
      status: 'published',
      createdAt: new Date().toISOString()
    };
  },

  async getById(id: string): Promise<Review | null> {
    return getDocument<Review>('reviews', id);
  },

  async getByActiveService(activeServiceId: string): Promise<Review | null> {
    const q = query(
      collection(db, 'reviews'),
      where('activeServiceId', '==', activeServiceId),
      limit(1)
    );
    const snapshot = await getDocs(q);
    return snapshot.empty ? null : { id: snapshot.docs[0].id, ...snapshot.docs[0].data() } as Review;
  },

  async getStaffReviews(staffId: string): Promise<Review[]> {
    const q = query(
      collection(db, 'reviews'),
      where('staffId', '==', staffId),
      where('status', '==', 'published'),
      orderBy('createdAt', 'desc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Review));
  },

  async getSpaceReviews(spaceId: string): Promise<Review[]> {
    const q = query(
      collection(db, 'reviews'),
      where('spaceId', '==', spaceId),
      where('status', '==', 'published'),
      orderBy('createdAt', 'desc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Review));
  },

  async addStaffResponse(reviewId: string, comment: string): Promise<void> {
    return updateDocument<Review>('reviews', reviewId, {
      staffResponse: {
        comment,
        createdAt: new Date().toISOString()
      }
    });
  },

  async updateStatus(reviewId: string, status: Review['status']): Promise<void> {
    return updateDocument<Review>('reviews', reviewId, { status });
  }
};

// Add the contact service to the existing firestore.ts file

// First, let's define the Contact interface
export interface Contact {
  id?: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  userAgent?: string;
  referrer?: string;
  createdAt: string;
  updatedAt?: string;
}

// Then add the contactService to the exported services
export const contactService = {
  async create(data: Omit<Contact, 'id'>): Promise<Contact> {
    const docRef = await addDoc(collection(db, 'contacts'), {
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    
    return {
      id: docRef.id,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  },

  async getAll(): Promise<Contact[]> {
    const contactsRef = collection(db, 'contacts');
    const q = query(contactsRef, orderBy('createdAt', 'desc'));
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Contact[];
  },

  async getById(id: string): Promise<Contact | null> {
    const docRef = doc(db, 'contacts', id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as Contact;
    }
    
    return null;
  },

  async update(id: string, data: Partial<Contact>): Promise<void> {
    const docRef = doc(db, 'contacts', id);
    await updateDoc(docRef, {
      ...data,
      updatedAt: new Date().toISOString()
    });
  },

  async delete(id: string): Promise<void> {
    const docRef = doc(db, 'contacts', id);
    await deleteDoc(docRef);
  }
};

// New space service to replace space service
export const spaceService = {
  async getById(id: string): Promise<Space | null> {
    // Try to get from 'spaces' collection first
    let space = await getDocument<Space>('spaces', id);
    
    return space;
  },

  async getAllForUser(userId: string): Promise<Space[]> {
    // First try to get from spaces collection
    const spacesQuery = query(collection(db, 'spaces'), where('ownerId', '==', userId));
    const spacesSnapshot = await getDocs(spacesQuery);
    const spaces = spacesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Space[];
  
    
    return spaces;
  },

  async update(id: string, data: Partial<Space>): Promise<Space> {
    // Check if document exists in spaces collection
    const spaceDocRef = doc(db, 'spaces', id);
    const spaceDoc = await getDoc(spaceDocRef);
    
    // If it exists in spaces collection, update it there
    if (spaceDoc.exists()) {
      await updateDoc(spaceDocRef, {
        ...data,
        updatedAt: new Date().toISOString()
      });
    } else {
      // return error
      throw new Error(`Space with ID ${id} not found in spaces collection`);
    }
    
    // Return the updated space
    return {
      id,
      ...data,
      updatedAt: new Date().toISOString()
    } as Space;
  },

  async create(data: Omit<Space, 'id'>): Promise<Space> {
    // Always create in spaces collection for new documents
    const docRef = await addDoc(collection(db, 'spaces'), {
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    
    return {
      id: docRef.id,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  },

  async delete(id: string): Promise<void> {
    // Try to delete from spaces collection
    const spaceDocRef = doc(db, 'spaces', id);
    const spaceDoc = await getDoc(spaceDocRef);
    
    if (spaceDoc.exists()) {
      await deleteDoc(spaceDocRef);
    } 
  }
};