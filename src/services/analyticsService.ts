import { collection, query, where, getDocs, orderBy, limit, Timestamp } from 'firebase/firestore';
import { db } from '@/utils/firebase';

/**
 * Service for querying and analyzing onboarding and space creation analytics
 */
export const analyticsService = {
  /**
   * Get onboarding analytics for specific user
   * @param userId User ID to query
   * @returns Array of onboarding analytics records
   */
  async getOnboardingAnalyticsByUser(userId: string) {
    try {
      const q = query(
        collection(db, 'onboarding-analytics'),
        where('userId', '==', userId),
        orderBy('completedAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching onboarding analytics:', error);
      throw error;
    }
  },
  
  /**
   * Get recent onboarding analytics
   * @param limitCount Number of records to return
   * @returns Array of recent onboarding analytics
   */
  async getRecentOnboardingAnalytics(limitCount = 50) {
    try {
      const q = query(
        collection(db, 'onboarding-analytics'),
        orderBy('completedAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching recent onboarding analytics:', error);
      throw error;
    }
  },
  
  /**
   * Get spaces created during onboarding
   * @param limitCount Number of records to return
   * @returns Array of space creation analytics
   */
  async getSpacesCreatedDuringOnboarding(limitCount = 50) {
    try {
      const q = query(
        collection(db, 'space-analytics'),
        where('createdDuringOnboarding', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching spaces created during onboarding:', error);
      throw error;
    }
  },
  
  /**
   * Get onboarding analytics by business type
   * @param businessType Type of business to query
   * @returns Array of onboarding analytics for the specified business type
   */
  async getOnboardingAnalyticsByBusinessType(businessType: string) {
    try {
      const q = query(
        collection(db, 'onboarding-analytics'),
        where('businessType', '==', businessType),
        orderBy('completedAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching onboarding analytics by business type:', error);
      throw error;
    }
  },
  
  /**
   * Get onboarding analytics by referral source
   * @param referralSource Referral source to query
   * @returns Array of onboarding analytics for the specified referral source
   */
  async getOnboardingAnalyticsByReferralSource(referralSource: string) {
    try {
      const q = query(
        collection(db, 'onboarding-analytics'),
        where('referralSource', '==', referralSource),
        orderBy('completedAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching onboarding analytics by referral source:', error);
      throw error;
    }
  },
  
  /**
   * Get onboarding analytics within a date range
   * @param startDate Start date for the range
   * @param endDate End date for the range
   * @returns Array of onboarding analytics within the specified date range
   */
  async getOnboardingAnalyticsInDateRange(startDate: Date, endDate: Date) {
    try {
      const startTimestamp = startDate.toISOString();
      const endTimestamp = endDate.toISOString();
      
      const q = query(
        collection(db, 'onboarding-analytics'),
        where('completedAt', '>=', startTimestamp),
        where('completedAt', '<=', endTimestamp),
        orderBy('completedAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching onboarding analytics in date range:', error);
      throw error;
    }
  }
};
