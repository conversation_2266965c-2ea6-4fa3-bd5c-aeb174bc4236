// A placeholder service for subscription management with Paddle

export const SubscriptionTier = {
  FREE: 'free',
  BASIC: 'basic',
  PRO: 'pro',
  ENTERPRISE: 'enterprise'
} as const;

export type SubscriptionTier = typeof SubscriptionTier[keyof typeof SubscriptionTier];

interface SubscriptionPlan {
  id: string;
  name: string;
  tier: SubscriptionTier;
  price: number;
  interval: 'month' | 'year';
  features: string[];
}

import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { pricingPlans } from '@/lib/billing';

export const getSubscriptionStatus = async (userId: string) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      return {
        tier: SubscriptionTier.FREE,
        status: 'active',
        trialEnd: null,
        features: pricingPlans.free.features,
        limits: {
          customers: 400,
          appointmentsPerMonth: 30,
          spaces: 1
        }
      };
    }
    
    const userData = userDoc.data();
    const subscriptionTier = userData.subscriptionTier || SubscriptionTier.FREE;
    
    // Get plan limits based on tier
    let limits = { customers: 400, appointmentsPerMonth: 30, spaces: 1 };
    let features = pricingPlans.free.features;
    
    if (subscriptionTier === SubscriptionTier.BASIC) {
      limits = { customers: 1200, appointmentsPerMonth: -1, spaces: 2 }; // -1 means unlimited
      features = pricingPlans.basic.features;
    } else if (subscriptionTier === SubscriptionTier.PRO) {
      limits = { customers: -1, appointmentsPerMonth: -1, spaces: 5 };
      features = pricingPlans.pro.features;
    } else if (subscriptionTier === SubscriptionTier.ENTERPRISE) {
      limits = { customers: -1, appointmentsPerMonth: -1, spaces: -1 };
      features = pricingPlans.enterprise.features;
    }
    
    return {
      tier: subscriptionTier,
      status: userData.subscriptionStatus || 'active',
      trialEnd: userData.trialEnd?.toDate() || null,
      features,
      limits
    };
  } catch (error) {
    console.error('Error getting subscription status:', error);
    return {
      tier: SubscriptionTier.FREE,
      status: 'active',
      trialEnd: null,
      features: pricingPlans.free.features,
      limits: { customers: 400, appointmentsPerMonth: 30, spaces: 1 }
    };
  }
};

// This will be replaced with actual Paddle checkout
export const startSubscription = async (planId: string) => {
  throw new Error('Paddle integration not yet implemented');
};

// This will be replaced with actual Paddle portal
export const manageSubscription = async () => {
  throw new Error('Paddle integration not yet implemented');
};

// Feature gate logic based on subscription tier
export const hasFeatureAccess = async (userId: string, feature: string): Promise<boolean> => {
  try {
    const status = await getSubscriptionStatus(userId);
    
    // Define feature access by tier
    const featureMap: Record<string, string[]> = {
      'basic_appointments': ['free', 'basic', 'pro', 'enterprise'],
      'basic_customer_management': ['free', 'basic', 'pro', 'enterprise'],
      'basic_email_notifications': ['free', 'basic', 'pro', 'enterprise'],
      'advanced_sms_notifications': ['pro', 'enterprise'],
      'advanced_analytics': ['pro', 'enterprise'],
      'advanced_marketing': ['pro', 'enterprise'],
      'advanced_loyalty_program': ['pro', 'enterprise'],
      'advanced_referral_system': ['pro', 'enterprise'],
      'advanced_staff_portal': ['basic', 'pro', 'enterprise'],
      'enterprise_custom_branding': ['enterprise'],
      'enterprise_api_access': ['enterprise'],
      'enterprise_dedicated_support': ['enterprise']
    };
    
    const allowedTiers = featureMap[feature];
    if (!allowedTiers) {
      return false; // Unknown feature
    }
    
    return allowedTiers.includes(status.tier);
  } catch (error) {
    console.error('Error checking feature access:', error);
    return false;
  }
};

// Check if user has reached their limits
export const checkUsageLimits = async (userId: string, type: 'customers' | 'appointments' | 'spaces'): Promise<{ canCreate: boolean; limit: number; current: number }> => {
  try {
    const status = await getSubscriptionStatus(userId);
    const limit = status.limits[type === 'appointments' ? 'appointmentsPerMonth' : type];
    
    // If unlimited (-1), always allow
    if (limit === -1) {
      return { canCreate: true, limit: -1, current: 0 };
    }
    
    // TODO: Get actual current usage from database
    // For now, return mock data
    const current = 0;
    
    return {
      canCreate: current < limit,
      limit,
      current
    };
  } catch (error) {
    console.error('Error checking usage limits:', error);
    return { canCreate: false, limit: 0, current: 0 };
  }
};
