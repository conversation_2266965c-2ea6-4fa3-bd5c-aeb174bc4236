import { db } from '@/utils/firebase';
import { doc, getDoc, updateDoc, collection, addDoc, query, where, orderBy, getDocs, Timestamp, limit } from 'firebase/firestore';
import { Customer } from './types/models';
import { withRetry, FailedOperationsQueue } from '@/utils/retryUtils';

// Environment-aware logger
const logger = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[INFO] ${message}`, data);
    }
    // Add info capture here using sentry
    
  },
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV !== 'production') {
      console.error(`[ERROR] ${message}`, error);
    }
    // Capture errors using Sentry
  }
};

export interface LoyaltyTransaction {
  id?: string;
  customerId: string;
  points: number;
  type: 'earn' | 'redeem';
  source: 'appointment' | 'referral' | 'signup' | 'birthday' | 'reward';
  sourceId?: string; // ID of the related entity (appointment ID, etc.)
  description: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface LoyaltyReward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  type: 'discount' | 'free_service' | 'product';
  value: number; // Percentage for discount, or direct value
  expiresAt?: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

// Points earning rules
const POINTS_PER_CURRENCY = 0.1; // 10 points per $100 spent
const REFERRAL_POINTS = 100;
const SIGNUP_POINTS = 50;
const BIRTHDAY_POINTS = 200;

export const loyaltyService = {
  /**
   * Get a customer's current loyalty points balance
   */
  async getCustomerLoyaltyPoints(customerId: string): Promise<number> {
    try {
      return await withRetry(async () => {
        const customerRef = doc(db, 'customers', customerId);
        const customerDoc = await getDoc(customerRef);
        
        if (!customerDoc.exists()) {
          throw new Error('Customer not found');
        }
        
        const customerData = customerDoc.data() as Customer;
        return customerData.loyaltyPoints?.balance || 0;
      });
    } catch (error) {
      logger.error('Error getting customer loyalty points:', error);
      // Track this failure for diagnostics, but just return 0 since we can't block UI
      FailedOperationsQueue.getInstance().addFailedOperation(
        'getCustomerLoyaltyPoints',
        { customerId },
        error instanceof Error ? error : String(error)
      );
      return 0; // Return 0 as default to prevent UI errors
      throw error;
    }
  },

  /**
   * Update a customer's loyalty points
   */
  async updateCustomerLoyaltyPoints(customerId: string, points: number): Promise<boolean> {
    try {
      return await withRetry(async () => {
        const customerRef = doc(db, 'customers', customerId);
        const customerDoc = await getDoc(customerRef);
        
        if (!customerDoc.exists()) {
          throw new Error('Customer not found');
        }
        
        const customerData = customerDoc.data() as Customer;
        const currentPoints = customerData.loyaltyPoints?.balance || 0;
        const newBalance = Math.max(0, currentPoints + points); // Ensure balance doesn't go negative
        
        // Update customer loyalty points
        await updateDoc(customerRef, {
          'loyaltyPoints.balance': newBalance,
          'loyaltyPoints.updatedAt': new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
        
        return true;
      });
    } catch (error) {
      logger.error('Error updating customer loyalty points:', error);
      // Track this failure for later recovery
      FailedOperationsQueue.getInstance().addFailedOperation(
        'updateCustomerLoyaltyPoints',
        { customerId, points },
        error instanceof Error ? error : String(error)
      );
      throw error;
    }
  },

  /**
   * Award points to a customer for an appointment/service
   */
  async awardPointsForAppointment(
    customerId: string, 
    appointmentId: string, 
    totalAmount: number
  ): Promise<LoyaltyTransaction> {
    try {
      return await withRetry(async () => {
        const pointsEarned = Math.floor(totalAmount * POINTS_PER_CURRENCY);
        
        // Don't award points if the amount is too small
        if (pointsEarned <= 0) {
          logger.info(`No points awarded for appointment ${appointmentId} - amount too small (${totalAmount})`);
          throw new Error('Amount too small to award points');
        }
        
        // Validate the customer exists and is in the loyalty program
        const customerRef = doc(db, 'customers', customerId);
        const customerDoc = await getDoc(customerRef);
        
        if (!customerDoc.exists()) {
          throw new Error('Customer not found');
        }
        
        const customerData = customerDoc.data() as Customer;
        if (!customerData.loyaltyPoints) {
          // Initialize loyalty points for this customer
          await updateDoc(customerRef, {
            loyaltyPoints: {
              balance: 0,
              tier: 'bronze',
              enrolledAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          });
        }
        
        // Check if this appointment has already been awarded points
        const existingTransactionQuery = query(
          collection(db, 'loyalty-transactions'),
          where('sourceId', '==', appointmentId),
          where('source', '==', 'appointment')
        );
        
        const existingTransactions = await getDocs(existingTransactionQuery);
        
        if (!existingTransactions.empty) {
          logger.info(`Points already awarded for appointment ${appointmentId}`);
          throw new Error('Points already awarded for this appointment');
        }
        
        // Update customer's loyalty points
        await this.updateCustomerLoyaltyPoints(customerId, pointsEarned);
        
        // Create a transaction record
        const transaction: LoyaltyTransaction = {
          customerId,
          points: pointsEarned,
          type: 'earn',
          source: 'appointment',
          sourceId: appointmentId,
          description: `Earned ${pointsEarned} points for appointment`,
          createdAt: new Date().toISOString(),
          metadata: {
            amount: totalAmount
          }
        };
        
        // Save transaction to Firestore
        const transactionRef = await addDoc(collection(db, 'loyalty-transactions'), transaction);
        
        return {
          ...transaction,
          id: transactionRef.id
        };
      });
    } catch (error) {
      logger.error('Error awarding points for appointment:', error);
      
      // Don't log "already awarded" as an error
      if (error instanceof Error && error.message === 'Points already awarded for this appointment') {
        return {
          customerId,
          points: 0,
          type: 'earn',
          source: 'appointment',
          sourceId: appointmentId,
          description: 'Points already awarded for this appointment',
          createdAt: new Date().toISOString(),
          id: 'existing'
        };
      }
      
      // Track this failure for later recovery
      FailedOperationsQueue.getInstance().addFailedOperation(
        'awardPointsForAppointment',
        { customerId, appointmentId, totalAmount },
        error instanceof Error ? error : String(error)
      );
      throw error;
    }
  },

  /**
   * Award points for customer referral
   */
  async awardPointsForReferral(
    customerId: string, 
    referredCustomerId: string
  ): Promise<LoyaltyTransaction> {
    try {
      // Update customer's loyalty points
      await this.updateCustomerLoyaltyPoints(customerId, REFERRAL_POINTS);
      
      // Create a transaction record
      const transaction: LoyaltyTransaction = {
        customerId,
        points: REFERRAL_POINTS,
        type: 'earn',
        source: 'referral',
        sourceId: referredCustomerId,
        description: `Earned ${REFERRAL_POINTS} points for referring a friend`,
        createdAt: new Date().toISOString(),
        metadata: {
          referredCustomerId
        }
      };
      
      // Save transaction to Firestore
      const transactionRef = await addDoc(collection(db, 'loyalty-transactions'), transaction);
      
      return {
        ...transaction,
        id: transactionRef.id
      };
    } catch (error) {
      logger.error('Error awarding points for referral:', error);
      throw error;
    }
  },
  /**
   * Attempt to recover failed loyalty operations
   */
  async recoverFailedOperations(): Promise<{
    recovered: number;
    failed: number;
  }> {
    const failedOps = FailedOperationsQueue.getInstance().getFailedOperations();
    let recovered = 0;
    let stillFailed = 0;
    
    for (let i = 0; i < failedOps.length; i++) {
      const op = failedOps[i];
      
      try {
        logger.info(`Attempting to recover failed operation: ${op.operation}`, op.params);
        
        switch (op.operation) {
          case 'updateCustomerLoyaltyPoints':
            await this.updateCustomerLoyaltyPoints(op.params.customerId, op.params.points);
            break;
          case 'awardPointsForAppointment':
            await this.awardPointsForAppointment(
              op.params.customerId,
              op.params.appointmentId,
              op.params.totalAmount
            );
            break;
          case 'awardPointsForReferral':
            await this.awardPointsForReferral(
              op.params.customerId,
              op.params.referredCustomerId
            );
            break;
          case 'awardPointsForSignup':
            await this.awardPointsForSignup(op.params.customerId);
            break;
          case 'awardBirthdayPoints':
            await this.awardBirthdayPoints(op.params.customerId);
            break;
          default:
            logger.error(`Unknown operation type: ${op.operation}`);
            stillFailed++;
            continue;
        }
        
        // If we got here, the operation was successful
        FailedOperationsQueue.getInstance().removeOperation(i);
        recovered++;
        i--; // Adjust index since we removed an item
      } catch (error) {
        logger.error(`Failed to recover operation ${op.operation}:`, error);
        stillFailed++;
      }
    }
    
    return { recovered, failed: stillFailed };
  },

  /**
   * Award points for new customer signup
   */
  async awardPointsForSignup(customerId: string): Promise<LoyaltyTransaction> {
    try {
      // Update customer's loyalty points
      await this.updateCustomerLoyaltyPoints(customerId, SIGNUP_POINTS);
      
      // Create a transaction record
      const transaction: LoyaltyTransaction = {
        customerId,
        points: SIGNUP_POINTS,
        type: 'earn',
        source: 'signup',
        description: `Welcome bonus: ${SIGNUP_POINTS} points`,
        createdAt: new Date().toISOString()
      };
      
      // Save transaction to Firestore
      const transactionRef = await addDoc(collection(db, 'loyalty-transactions'), transaction);
      
      return {
        ...transaction,
        id: transactionRef.id
      };
    } catch (error) {
      logger.error('Error awarding points for signup:', error);
      throw error;
    }
  },
  /**
   * Ensure customer is enrolled in the loyalty program
   * If not, initialize their loyalty data
   */
  async ensureCustomerLoyaltyEnrollment(customerId: string): Promise<boolean> {
    try {
      const customerRef = doc(db, 'customers', customerId);
      const customerDoc = await getDoc(customerRef);
      
      if (!customerDoc.exists()) {
        throw new Error('Customer not found');
      }
      
      const customerData = customerDoc.data() as Customer;
      
      // Check if customer is already enrolled
      if (customerData.loyaltyPoints) {
        return true;
      }
      
      // Initialize loyalty points
      const referralCode = this.generateReferralCode(customerId);
      
      await updateDoc(customerRef, {
        loyaltyPoints: {
          balance: 0,
          tier: 'bronze',
          enrolledAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          referralCode
        },
        updatedAt: new Date().toISOString()
      });
      
      return true;
    } catch (error) {
      logger.error('Error ensuring customer loyalty enrollment:', error);
      throw error;
    }
  },

  /**
   * Generate a unique referral code for a customer
   */
  generateReferralCode(customerId: string): string {
    // Generate a code using the first 6 chars of the customerId and a random suffix
    const prefix = customerId.substring(0, 6);
    const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}-${randomSuffix}`;
  },

  /**
   * Award birthday points to a customer
   */
  async awardBirthdayPoints(customerId: string): Promise<LoyaltyTransaction> {
    try {
      // Update customer's loyalty points
      await this.updateCustomerLoyaltyPoints(customerId, BIRTHDAY_POINTS);
      
      // Create a transaction record
      const transaction: LoyaltyTransaction = {
        customerId,
        points: BIRTHDAY_POINTS,
        type: 'earn',
        source: 'birthday',
        description: `Happy Birthday! ${BIRTHDAY_POINTS} points`,
        createdAt: new Date().toISOString()
      };
      
      // Save transaction to Firestore
      const transactionRef = await addDoc(collection(db, 'loyalty-transactions'), transaction);
      
      return {
        ...transaction,
        id: transactionRef.id
      };
    } catch (error) {
      logger.error('Error awarding birthday points:', error);
      throw error;
    }
  },

  /**
   * Redeem points for a reward
   */
  async redeemPointsForReward(
    customerId: string, 
    rewardId: string
  ): Promise<LoyaltyTransaction> {
    try {
      // Get the reward
      const rewardRef = doc(db, 'loyalty-rewards', rewardId);
      const rewardDoc = await getDoc(rewardRef);
      
      if (!rewardDoc.exists()) {
        throw new Error('Reward not found');
      }
      
      const reward = rewardDoc.data() as LoyaltyReward;
      
      if (!reward.isActive) {
        throw new Error('Reward is not active');
      }
      
      // Check if customer has enough points
      const customerPoints = await this.getCustomerLoyaltyPoints(customerId);
      
      if (customerPoints < reward.pointsCost) {
        throw new Error('Not enough points to redeem this reward');
      }
      
      // Deduct points from customer
      await this.updateCustomerLoyaltyPoints(customerId, -reward.pointsCost);
      
      // Create a transaction record
      const transaction: LoyaltyTransaction = {
        customerId,
        points: -reward.pointsCost,
        type: 'redeem',
        source: 'reward',
        sourceId: rewardId,
        description: `Redeemed ${reward.pointsCost} points for ${reward.name}`,
        createdAt: new Date().toISOString(),
        metadata: {
          reward
        }
      };
      
      // Save transaction to Firestore
      const transactionRef = await addDoc(collection(db, 'loyalty-transactions'), transaction);
      
      return {
        ...transaction,
        id: transactionRef.id
      };
    } catch (error) {
      logger.error('Error redeeming points for reward:', error);
      throw error;
    }
  },

  /**
   * Get available rewards for a customer
   */
  async getAvailableRewards(customerId: string): Promise<{
    rewards: LoyaltyReward[];
    customerPoints: number;
  }> {
    try {
      // Get customer's points
      const customerPoints = await this.getCustomerLoyaltyPoints(customerId);
      
      // Get all active rewards
      const rewardsRef = query(
        collection(db, 'loyalty-rewards'),
        where('isActive', '==', true)
      );
      
      const rewardsSnapshot = await getDocs(rewardsRef);
      const rewards = rewardsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as LoyaltyReward[];
      
      return {
        rewards,
        customerPoints
      };
    } catch (error) {
      logger.error('Error getting available rewards:', error);
      throw error;
    }
  },

  /**
   * Get loyalty transaction history for a customer
   */
  async getCustomerTransactionHistory(customerId: string): Promise<LoyaltyTransaction[]> {
    try {
      return await withRetry(async () => {
        const transactionsRef = query(
          collection(db, 'loyalty-transactions'),
          where('customerId', '==', customerId),
          orderBy('createdAt', 'desc')
        );
        
        const transactionsSnapshot = await getDocs(transactionsRef);
        
        return transactionsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as LoyaltyTransaction[];
      });
    } catch (error) {
      logger.error('Error getting customer transaction history:', error);
      // Return empty array to avoid UI errors
      return [];
      throw error;
    }
  },

  /**
   * Get all available loyalty rewards
   */
  async getAllRewards(): Promise<LoyaltyReward[]> {
    try {
      const rewardsRef = collection(db, 'loyalty-rewards');
      const rewardsSnapshot = await getDocs(rewardsRef);
      
      return rewardsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as LoyaltyReward[];
    } catch (error) {
      logger.error('Error getting all rewards:', error);
      throw error;
    }
  },

  /**
   * Create a new loyalty reward
   */
  async createReward(reward: Omit<LoyaltyReward, 'id'>): Promise<LoyaltyReward> {
    try {
      return await withRetry(async () => {
        const rewardRef = await addDoc(collection(db, 'loyalty-rewards'), reward);
        
        return {
          ...reward,
          id: rewardRef.id
        };
      });
    } catch (error) {
      logger.error('Error creating reward:', error);
      throw error;
    }
  },

  /**
   * Update a loyalty reward
   */
  async updateReward(id: string, reward: Partial<LoyaltyReward>): Promise<boolean> {
    try {
      const rewardRef = doc(db, 'loyalty-rewards', id);
      await updateDoc(rewardRef, reward);
      
      return true;
    } catch (error) {
      logger.error('Error updating reward:', error);
      throw error;
    }
  },

  /**
   * Get all loyalty transactions for a space
   */
  async getAllTransactions(spaceId: string): Promise<LoyaltyTransaction[]> {
    try {
      return await withRetry(async () => {
        const transactionsRef = query(
          collection(db, 'loyalty-transactions'),
          where('metadata.spaceId', '==', spaceId),
          orderBy('createdAt', 'desc'),
          limit(100) // Limit to most recent 100 transactions
        );
        
        const transactionsSnapshot = await getDocs(transactionsRef);
        
        return transactionsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as LoyaltyTransaction[];
      });
    } catch (error) {
      logger.error('Error getting all transactions:', error);
      return [];
    }
  },
};
