# Salon to Space Terminology Update

This document outlines the changes made to transition from "salon" terminology to "space" throughout the GroomBook application, making it more inclusive for various business types.

## Changes Completed

### 1. Data Models
- Added `Space` interface that extends the functionality of `Salon`
- Updated all relevant models to use `spaceId` with `salonId` maintained for backward compatibility:
  - Staff
  - Service
  - Appointment
  - ActiveService
  - Invoice
  - Review
  - RevenueTarget
- Enhanced the Space interface to include all properties needed for settings:
  - Currency settings
  - M-PESA integration settings
  - Business hours
  - Notification preferences

### 2. Redux Store
- Created `spaceSlice.ts` to parallel the existing `salonSlice.ts`
- Added selectors for working with the space state
- Updated store configuration to include both reducers
- Added backward compatibility in components by checking both states

### 3. Database Services
- Created `spaceService` in `firestore.ts` to work alongside `salonService`
- Added backward compatibility to query both collections
- Updated staff portal service to use `spaceId` with fallback to `salonId`
- Renamed methods in `publicAppointmentService` to use `space` terminology with backward compatibility:
  - `getSpaceServices` (replaces `getSalonServices`)
  - `getSpaceStaff` (replaces `getSalonStaff`)
  - `isSpaceOpen` (replaces `isSalonOpen`)

### 4. Components
- Created `SpaceSwitcher.tsx` to replace `SalonSwitcher.tsx`
- Created `CreateSpaceDialog.tsx` to replace `CreateSalonDialog.tsx`
- Updated dashboard services page to use space terminology
- Updated `BookingQRCode` component to use new terminology
- Updated `RevenueChart` component to accept both `spaceId` and `salonId` props
- Created route redirection for backward compatibility (/book/[salonId] → /book/[spaceId])
- Updated settings page to use `space` terminology instead of `salon` terminology
- Updated dashboard page to use `currentSpace` instead of `currentSalon`

### 5. Firebase Cloud Functions
- Updated `ServiceData` interface to use `spaceId` with `salonId` maintained for backward compatibility
- Updated `MessageRecord` interface to include both `spaceId` and `salonId` fields
- Modified `sendServiceCompletionSMS` to check both "spaces" and "salons" collections
- Updated error handling and messaging to reference spaces instead of salons
- Modified `sendBulkSMSCampaign` to use either `spaceId` or `salonId` (backward compatibility)
- Updated `sendInvoiceViaSMS` to handle both space and salon terminology

### 6. Compatibility Layer
- Created `compatibility.ts` utility with functions to help with the transition:
  - `getCurrentSpaceId`
  - `getCurrentSpace`
  - `ensureSpaceId`
  - `setCompatibleIds`
  - `salonToSpace`
  - `getSpaceIdField`

## Additional Updates

### 7. Notification Service
- Updated `Notification` interface to support both `spaceId` and `salonId` for backward compatibility
- Added new `getForSpace` method to query notifications by space ID
- Enhanced `markAllAsRead` method to check both space and salon references
- Updated `getByType` method to fetch notifications for a space instead of a salon
- Changed `createServiceCompletionNotification` method to use `spaceId` instead of `salonId`

### 8. Type Definitions
- Created `space.ts` type definition to replace `salon.ts`
- Enhanced the Space type to include additional fields:
  - More comprehensive currency settings
  - Extended business type options (salon, barbershop, wellness, fitness, space)
  - Additional notification preferences
  - Enhanced M-PESA integration options

### 9. Book Page Components
- Updated booking flow to use space terminology consistently
- Modified error messages and log statements to reference spaces
- Updated the business type selection fields in forms and dialogs:
  - Added new business types (wellness center, fitness studio) 
  - Changed default selection from "salon" to "space"

### 10. Dashboard Layout
- Updated localStorage references from `currentSalonId` to `currentSpaceId`
- Modified feedback submission to use space terminology
- Updated notifications handling to use the enhanced notification service

## Remaining Work

1. **UI Terminology**
   - Review all visible UI text to change "salon" to "space"
   - Update placeholder texts, labels, and descriptions

2. **Component Replacement**
   - Replace instances of `SalonSwitcher` with `SpaceSwitcher` throughout the app
   - Update layout components to reference spaces instead of salons

3. **Database Migration**
   - Consider a migration script to copy data from 'salons' collection to 'spaces' collection
   - Add dual-write functionality during transition period

4. **Documentation**
   - Update all documentation to reflect the new terminology
   - Provide migration guides for plugin developers

5. **Testing**
   - Test all Firebase Cloud Functions with both space and salon collections
   - Verify payment flows work correctly with the updated terminology

## Transition Strategy

The current implementation follows a hybrid approach that maintains backward compatibility while introducing the new terminology. This approach allows for:

1. **Gradual Migration**: Systems can continue functioning with both terms during transition
2. **Compatible Queries**: Database queries check both fields to ensure no data is missed
3. **API Consistency**: APIs remain compatible with both salon and space references

## Testing Recommendations

1. Test all customer-facing booking flows with both:
   - Spaces created with the new model
   - Legacy salons from the old model

2. Verify staff portal access works correctly with:
   - Staff assigned to the new spaces
   - Staff previously assigned to salons

3. Ensure all reports and dashboards correctly aggregate data from both terminology systems

4. Test PIN management and reset flows in both contexts

## Final Steps

1. **Data Migration**
   - Create a script to migrate existing salon data to the spaces collection
   - Add logic to maintain consistency during the transition period
   - Consider a flag in user profile to indicate migration status

2. **Feature Enhancements**
   - Leverage the new space model to support additional business types
   - Implement specialized templates for different business categories
   - Consider business-type specific features (fitness booking with equipment selection, wellness bookings with specialty options)

3. **Communication Plan**
   - Prepare announcement for users explaining the terminology change
   - Create in-app guidance to explain the more inclusive approach
   - Update marketing materials and website to reflect the new terminology
