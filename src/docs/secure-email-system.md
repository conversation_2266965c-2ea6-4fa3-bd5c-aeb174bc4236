# Secure Email System

## Overview

This document explains how we've implemented a secure email sending system using Resend. The key security improvement is that we no longer expose the Resend API key to the client-side.

## Implementation Details

### Server-side Email Utility (`/src/utils/email.ts`)

This module contains the server-side implementation for sending emails using Resend. It uses the `RESEND_API_KEY` environment variable (without the `NEXT_PUBLIC_` prefix), ensuring the API key is only available in server contexts.

```typescript
import { Resend } from 'resend';

const RESEND_API_KEY = process.env.RESEND_API_KEY || '';
export const resend = new Resend(RESEND_API_KEY);

// Other functions...
```

### API Route for Email Sending (`/src/app/api/send-email/route.ts`)

We've created a secure API route that handles email sending requests. This route:
- Validates incoming requests using Zod
- Calls the server-side email utility
- Returns appropriate responses

### Client-side Helper (`/src/utils/email-client.ts`)

This module provides a client-safe way to send emails by calling our secure API route:

```typescript
export async function sendEmailClient({
  to,
  subject,
  text,
  html,
  from,
}: {
  to: string | string[];
  subject: string;
  text?: string;
  html: string;
  from?: string;
}): Promise<boolean> {
  // Implementation that calls the API route
}
```

## How to Use

### In Server Components or API Routes

Import and use the `sendEmail` function directly:

```typescript
import { sendEmail } from '@/utils/email';

// In a Server Action or API Route
async function handleFormSubmission(formData: FormData) {
  await sendEmail({
    to: '<EMAIL>',
    subject: 'Your Subject',
    html: '<p>Email content</p>',
    text: 'Email content'
  });
}
```

### In Client Components

Import and use the `sendEmailClient` function:

```typescript
import { sendEmailClient } from '@/utils/email-client';

// In a client component
function ContactForm() {
  const handleSubmit = async (e) => {
    e.preventDefault();
    // Get form data...
    
    await sendEmailClient({
      to: recipientEmail,
      subject: formSubject,
      html: emailContent
    });
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
}
```

## Environment Variables

Make sure to set up the following environment variables:

- `RESEND_API_KEY`: Your Resend API key (server-side only)

Note: DO NOT use the `NEXT_PUBLIC_` prefix for this variable as it would expose the key to the client.
