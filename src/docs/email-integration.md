# GroomBook Email Integration

This document outlines how to set up and use the Resend email service integration with GroomBook.

## Setup Instructions

1. **Sign up for Resend**
   - Create an account at [Resend.com](https://resend.com)
   - Verify your domain for better deliverability

2. **Get API Key**
   - Generate an API key in the Resend dashboard
   - Add it to your environment variables:

   ```
   # .env.local
   NEXT_PUBLIC_RESEND_API_KEY=re_your_api_key_here
   ```

3. **Install Dependencies**
   Run the following command to install the Resend SDK:

   ```bash
   npm install resend
   # or
   yarn add resend
   ```

## Usage

The email utility is set up in `/src/utils/email.ts` and provides easy methods for sending emails:

```typescript
// Example: Send a custom email
import { sendEmail } from '@/utils/email';

await sendEmail({
  to: '<EMAIL>',
  subject: 'Your Appointment Confirmation',
  text: 'Your appointment has been confirmed',
  html: '<p>Your appointment has been confirmed</p>'
});
```

### Available Templates

1. **Referral Invitation**
   - Used when inviting new users via referral
   - Customizable message and referral code

## Testing Emails

To test email sending in development:

1. Use the Resend dashboard to view sent emails
2. Check the console logs for send status
3. Use a test email address for development

## Troubleshooting

- **Emails not sending**: Verify your API key is correct and environment variables are loaded
- **Spam issues**: Make sure your domain is properly verified in Resend
- **Template rendering issues**: Test the email HTML in a browser first

## Additional Resources

- [Resend Documentation](https://resend.com/docs)
- [Email Best Practices](https://resend.com/blog/email-best-practices)
