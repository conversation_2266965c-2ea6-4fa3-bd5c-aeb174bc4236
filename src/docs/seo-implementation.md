# SEO Implementation Guide

This document provides an overview of the SEO implementation for the GroomBook application.

## Overview

The GroomBook application has been optimized for search engines by:

1. Implementing proper metadata
2. Generating a sitemap
3. Creating a robots.txt file
4. Excluding private/dashboard routes from indexing

## Metadata Implementation

The application uses Next.js metadata API to implement comprehensive SEO metadata, including:

- Page titles and descriptions
- Open Graph and Twitter card metadata
- Proper indexing rules
- Search engine verifications
- Canonical URLs

All metadata is defined in `/src/app/metadata.ts` and can be customized per-page as needed.

## Sitemap Implementation

The sitemap is generated using the `next-sitemap` package, which creates:

1. A sitemap index file (`sitemap.xml`)
2. Individual sitemap files for different sections of the site (`sitemap-0.xml`, etc.)

The sitemap includes:
- Priority settings for different page types (home, services, etc.)
- Change frequency information
- Last modified timestamps

## Configuration

The sitemap configuration is in `next-sitemap.config.js` and includes:

- Site URL configuration (from environment variables)
- Exclusions for private routes (dashboard, login, etc.)
- Custom transformations for page priorities
- Robots.txt generation settings

## Robots.txt

The robots.txt file is automatically generated and includes:

- Allow/disallow rules for public/private sections
- Reference to the sitemap
- Host directive

## Maintenance

To update the sitemap when new pages are added:

1. Build the application with `npm run build`
2. The sitemap is automatically generated during the build process due to the postbuild script
3. Alternatively, run `npm run generate-sitemap` to generate the sitemap manually

## Environment Variables

The SEO implementation depends on the `SITE_URL` environment variable, which should be set to the production URL of the site.

## Testing

Use these tools to test the SEO implementation:
- [Google Search Console](https://search.google.com/search-console)
- [Google Structured Data Testing Tool](https://search.google.com/test/rich-results)
- [Bing Webmaster Tools](https://www.bing.com/webmasters)
