# Marketing Tracking in GroomBook

This document explains how marketing source tracking works in the GroomBook application, enabling businesses to understand how customers are finding their space.

## Overview

The marketing tracking system allows businesses to:

1. Generate a QR code that links directly to their booking page
2. Record the sources from which customers found their business
3. View statistics on the most effective marketing channels
4. Track changes in referral sources over time

## Key Components

### 1. Marketing Service

The `marketingService.ts` file contains the core functionality for tracking marketing sources:

- `recordSource`: Records a new marketing source for a space, incrementing the count if the source already exists
- `getSourcesBySpace`: Retrieves all marketing sources for a specific space
- `updateSummaryStats`: Updates the summary statistics for marketing sources
- `getSummary`: Retrieves the marketing summary for a space

### 2. Marketing Card Component

The `MarketingCard.tsx` component provides the user interface for:

- Displaying marketing source statistics
- Generating and downloading QR codes for booking
- Recording new customer referral sources

### 3. Firestore Integration

Marketing data is stored in two Firestore collections:

- `marketing-sources`: Stores individual records of marketing sources
- `marketing-summaries`: Stores aggregated statistics for each space

## How to Use

### Generating QR Codes

1. From the dashboard, find the Marketing Card
2. Click on "Booking QR Code"
3. The system generates a QR code that links directly to your space's booking page
4. Download the QR code using the download button
5. Print and display this QR code in your physical location or share it digitally

### Recording Referral Sources

1. Click on "Add Referral Source" in the Marketing Card
2. Select the source from which a customer found your business
3. If the source isn't listed, select "Other" and provide details
4. Click "Save Source" to record this information

### Viewing Marketing Statistics

The Marketing Card automatically displays:

- The top referral source with its percentage
- A breakdown of the top three referral sources
- The total number of recorded sources

## Best Practices

1. **Consistent Recording**: Record the referral source for every new customer
2. **Strategic QR Code Placement**: Place QR codes in visible locations in your space
3. **Source Specificity**: Be as specific as possible when selecting "Other" as a source
4. **Regular Analysis**: Review your marketing statistics regularly to adjust your strategy

## Future Enhancements

Future versions will include:

- Time-based analysis of marketing sources
- Export functionality for marketing data
- Integration with digital marketing campaigns
- Cost analysis for different marketing channels

## Technical Implementation Details

The marketing tracking system uses:

- Firestore for data storage and retrieval
- React hooks for state management
- Atomic increments for efficient counting
- Batched updates for data consistency

For any technical issues, please check the error logs in the console or contact the development team.
