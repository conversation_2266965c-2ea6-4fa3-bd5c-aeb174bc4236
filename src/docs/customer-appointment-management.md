# Customer Management in Public Appointment Bookings

## Overview

This document describes how customer records are managed when public appointments are booked through the client-facing booking flow in GroomBook. The implementation ensures reliable customer identification, record creation/updating, and proper loyalty points integration.

## Key Features

### 1. Customer Identification

The system intelligently identifies customers using two primary identifiers:

- **Phone Number (Primary)**: Phone numbers are converted to international format for consistent storage and matching, regardless of how they are entered by users.
- **Email (Secondary)**: Email addresses are normalized to lowercase and trimmed to ensure consistent matching.

### 2. Customer Record Management

#### Existing Customers
When an existing customer books an appointment:
- Their customer record is retrieved and reused
- Visit count is incremented
- Last visit timestamp is updated
- No duplicate records are created

#### New Customers
When a new customer books an appointment:
- A new customer record is created with proper initialization
- Loyalty program is automatically enrolled
- Welcome signup points are awarded
- Base loyalty tier (Bronze) is assigned

### 3. Phone Number Normalization

To ensure consistent identification regardless of input format:
- Local formats (e.g., `0712345678`) are converted to international format (`+254712345678`)
- Various formats are supported:
  - With or without leading zeros
  - With or without country code
  - With or without plus sign
  - With or without spaces/formatting characters

### 4. Loyalty Program Integration

New customer signups through the booking portal automatically:
- Register the customer in the loyalty program
- Award signup bonus points (currently 50 points)
- Initialize loyalty tier status
- Create a loyalty transaction record

### 5. Error Handling

The implementation includes robust error handling:
- Loyalty point failures don't block appointment creation
- Phone formatting failures fall back to original input
- Multiple identification methods ensure maximum customer matching

## Implementation Details

### Files
- `/src/store/slices/appointmentSlice.ts`: Main implementation of `createPublicAppointment`
- `/src/utils/phoneUtils.ts`: Phone formatting utilities
- `/src/services/loyaltyService.ts`: Loyalty point awarding system

### Process Flow

```
1. Start booking → Collect customer details
2. Format phone number to international format
3. Check for existing customer by phone
4. If not found, check for existing customer by email
5. If customer exists:
   a. Use existing customer ID
   b. Update visit count and last visit date
6. If customer doesn't exist:
   a. Create new customer record
   b. Initialize loyalty program enrollment
   c. Award signup bonus points
7. Create appointment using customer ID
8. Return appointment information
```

## Testing

The implementation includes comprehensive test cases covering:
- Customer lookup by phone with various input formats
- Customer lookup by email with case-insensitive matching
- New customer creation with proper initialization
- Loyalty points awarding for new signups
- Visit count incrementation for returning customers

## Benefits

- **No More Guest Customers**: All appointment bookings create or update proper customer records
- **Loyalty Program Growth**: Automatic enrollment increases loyalty program participation
- **Better Data**: Improved customer tracking and history across multiple bookings
- **Reliable Identification**: Formatting standardization prevents duplicate customer records
- **Engagement Opportunities**: Real customer records enable follow-up marketing and loyalty activities
