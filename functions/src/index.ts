import { onRequest } from "firebase-functions/v2/https";
import { onDocumentUpdated, FirestoreEvent, Change } from "firebase-functions/v2/firestore";
import { DocumentSnapshot } from "firebase-admin/firestore";
import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import axios from "axios";
import { defineString } from "firebase-functions/params";

// Create a logger alias for compatibility
const logger = functions.logger;

// Initialize Firebase if not already initialized
try {
  admin.initializeApp();
} catch (error) {
  // App already initialized
}

// Get environment variables
const getConfig = () => {
  return {
    infobip: {
      apiKey: process.env.INFOBIP_API_KEY || defineString("infobip.apikey"),
      apiUrl: process.env.INFOBIP_API_URL || defineString("infobip.apiUrl", { default: "https://2mq9n6.api.infobip.com" }),
      senderId: process.env.INFOBIP_SENDER_ID || defineString("infobip.senderid"),
    },
  };
};

interface ServiceData {
  customerName: string;
  customerPhone: string;
  services: {
    name: string;
    price: number;
  }[];
  totalAmount: number;
  spaceId: string;
  salonId?: string; // For backward compatibility
  status: "in_progress" | "completed" | "awaiting_payment" | "paid";
}


// Create a messages collection to track all sent messages
interface MessageRecord {
  to: string;
  messageId?: string;
  messageContent: string;
  status: "sent" | "delivered" | "failed" | "pending";
  timestamp: admin.firestore.Timestamp;
  serviceId?: string;
  spaceId?: string;
  salonId?: string; // For backward compatibility
  deliveryTimestamp?: admin.firestore.Timestamp;
  errorDetails?: string;
}

interface Space {
  id: string;
  name: string;
  type: "salon" | "barbershop" | "spa" | "fitness" | "other";
  description?: string;
  logo?: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  website?: string;
  instagram?: string;
  facebook?: string;
  createdAt: string;
  updatedAt: string;
  ownerId: string;
  // Business hours
  businessHours?: {
    day: string;
    open: string;
    close: string;
    enabled: boolean;
  }[];
  // M-PESA Integration
  mpesaBusinessName?: string;
  mpesaAccountType?: "till" | "paybill";
  mpesaShortcode?: string;
  mpesaAccountNumber?: string;
  mpesaPhoneNumber?: string;
  mpesaAutoConfirm?: boolean;
  mpesaEnabled?: boolean;
}


export const sendServiceCompletionSMS = onDocumentUpdated(
  "active-services/{serviceId}",
  async (
    event: FirestoreEvent<Change<DocumentSnapshot> | undefined, { serviceId: string }>
  ) => {
    logger.info("Service document updated, event received",
      { eventId: event.id, serviceId: event.params.serviceId }
    );

    if (!event.data) {
      logger.warn("No data associated with the event.",
        { eventId: event.id, serviceId: event.params.serviceId }
      );
      return null;
    }

    const newData = event.data.after.data() as ServiceData;
    const previousData = event.data.before.data() as ServiceData;

    // Ensure data exists, especially if ServiceData is a strict type
    if (!newData || !previousData) {
      logger.error("Missing data in snapshot.", {
        serviceId: event.params.serviceId,
        afterExists: event.data.after.exists,
        beforeExists: event.data.before.exists,
      });
      return null;
    }

    // Only send SMS when status changes to 'completed'
    if (previousData.status !== "completed" && newData.status === "completed") {
      try {
        logger.info("Processing service completion notification", {
          serviceId: event.params.serviceId,
          customer: newData.customerName,
          phone: newData.customerPhone,
        });

        // Get space data for payment URL (try both collections for backward compatibility)
        const spaceId = newData.spaceId;

        if (!spaceId) {
          const error = new Error("Space ID or Salon ID not found in service data");
          logger.error(error.message, { serviceId: event.params.serviceId });
          throw error;
        }

        let spaceSnapshot = await admin.firestore()
          .collection("spaces")
          .doc(spaceId)
          .get();

        // If not found in spaces collection, try the salons collection (backward compatibility)
        if (!spaceSnapshot.exists) {
          spaceSnapshot = await admin.firestore()
            .collection("salons")
            .doc(spaceId)
            .get();
        }

        if (!spaceSnapshot.exists) {
          // It's good practice to throw an error that can be caught and logged
          const spaceNotFoundError = new Error(`Space not found with ID: ${spaceId}`);
          logger.error(spaceNotFoundError.message,
            { serviceId: event.params.serviceId, spaceId: spaceId }
          );
          // Save failed message record if critical information is missing early
          await saveMessageRecord({
            to: newData.customerPhone || "unknown", // Handle cases where customerPhone might be missing
            messageContent: "Service completion notification - Space not found",
            status: "failed",
            timestamp: admin.firestore.Timestamp.now(),
            serviceId: event.params.serviceId,
            spaceId: spaceId,
            errorDetails: spaceNotFoundError.message,
          });
          throw spaceNotFoundError; // Or return if you don't want to retry the function
        }
        const spaceData = spaceSnapshot.data() as Space;


        // Format services list
        const servicesList = newData.services
          .map((s) => `${s.name}: KES ${s.price}`)
          .join("\n");

        // Generate payment URL
        const paymentUrl = `https://pay.groombook.me/${spaceId}/${event.params.serviceId}`;

        // Compose message
        const message = `Dear ${newData.customerName},\n\n` +
          `Your services at ${spaceData.name} are complete:\n\n` +
          `${servicesList}\n\n` +
          `Total Amount: KES ${newData.totalAmount}\n\n` +
          `Pay now: ${paymentUrl}\n\n` +
          `Thank you for choosing ${spaceData.name}!`;

        // Send SMS using Infobip (or your SMS provider)
        const messageResult = await sendSMS(newData.customerPhone, message);

        // Save message record to Firestore
        if (messageResult && messageResult.messageId) {
          await saveMessageRecord({
            to: newData.customerPhone,
            messageId: messageResult.messageId,
            messageContent: message,
            status: "sent",
            timestamp: admin.firestore.Timestamp.now(),
            serviceId: event.params.serviceId,
            spaceId: spaceId,
          });
          logger.info(`Completion SMS sent successfully to ${newData.customerPhone}`,
            { serviceId: event.params.serviceId, messageId: messageResult.messageId });
        } else {
          // This case handles if sendSMS returns null or a
          // non-successful-like response without throwing an error
          logger.error("SMS no success.", {
            serviceId: event.params.serviceId,
            customerPhone: newData.customerPhone,
          });
          await saveMessageRecord({
            to: newData.customerPhone,
            messageContent: message, // Save the intended message
            status: "failed",
            timestamp: admin.firestore.Timestamp.now(),
            serviceId: event.params.serviceId,
            spaceId: spaceId,
            errorDetails: "SMS provider did not return a messageId or indicated failure.",
          });
        }

        return null;
      } catch (error: unknown) { // Using unknown instead of any for better type safety
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        const errorStack = error instanceof Error ? error.stack : undefined;

        logger.error("Error sending service completion SMS:", {
          serviceId: event.params.serviceId,
          customerPhone: newData.customerPhone, // Log phone even in error context if available
          errorMessage,
          errorStack,
          errorObject: error, // Log the whole error object for more details
        });
        // Save failed message record
        // Ensure newData is available; if the error happened very early
        // (e.g., salon not found), some fields might be undefined
        await saveMessageRecord({
          to: newData?.customerPhone || "unknown",
          // Generic message if original couldn't be composed
          messageContent: "Service completion notification (failed to compose or send)",
          status: "failed",
          timestamp: admin.firestore.Timestamp.now(),
          serviceId: event.params.serviceId,
          spaceId: newData?.spaceId || newData?.salonId || "unknown",
          errorDetails: errorMessage,
        });
        // It's generally a good practice to rethrow or explicitly return an error/promise rejection
        // for Firebase to know the function failed, unless you've fully handled it.
        // Depending on your function's retry policy, this might trigger retries.
        throw error; // Or return Promise.reject(error);
      }
    } else {
      logger.info("Service status not changed to 'completed' or already 'completed'. No SMS sent.", {
        serviceId: event.params.serviceId,
        previousStatus: previousData?.status,
        newStatus: newData?.status,
      });
    }
    return null;
  }
);

// Helper function to save message records
async function saveMessageRecord(messageData: MessageRecord): Promise<void> {
  try {
    // Add message records here
    await admin.firestore()
      .collection("messages")
      .add(messageData);
  } catch (error) {
    logger.error("Error saving message record:", error);
  }
}

// Implement actual SMS sending with Infobip
async function sendSMS(
  phoneNumber: string,
  message: string): Promise<{ messageId?: string } | null> {
  // Ensure phone number starts with country code and has no special chars
  const formattedPhoneNumber = phoneNumber.startsWith("+") ?
    phoneNumber.substring(1) : phoneNumber;

  const infobipConfig = getConfig().infobip;

  try {
    const response = await axios({
      method: "post",
      url: `${infobipConfig.apiUrl}/sms/2/text/advanced`,
      headers: {
        "Authorization": `App ${infobipConfig.apiKey}`,
        "Content-Type": "application/json",
        "Accept": "application/json",
      },
      data: {
        messages: [
          {
            destinations: [
              {
                to: formattedPhoneNumber,
              },
            ],
            from: infobipConfig.senderId,
            text: message,
            notifyUrl: process.env.SMS_DELIVERY_WEBHOOK_URL || "",
            notifyContentType: "application/json",
            callbackData: "Callback data",
          },
        ],
      },
      timeout: 10000, // 10 second timeout
    });

    logger.info("SMS request successful:", response.data);

    // Extract message ID from Infobip response
    if (response.data &&
      response.data.messages &&
      response.data.messages.length > 0 &&
      response.data.messages[0].messageId) {
      return { messageId: response.data.messages[0].messageId };
    }

    return null;
  } catch (error) {
    logger.error("Error sending SMS via Infobip:", error);
    throw error;
  }
}


// Function to send bulk SMS campaigns
// export const sendBulkSMSCampaign = onRequest({ maxInstances: 10, cors: true }, async (req, res) => {
//   // Check if user is authenticated with sufficient permissions

//   // In v2 HTTP functions, auth is handled differently
//   // We can use req.auth if we set up authentication

//   try {
//     const data = req.body;
//     const { recipients, message, spaceId, salonId, campaignName } = data;
//     const businessId = spaceId || salonId; // Use either spaceId or salonId for backward compatibility

//     if (!Array.isArray(recipients) || recipients.length === 0) {
//       throw new functions.https.HttpsError(
//         "invalid-argument",
//         "Recipients must be a non-empty array of phone numbers"
//       );
//     }

//     if (!message || typeof message !== "string") {
//       throw new functions.https.HttpsError(
//         "invalid-argument",
//         "Message must be a non-empty string"
//       );
//     }

//     // Create campaign record
//     const campaignRef = await admin.firestore()
//       .collection("campaigns")
//       .add({
//         name: campaignName || "Bulk SMS Campaign",
//         spaceId: businessId,
//         message: message,
//         totalRecipients: recipients.length,
//         sentCount: 0,
//         deliveredCount: 0,
//         failedCount: 0,
//         status: "in_progress",
//         createdAt: admin.firestore.Timestamp.now(),
//         createdBy: "system", // In v2 HTTP functions, auth is handled differently
//       });

//     // Process recipients in batches of 100
//     const batchSize = 100;
//     const batches = [];

//     for (let i = 0; i < recipients.length; i += batchSize) {
//       const batch = recipients.slice(i, i + batchSize);
//       batches.push(batch);
//     }

//     let sentCount = 0;
//     let failedCount = 0;

//     // Process each batch
//     for (const batch of batches) {
//       const infobipConfig = getConfig().infobip;

//       try {
//         const response = await axios({
//           method: "post",
//           url: `${infobipConfig.apiUrl}/sms/2/text/multi`,
//           headers: {
//             "Authorization": `App ${infobipConfig.apiKey}`,
//             "Content-Type": "application/json",
//           },
//           data: {
//             messages: batch.map((phoneNumber) => {
//               // Ensure phone number starts with country code and has no special chars
//               const formattedPhoneNumber = phoneNumber.startsWith("+") ?
//                 phoneNumber.substring(1) : phoneNumber;

//               return {
//                 from: infobipConfig.senderId,
//                 destinations: [{ to: formattedPhoneNumber }],
//                 text: message,
//                 notifyUrl: process.env.SMS_DELIVERY_WEBHOOK_URL || "",
//                 notifyContentType: "application/json",
//                 callbackData: `campaign:${campaignRef.id}`,
//               };
//             }),
//           },
//           timeout: 30000, // 30 second timeout
//         });

//         // Process response
//         const messages = response.data.messages || [];

//         // Track message records in Firestore
//         const bulkWriteBatch = admin.firestore().batch();

//         interface InfobipMessageResponse {
//           to: string;
//           messageId: string;
//           status?: {
//             groupName: string;
//           };
//         }

//         messages.forEach((msg: InfobipMessageResponse) => {
//           const messageRef = admin.firestore().collection("messages").doc();

//           bulkWriteBatch.set(messageRef, {
//             to: msg.to,
//             messageId: msg.messageId,
//             messageContent: message,
//             status: msg.status?.groupName === "PENDING" ? "sent" : "failed",
//             timestamp: admin.firestore.Timestamp.now(),
//             salonId: salonId,
//             campaignId: campaignRef.id,
//           } as MessageRecord);

//           if (msg.status?.groupName === "PENDING") {
//             sentCount++;
//           } else {
//             failedCount++;
//           }
//         });

//         await bulkWriteBatch.commit();

//       } catch (error) {
//         logger.error("Error sending batch in bulk SMS campaign:", error);
//         failedCount += batch.length;
//         // Log the error but continue processing other batches
//         logger.error("Failed to send batch, continuing with other batches");
//       }
//     }

//     // Update campaign with results
//     await campaignRef.update({
//       sentCount: sentCount,
//       failedCount: failedCount,
//       status: "completed",
//       completedAt: admin.firestore.Timestamp.now(),
//     });

//     res.status(200).json({
//       success: true,
//       campaignId: campaignRef.id,
//       sent: sentCount,
//       failed: failedCount,
//     });
//     return;
//   } catch (error) {
//     logger.error("Error in bulk SMS campaign:", error);

//     res.status(500).json({
//       success: false,
//       error: "Failed to send bulk SMS campaign",
//     });
//     return;
//   }
// });

// Optional: Function to send invoices via SMS
export const sendInvoiceViaSMS = onRequest({ maxInstances: 10 }, async (request, response) => {
  // In v2 HTTP functions, auth is handled differently
  // We can use request.auth if we set up authentication

  try {
    const data = request.body;
    const { phoneNumber, customerName, services, totalAmount, invoiceId, spaceName, spaceId, salonName, salonId } = data;

    // For backward compatibility, use either space or salon data
    const businessName = spaceName || salonName;
    const businessId = spaceId || salonId;

    // Format services list
    const servicesList = services
      .map((s: { name: string, price: number }) => `${s.name}: KES ${s.price}`)
      .join("\n");

    // Generate invoice URL (you could have a separate invoice view page)
    const invoiceUrl = `https://invoice.groombook.me/${invoiceId}`;

    // Compose invoice message
    const message = `Dear ${customerName},\n\n` +
      `Here is your invoice from ${businessName}:\n\n` +
      `${servicesList}\n\n` +
      `Total Amount: KES ${totalAmount}\n\n` +
      `View invoice: ${invoiceUrl}\n\n` +
      "Thank you for your business!";

    // Send SMS
    const messageResult = await sendSMS(phoneNumber, message);

    // Save message record
    if (messageResult) {
      await saveMessageRecord({
        to: phoneNumber,
        messageId: messageResult.messageId,
        messageContent: message,
        status: "sent",
        timestamp: admin.firestore.Timestamp.now(),
        spaceId: businessId,
        salonId: salonId, // Keep for backward compatibility
      });
    }

    response.status(200).json({
      success: true,
      messageId: messageResult?.messageId,
    });
    return;
  } catch (error) {
    logger.error("Error sending invoice via SMS:", error);

    response.status(500).json({
      success: false,
      error: "Failed to send invoice via SMS",
    });
    return;
  }
});

// Paddle webhook handler using Firebase Functions
export const paddleWebhook = onRequest({
  maxInstances: 10,
  cors: true,
}, async (request, response) => {
  try {
    const body = JSON.stringify(request.body);
    const signature = request.headers["paddle-signature"] as string;

    // Verify webhook signature
    if (!verifyPaddleWebhookSignature(body, signature)) {
      logger.error("Invalid Paddle webhook signature");
      response.status(401).json({ error: "Invalid signature" });
      return;
    }

    const event = request.body;
    const { event_type: eventType, data } = event;

    logger.info(`📨 Paddle webhook received: ${eventType}`, {
      timestamp: new Date().toISOString(),
      eventId: event.event_id || "unknown",
    });

    // Handle different webhook events
    switch (eventType) {
    case "subscription.created":
      await handlePaddleSubscriptionCreated(data);
      break;

    case "subscription.updated":
      await handlePaddleSubscriptionUpdated(data);
      break;

    case "subscription.canceled":
      await handlePaddleSubscriptionCanceled(data);
      break;

    case "subscription.paused":
      await handlePaddleSubscriptionPaused(data);
      break;

    case "subscription.resumed":
      await handlePaddleSubscriptionResumed(data);
      break;

    case "transaction.completed":
      await handlePaddleTransactionCompleted(data);
      break;

    case "transaction.payment_failed":
      await handlePaddlePaymentFailed(data);
      break;

    default:
      logger.info(`🤷‍♂️ Unhandled Paddle webhook event: ${eventType}`);
    }

    response.status(200).json({ received: true });
    return;

  } catch (error) {
    logger.error("❌ Paddle webhook processing error:", error);
    response.status(500).json({ error: "Webhook processing failed" });
    return;
  }
});

// Paddle webhook signature verification
function verifyPaddleWebhookSignature(body: string, signature: string | null): boolean {
  const webhookSecret = functions.config().paddle?.webhook_key;

  if (!webhookSecret || !signature) {
    return false;
  }

  try {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const crypto = require("crypto");
    const expectedSignature = crypto
      .createHmac("sha256", webhookSecret)
      .update(body)
      .digest("hex");

    return crypto.timingSafeEqual(
      Buffer.from(signature, "hex"),
      Buffer.from(expectedSignature, "hex")
    );
  } catch (error) {
    logger.error("Error verifying Paddle webhook signature:", error);
    return false;
  }
}

// Paddle webhook event handlers
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function handlePaddleSubscriptionCreated(data: any) {
  logger.info("🎉 New Paddle subscription created:", {
    subscriptionId: data.id,
    customerId: data.customer_id,
    status: data.status,
    priceId: data.items?.[0]?.price?.id,
  });

  try {
    // Extract customer info from custom data
    const customData = data.custom_data || {};
    const userId = customData.userId;

    if (!userId) {
      logger.error("No userId found in subscription custom_data");
      return;
    }

    const priceId = data.items?.[0]?.price?.id;
    const tier = getPaddleTierFromPriceId(priceId);

    // Update user subscription in database
    await updatePaddleSubscriptionInDatabase({
      userId,
      subscriptionId: data.id,
      customerId: data.customer_id,
      status: data.status,
      priceId: priceId,
      tier: tier,
      interval: data.billing_cycle?.interval,
      currentPeriodStart: new Date(data.current_billing_period?.starts_at),
      currentPeriodEnd: new Date(data.current_billing_period?.ends_at),
      createdAt: new Date(data.created_at),
    });

    logger.info("✅ Paddle subscription created successfully in database");

  } catch (error) {
    logger.error("❌ Error handling Paddle subscription creation:", error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function handlePaddleSubscriptionUpdated(data: any) {
  logger.info("🔄 Paddle subscription updated:", {
    subscriptionId: data.id,
    status: data.status,
    scheduledChange: data.scheduled_change,
  });

  try {
    await updatePaddleSubscriptionInDatabase({
      subscriptionId: data.id,
      status: data.status,
      currentPeriodStart: new Date(data.current_billing_period?.starts_at),
      currentPeriodEnd: new Date(data.current_billing_period?.ends_at),
      scheduledChange: data.scheduled_change,
      updatedAt: new Date(data.updated_at),
    });

    logger.info("✅ Paddle subscription updated successfully in database");

  } catch (error) {
    logger.error("❌ Error handling Paddle subscription update:", error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function handlePaddleSubscriptionCanceled(data: any) {
  logger.info("❌ Paddle subscription canceled:", {
    subscriptionId: data.id,
    canceledAt: data.canceled_at,
    reason: data.cancellation_reason,
  });

  try {
    await updatePaddleSubscriptionInDatabase({
      subscriptionId: data.id,
      status: "canceled",
      canceledAt: new Date(data.canceled_at),
      cancellationReason: data.cancellation_reason?.code,
    });

    logger.info("✅ Paddle subscription cancellation handled successfully");

  } catch (error) {
    logger.error("❌ Error handling Paddle subscription cancellation:", error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function handlePaddleSubscriptionPaused(data: any) {
  logger.info("⏸️ Paddle subscription paused:", {
    subscriptionId: data.id,
    pausedAt: data.paused_at,
  });

  try {
    await updatePaddleSubscriptionInDatabase({
      subscriptionId: data.id,
      status: "paused",
      pausedAt: new Date(data.paused_at),
    });

    logger.info("✅ Paddle subscription pause handled successfully");

  } catch (error) {
    logger.error("❌ Error handling Paddle subscription pause:", error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function handlePaddleSubscriptionResumed(data: any) {
  logger.info("▶️ Paddle subscription resumed:", {
    subscriptionId: data.id,
  });

  try {
    await updatePaddleSubscriptionInDatabase({
      subscriptionId: data.id,
      status: "active",
      pausedAt: null,
    });

    logger.info("✅ Paddle subscription resumed successfully");

  } catch (error) {
    logger.error("❌ Error handling Paddle subscription resume:", error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function handlePaddleTransactionCompleted(data: any) {
  logger.info("💰 Paddle transaction completed:", {
    transactionId: data.id,
    subscriptionId: data.subscription_id,
    amount: data.details?.totals?.grand_total?.amount,
    currency: data.details?.totals?.grand_total?.currency_code,
  });

  try {
    // Log the successful payment
    await logPaddleTransactionInDatabase({
      transactionId: data.id,
      subscriptionId: data.subscription_id,
      customerId: data.customer_id,
      amount: data.details?.totals?.grand_total?.amount,
      currency: data.details?.totals?.grand_total?.currency_code,
      status: "completed",
      paidAt: new Date(data.updated_at),
    });

    // Reset any failed payment flags
    if (data.subscription_id) {
      await updatePaddleSubscriptionInDatabase({
        subscriptionId: data.subscription_id,
        paymentFailed: false,
        lastPaymentAt: new Date(data.updated_at),
      });
    }

    logger.info("✅ Paddle transaction logged successfully");

  } catch (error) {
    logger.error("❌ Error handling Paddle completed transaction:", error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function handlePaddlePaymentFailed(data: any) {
  logger.info("💳 Paddle payment failed:", {
    transactionId: data.id,
    subscriptionId: data.subscription_id,
    reason: data.details?.payment_method?.card?.cardholder_authentication_reason,
  });

  try {
    // Mark subscription as having payment issues
    if (data.subscription_id) {
      await updatePaddleSubscriptionInDatabase({
        subscriptionId: data.subscription_id,
        paymentFailed: true,
        paymentFailedAt: new Date(data.updated_at),
      });
    }

    // Log the failed transaction
    await logPaddleTransactionInDatabase({
      transactionId: data.id,
      subscriptionId: data.subscription_id,
      customerId: data.customer_id,
      status: "failed",
      failureReason: data.details?.payment_method?.card?.cardholder_authentication_reason,
      failedAt: new Date(data.updated_at),
    });

    logger.info("✅ Paddle payment failure handled successfully");

  } catch (error) {
    logger.error("❌ Error handling Paddle payment failure:", error);
    throw error;
  }
}

// Helper functions for Paddle subscription management
function getPaddleTierFromPriceId(priceId: string): string {
  const PRICE_ID_TO_TIER: Record<string, string> = {
    "pri_01jw0x8s4xxz8v38ptt3ffqwtp": "basic", // Basic monthly - $9
    "pri_01jw0xgr4mg7r0fj5fqwv1mddj": "basic", // Basic yearly - $90
    "pri_01jw0xnpq49mv9qfr6nzv2qkjn": "pro", // Pro monthly
    "pri_01jw0xqmnys4sqhfazgsc7k0f4": "pro", // Pro yearly
    "pri_01jw0xxvjfgxdb1kn001qay4ha": "enterprise", // Enterprise monthly
    "pri_01jw0y01tdcfskjxa642xk41k7": "enterprise", // Enterprise yearly
  };

  return PRICE_ID_TO_TIER[priceId] || "free";
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function updatePaddleSubscriptionInDatabase(subscriptionData: any): Promise<void> {
  try {
    const db = admin.firestore();

    if (subscriptionData.userId && subscriptionData.subscriptionId) {
      // Update user document with subscription info
      await db.collection("users").doc(subscriptionData.userId).update({
        subscriptionId: subscriptionData.subscriptionId,
        subscriptionTier: subscriptionData.tier,
        subscriptionStatus: subscriptionData.status,
        updatedAt: admin.firestore.Timestamp.now(),
      });

      // Create or update subscription document
      await db.collection("subscriptions").doc(subscriptionData.subscriptionId).set({
        ...subscriptionData,
        currentPeriodStart: subscriptionData.currentPeriodStart ?
          admin.firestore.Timestamp.fromDate(subscriptionData.currentPeriodStart) : null,
        currentPeriodEnd: subscriptionData.currentPeriodEnd ?
          admin.firestore.Timestamp.fromDate(subscriptionData.currentPeriodEnd) : null,
        createdAt: subscriptionData.createdAt ?
          admin.firestore.Timestamp.fromDate(subscriptionData.createdAt) :
          admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      }, { merge: true });
    } else if (subscriptionData.subscriptionId) {
      // Update existing subscription document
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateData: any = { ...subscriptionData };

      // Convert dates to Firestore timestamps
      if (updateData.currentPeriodStart) {
        updateData.currentPeriodStart = admin.firestore.Timestamp.fromDate(updateData.currentPeriodStart);
      }
      if (updateData.currentPeriodEnd) {
        updateData.currentPeriodEnd = admin.firestore.Timestamp.fromDate(updateData.currentPeriodEnd);
      }
      if (updateData.canceledAt) {
        updateData.canceledAt = admin.firestore.Timestamp.fromDate(updateData.canceledAt);
      }
      if (updateData.pausedAt) {
        updateData.pausedAt = admin.firestore.Timestamp.fromDate(updateData.pausedAt);
      }
      if (updateData.lastPaymentAt) {
        updateData.lastPaymentAt = admin.firestore.Timestamp.fromDate(updateData.lastPaymentAt);
      }
      if (updateData.paymentFailedAt) {
        updateData.paymentFailedAt = admin.firestore.Timestamp.fromDate(updateData.paymentFailedAt);
      }

      updateData.updatedAt = admin.firestore.Timestamp.now();

      await db.collection("subscriptions").doc(subscriptionData.subscriptionId).update(updateData);
    }

  } catch (error) {
    logger.error("Error updating Paddle subscription in database:", error);
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function logPaddleTransactionInDatabase(transactionData: any): Promise<void> {
  try {
    const db = admin.firestore();

    const logData = {
      ...transactionData,
      paidAt: transactionData.paidAt ? admin.firestore.Timestamp.fromDate(transactionData.paidAt) : null,
      failedAt: transactionData.failedAt ? admin.firestore.Timestamp.fromDate(transactionData.failedAt) : null,
      createdAt: admin.firestore.Timestamp.now(),
    };

    await db.collection("transactions").doc(transactionData.transactionId).set(logData);

  } catch (error) {
    logger.error("Error logging Paddle transaction in database:", error);
    throw error;
  }
}
